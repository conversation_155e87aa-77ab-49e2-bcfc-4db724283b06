#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
并行处理功能测试脚本
简化版本，用于快速验证并行功能
"""

import logging
import time
import sys
import os

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def mock_llm_analysis(**kwargs):
    """模拟LLM分析函数"""
    fund_code = kwargs.get('fund_code', 'UNKNOWN')
    
    # 模拟不同的处理时间
    if fund_code.endswith('1'):
        time.sleep(1)  # 1秒
    elif fund_code.endswith('2'):
        time.sleep(2)  # 2秒
    else:
        time.sleep(0.5)  # 0.5秒
    
    return f"基金 {fund_code} 分析完成"

def test_parallel_vs_serial():
    """测试并行vs串行性能"""
    logger.info("=== 并行处理功能测试 ===")
    
    try:
        from parallel_processor import create_analysis_task, simple_parallel_analysis
        
        # 创建测试任务
        test_tasks = []
        for i in range(1, 6):  # 5个测试基金
            task = create_analysis_task(
                f"TEST{i:03d}.OF",
                mock_llm_analysis,
                fund_code=f"TEST{i:03d}.OF"
            )
            test_tasks.append(task)
        
        # 测试串行处理
        logger.info("--- 串行处理测试 ---")
        start_time = time.time()
        serial_results = simple_parallel_analysis(test_tasks, max_workers=2, enable_parallel=False)
        serial_time = time.time() - start_time
        
        logger.info(f"串行处理完成，总耗时: {serial_time:.2f}秒")
        
        # 测试并行处理
        logger.info("--- 并行处理测试 ---")
        start_time = time.time()
        parallel_results = simple_parallel_analysis(test_tasks, max_workers=2, enable_parallel=True)
        parallel_time = time.time() - start_time
        
        logger.info(f"并行处理完成，总耗时: {parallel_time:.2f}秒")
        
        # 性能对比
        if parallel_time > 0:
            improvement = (serial_time - parallel_time) / serial_time * 100
            logger.info(f"性能提升: {improvement:.1f}%")
        
        # 验证结果一致性
        serial_success = sum(1 for r in serial_results if r['success'])
        parallel_success = sum(1 for r in parallel_results if r['success'])
        
        if serial_success == parallel_success:
            logger.info("✅ 结果一致性验证通过")
            return True
        else:
            logger.warning("⚠️ 结果一致性验证失败")
            return False
            
    except ImportError as e:
        logger.error(f"导入模块失败: {e}")
        logger.error("请确保在项目根目录下运行此脚本")
        return False
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}", exc_info=True)
        return False

def test_config_management():
    """测试配置管理功能"""
    logger.info("=== 配置管理功能测试 ===")
    
    try:
        from config_manager import get_parallel_config
        
        config = get_parallel_config()
        
        # 显示当前配置
        current_settings = config.get_current_settings()
        logger.info(f"当前配置: {current_settings}")
        
        # 测试配置修改
        original_enabled = config.is_enabled()
        original_workers = config.get_max_workers()
        
        # 临时修改配置
        config.enable_parallel(max_workers=2, save=False)
        logger.info(f"临时启用并行: {config.get_current_settings()}")
        
        config.disable_parallel(save=False)
        logger.info(f"临时禁用并行: {config.get_current_settings()}")
        
        # 恢复原始配置
        if original_enabled:
            config.enable_parallel(max_workers=original_workers, save=False)
        else:
            config.disable_parallel(save=False)
        
        logger.info("✅ 配置管理功能正常")
        return True
        
    except ImportError as e:
        logger.error(f"导入配置管理模块失败: {e}")
        return False
    except Exception as e:
        logger.error(f"配置管理测试失败: {e}", exc_info=True)
        return False

def main():
    """主测试函数"""
    logger.info("开始并行处理功能测试...")
    
    success_count = 0
    total_tests = 2
    
    # 测试并行处理
    if test_parallel_vs_serial():
        success_count += 1
    
    # 测试配置管理
    if test_config_management():
        success_count += 1
    
    # 总结
    logger.info(f"\n=== 测试总结 ===")
    logger.info(f"通过测试: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        logger.info("🎉 所有测试通过！并行处理功能正常工作")
        return True
    else:
        logger.warning("⚠️ 部分测试失败，请检查配置和依赖")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
