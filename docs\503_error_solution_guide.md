# 503错误解决方案指南

## 问题分析

根据日志分析，503 Service Unavailable错误主要由以下原因引起：

1. **并发请求过多**：同时发送多个请求导致API服务器过载
2. **请求频率过高**：缺乏请求间隔控制
3. **缺乏重试机制**：没有针对503错误的特殊处理
4. **超时配置不当**：默认超时设置可能不适合长时间处理

## 解决方案实施

### 1. 立即解决方案（已实施）

#### 1.1 降低并发数量
- 将`max_workers`从2降低到1
- 添加3秒请求间隔控制
- 更新配置文件：`config/parallel_config.json`

#### 1.2 增强重试机制
- 集成Tenacity库进行专业重试管理
- 添加指数退避策略：4-10秒延迟
- 针对503、连接错误、超时错误进行重试

#### 1.3 超时配置优化
- 连接超时：60秒
- 读取超时：300秒（5分钟）
- 总超时：360秒（6分钟）

### 2. 配置文件说明

#### 2.1 并行处理配置 (`config/parallel_config.json`)
```json
{
  "parallel_processing": {
    "enabled": true,
    "max_workers": 1,
    "request_interval": 3.0
  }
}
```

#### 2.2 API弹性配置 (`config/api_resilience_config.json`)
- 重试策略配置
- 超时设置
- 503错误特殊处理
- 速率限制配置

### 3. 代码修改说明

#### 3.1 LLM分析器增强 (`src/llm_analyzer.py`)
- 添加Tenacity重试装饰器
- 配置OpenAI客户端超时参数
- 增加内置重试机制

#### 3.2 并行处理器优化 (`src/parallel_processor.py`)
- 添加请求间隔控制
- 实现线程安全的速率限制
- 增强错误处理和日志记录

## 使用建议

### 1. 保守模式（推荐）
```bash
# 设置环境变量
MAX_WORKERS=1
REQUEST_INTERVAL=5.0
ENABLE_PARALLEL=true
```

### 2. 平衡模式
```bash
MAX_WORKERS=1
REQUEST_INTERVAL=3.0
ENABLE_PARALLEL=true
```

### 3. 监控和调试
- 查看日志中的重试信息
- 监控请求间隔是否生效
- 观察503错误频率变化

## 预期效果

1. **503错误显著减少**：通过降低并发和增加间隔
2. **自动恢复能力**：重试机制处理临时错误
3. **更稳定的处理**：避免API服务器过载
4. **更好的监控**：详细的错误日志和统计

## 进一步优化建议

### 1. 短期优化
- 根据实际测试结果调整请求间隔
- 监控API响应时间变化
- 优化重试策略参数

### 2. 长期优化
- 考虑迁移到异步架构（asyncio）
- 实现更智能的负载均衡
- 添加API健康检查机制

## 故障排除

### 1. 如果503错误仍然频繁
- 增加请求间隔到5-10秒
- 临时禁用并行处理
- 检查API服务商状态

### 2. 如果处理速度过慢
- 逐步增加并发数（谨慎）
- 减少请求间隔（小心监控）
- 考虑批量处理优化

### 3. 监控指标
- 503错误频率
- 平均请求响应时间
- 重试成功率
- 整体处理时间

## 配置调整命令

```bash
# 安装新依赖
pip install tenacity

# 测试配置
python -c "from src.parallel_processor import SimpleLLMParallelProcessor; print('配置加载成功')"

# 运行测试
python tests/test_parallel.py
```
