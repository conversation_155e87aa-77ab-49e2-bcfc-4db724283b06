# LLM API交互性能优化方案

## 🔍 性能瓶颈分析

### 当前问题
1. **Prompt过于冗长**: 单个prompt包含3000-5000字符，约1200-2000 tokens
2. **信息冗余**: 分析规则文档281行，包含大量重复信息
3. **API响应时间**: gemini-2.5-pro-preview-03-25模型处理复杂prompt耗时较长
4. **Token成本**: 大量输入token增加API调用成本

### 瓶颈定位
- **主要瓶颈**: LLM API交互（占总处理时间80%+）
- **次要瓶颈**: Prompt构建和数据预处理
- **API端点**: https://aa.aazs.me/v1/chat/completions

## 🚀 优化策略

### 1. Prompt精简优化

#### 1.1 分析规则模板化
**当前问题**: 深度分析专精.md文档281行，包含大量示例和说明

**优化方案**:
```python
# 创建精简版分析规则模板
def create_compact_analysis_rules():
    return """
【核心分析框架】
1. 核心投资逻辑摘要
2. 聚焦领域驱动因素分析  
3. 投资组合与变动分析
4. 业绩归因与关键驱动
5. 关键洞察与风险提示
6. 其他重要信息
7. 表格数据(JSON格式)

【输出要求】
- 客观基于季报文本
- 突出核心聚焦领域
- 使用🔴🟡🟢标识重点
- JSON格式表格数据
"""
```

#### 1.2 动态信息组装
**当前问题**: 所有信息一次性加载到prompt中

**优化方案**:
```python
def build_optimized_prompt(fund_data, analysis_type="standard"):
    """根据分析类型动态构建prompt"""
    
    # 基础信息（必需）
    base_prompt = f"""
    基金: {fund_data['name']} ({fund_data['code']})
    经理: {fund_data['manager']}
    报告期: {fund_data['report_date']}
    """
    
    # 根据分析类型添加特定信息
    if analysis_type == "quick":
        # 快速分析：只包含核心持仓和基本规则
        return base_prompt + get_core_holdings(fund_data) + get_compact_rules()
    elif analysis_type == "detailed":
        # 详细分析：包含完整信息
        return base_prompt + get_full_data(fund_data) + get_full_rules()
    else:
        # 标准分析：平衡信息量和性能
        return base_prompt + get_essential_data(fund_data) + get_standard_rules()
```

### 2. 分层分析策略

#### 2.1 两阶段分析
**第一阶段**: 快速概览分析
- 精简prompt（500-800字符）
- 识别核心聚焦领域
- 生成初步洞察

**第二阶段**: 深度分析（可选）
- 基于第一阶段结果
- 针对性深入分析
- 完整报告生成

```python
async def two_stage_analysis(fund_data):
    # 第一阶段：快速分析
    quick_prompt = build_optimized_prompt(fund_data, "quick")
    quick_result = await llm_api_call(quick_prompt, max_tokens=1000)
    
    # 判断是否需要第二阶段
    if requires_detailed_analysis(quick_result):
        # 第二阶段：详细分析
        detailed_prompt = build_detailed_prompt(fund_data, quick_result)
        detailed_result = await llm_api_call(detailed_prompt, max_tokens=3000)
        return merge_results(quick_result, detailed_result)
    
    return enhance_quick_result(quick_result)
```

### 3. API调用优化

#### 3.1 参数调优
```python
def optimized_api_call(prompt, analysis_type="standard"):
    """优化的API调用参数"""
    
    params = {
        "model": "gemini-2.5-pro-exp-03-25",
        "messages": [{"role": "user", "content": prompt}],
        "temperature": 0.3,  # 降低随机性，提高一致性
    }
    
    # 根据分析类型调整参数
    if analysis_type == "quick":
        params["max_tokens"] = 1500
        params["temperature"] = 0.1
    elif analysis_type == "detailed":
        params["max_tokens"] = 4000
        params["temperature"] = 0.5
    else:
        params["max_tokens"] = 2500
        params["temperature"] = 0.3
    
    return params
```

#### 3.2 并发处理
```python
import asyncio
import aiohttp

async def batch_analysis(fund_list, max_concurrent=3):
    """批量并发分析，控制并发数避免API限制"""
    
    semaphore = asyncio.Semaphore(max_concurrent)
    
    async def analyze_single_fund(fund_data):
        async with semaphore:
            return await analyze_with_llm_async(fund_data)
    
    tasks = [analyze_single_fund(fund) for fund in fund_list]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    return results
```

### 4. 缓存和预处理优化

#### 4.1 智能缓存
```python
import hashlib
import json
from functools import lru_cache

class AnalysisCache:
    def __init__(self):
        self.cache = {}
    
    def get_cache_key(self, fund_data, analysis_rules):
        """生成缓存键"""
        content = {
            'fund_code': fund_data.get('code'),
            'report_date': fund_data.get('report_date'),
            'holdings_hash': self._hash_holdings(fund_data.get('holdings', [])),
            'rules_version': analysis_rules.get('version', 'v3.0')
        }
        return hashlib.md5(json.dumps(content, sort_keys=True).encode()).hexdigest()
    
    def get_cached_result(self, cache_key):
        """获取缓存结果"""
        return self.cache.get(cache_key)
    
    def cache_result(self, cache_key, result):
        """缓存分析结果"""
        self.cache[cache_key] = {
            'result': result,
            'timestamp': time.time()
        }
```

#### 4.2 数据预处理优化
```python
def preprocess_fund_data(raw_data):
    """预处理基金数据，减少prompt中的冗余信息"""
    
    # 持仓数据精简
    holdings = []
    for holding in raw_data.get('holdings', [])[:10]:  # 只取前10大
        holdings.append({
            'code': holding['code'],
            'name': holding['name'][:10],  # 名称截断
            'industry_l1': holding.get('industry_l1', ''),
            'weight': f"{holding['weight']:.2f}%",
            'pe_ttm': f"{holding.get('pe_ttm', 0):.1f}" if holding.get('pe_ttm') else 'N/A'
        })
    
    # 季报文本精简
    market_outlook = truncate_text(raw_data.get('market_outlook', ''), 500)
    operation_analysis = truncate_text(raw_data.get('operation_analysis', ''), 800)
    
    return {
        'basic_info': extract_basic_info(raw_data),
        'holdings': holdings,
        'market_outlook': market_outlook,
        'operation_analysis': operation_analysis,
        'valuation_summary': summarize_valuation(raw_data)
    }
```

### 5. 模型和API选择优化

#### 5.1 模型对比测试
```python
MODEL_CONFIGS = {
    'gemini-2.5-pro-exp-03-25': {
        'cost_per_1k_tokens': 0.01,
        'avg_response_time': 8.5,
        'quality_score': 9.2
    },
    'gpt-4-turbo': {
        'cost_per_1k_tokens': 0.03,
        'avg_response_time': 6.2,
        'quality_score': 9.5
    },
    'claude-3-sonnet': {
        'cost_per_1k_tokens': 0.015,
        'avg_response_time': 7.1,
        'quality_score': 9.3
    }
}

def select_optimal_model(analysis_type, budget_constraint=None):
    """根据分析类型和预算选择最优模型"""
    if analysis_type == "quick":
        return "gemini-2.5-pro-exp-03-25"  # 成本效益最优
    elif budget_constraint and budget_constraint < 0.02:
        return "gemini-2.5-pro-exp-03-25"
    else:
        return "gpt-4-turbo"  # 质量最优
```

### 6. 监控和性能指标

#### 6.1 性能监控
```python
import time
from dataclasses import dataclass
from typing import Dict, List

@dataclass
class PerformanceMetrics:
    prompt_tokens: int
    completion_tokens: int
    total_tokens: int
    response_time: float
    api_cost: float
    cache_hit: bool

class PerformanceMonitor:
    def __init__(self):
        self.metrics: List[PerformanceMetrics] = []
    
    def record_api_call(self, metrics: PerformanceMetrics):
        self.metrics.append(metrics)
    
    def get_performance_summary(self) -> Dict:
        if not self.metrics:
            return {}
        
        return {
            'avg_response_time': sum(m.response_time for m in self.metrics) / len(self.metrics),
            'total_tokens_used': sum(m.total_tokens for m in self.metrics),
            'total_cost': sum(m.api_cost for m in self.metrics),
            'cache_hit_rate': sum(1 for m in self.metrics if m.cache_hit) / len(self.metrics),
            'avg_prompt_length': sum(m.prompt_tokens for m in self.metrics) / len(self.metrics)
        }
```

## 📊 预期优化效果

### 性能提升预期
- **响应时间**: 减少40-60%（从8-12秒降至3-5秒）
- **Token使用**: 减少50-70%（prompt精简）
- **API成本**: 降低50-60%
- **缓存命中**: 提升至30-50%（相似基金分析）

### 实施优先级
1. **高优先级**: Prompt精简优化（立即实施）
2. **中优先级**: 分层分析策略（1-2周内）
3. **低优先级**: 并发处理和缓存（后续优化）

## 🛠️ 实施计划

### 第一阶段（立即实施）
1. 创建精简版分析规则模板
2. 实现动态prompt构建
3. 优化API调用参数

### 第二阶段（1周内）
1. 实现两阶段分析策略
2. 添加性能监控
3. 数据预处理优化

### 第三阶段（2周内）
1. 实现智能缓存
2. 并发处理优化
3. 模型选择策略

## 📈 监控指标
- API响应时间
- Token使用量
- 成本控制
- 分析质量评分
- 缓存命中率
