"""
检查数据库中可用的基金数据
"""

import sys
import os
sys.path.append('.')

from sqlalchemy import create_engine, text
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 数据库连接
DB_USER = os.getenv("DB_USER")
DB_PASSWORD = os.getenv("DB_PASSWORD")
DB_HOST = os.getenv("DB_HOST", "localhost")
DB_PORT = os.getenv("DB_PORT", "5432")
DB_NAME = os.getenv("DB_NAME")

DATABASE_URL = f"postgresql+psycopg2://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
engine = create_engine(DATABASE_URL)


def check_specific_fund(fund_code):
    """检查特定基金的数据"""

    print("=" * 80)
    print(f"检查基金 {fund_code} 的数据")
    print("=" * 80)

    try:
        with engine.connect() as conn:
            # 1. 查看基金季报数据
            print(f"\n【1. 查看基金季报数据】")
            sql = text("""
                SELECT fund_code, fund_name, report_year, report_quarter, is_latest
                FROM fund_quarterly_data
                WHERE fund_code = :fund_code
                ORDER BY report_year DESC, report_quarter DESC
                LIMIT 10
            """)

            result = conn.execute(sql, {"fund_code": fund_code})
            quarterly_data = result.fetchall()

            if quarterly_data:
                print(f"找到 {len(quarterly_data)} 条季报记录:")
                print(f"{'基金代码':15s} {'基金名称':25s} {'年份':>6s} {'季度':>6s} {'最新':>6s}")
                print("-" * 70)
                for record in quarterly_data:
                    print(f"{record[0]:15s} {record[1][:23]:25s} {record[2]:6d} {record[3]:6d} {str(record[4]):>6s}")
            else:
                print("未找到季报数据")

            # 2. 查看持仓数据
            print(f"\n【2. 查看持仓数据】")
            sql2 = text("""
                SELECT ts_code, end_date, COUNT(*) as stock_count
                FROM tushare_fund_portfolio
                WHERE ts_code = :fund_code
                GROUP BY ts_code, end_date
                ORDER BY end_date DESC
                LIMIT 10
            """)

            result2 = conn.execute(sql2, {"fund_code": fund_code})
            portfolio_data = result2.fetchall()

            if portfolio_data:
                print(f"找到 {len(portfolio_data)} 条持仓记录:")
                print(f"{'基金代码':15s} {'日期':>12s} {'股票数量':>8s}")
                print("-" * 40)
                for record in portfolio_data:
                    print(f"{record[0]:15s} {str(record[1]):>12s} {record[2]:8d}")
            else:
                print("未找到持仓数据")

    except Exception as e:
        print(f"查询失败: {e}")
        import traceback
        traceback.print_exc()


def check_available_funds():
    """检查数据库中可用的基金"""

    print("=" * 80)
    print("检查数据库中可用的基金数据")
    print("=" * 80)
    
    try:
        with engine.connect() as conn:
            # 1. 查看基金季报数据表结构
            print("\n【1. 查看基金季报数据表中的基金】")
            sql = text("""
                SELECT fund_code, fund_name, COUNT(*) as record_count,
                       MAX(report_year) as latest_year, MAX(report_quarter) as latest_quarter
                FROM fund_quarterly_data 
                WHERE fund_name LIKE '%300%' OR fund_name LIKE '%ETF%' OR fund_code LIKE '%510%'
                GROUP BY fund_code, fund_name
                ORDER BY record_count DESC
                LIMIT 20
            """)
            
            result = conn.execute(sql)
            funds = result.fetchall()
            
            if funds:
                print(f"找到 {len(funds)} 个相关基金:")
                print(f"{'基金代码':15s} {'基金名称':30s} {'记录数':>8s} {'最新年份':>8s} {'最新季度':>8s}")
                print("-" * 80)
                for fund in funds:
                    print(f"{fund[0]:15s} {fund[1][:28]:30s} {fund[2]:8d} {fund[3]:8d} {fund[4]:8d}")
            else:
                print("未找到相关基金数据")
            
            # 2. 查看持仓数据表
            print(f"\n【2. 查看基金持仓数据表中的基金】")
            sql2 = text("""
                SELECT ts_code, COUNT(*) as holding_count,
                       MAX(end_date) as latest_date
                FROM tushare_fund_portfolio 
                WHERE ts_code LIKE '%510%' OR ts_code LIKE '%300%'
                GROUP BY ts_code
                ORDER BY holding_count DESC
                LIMIT 20
            """)
            
            result2 = conn.execute(sql2)
            portfolios = result2.fetchall()
            
            if portfolios:
                print(f"找到 {len(portfolios)} 个基金的持仓数据:")
                print(f"{'基金代码':15s} {'持仓记录数':>12s} {'最新日期':>12s}")
                print("-" * 45)
                for portfolio in portfolios:
                    print(f"{portfolio[0]:15s} {portfolio[1]:12d} {str(portfolio[2]):>12s}")
            else:
                print("未找到相关持仓数据")
                
            # 3. 检查具体的510300相关数据
            print(f"\n【3. 检查510300相关的具体数据】")
            sql3 = text("""
                SELECT fund_code, fund_name, report_year, report_quarter, is_latest
                FROM fund_quarterly_data 
                WHERE fund_code LIKE '%510300%' OR fund_name LIKE '%沪深300%'
                ORDER BY report_year DESC, report_quarter DESC
                LIMIT 10
            """)
            
            result3 = conn.execute(sql3)
            specific_funds = result3.fetchall()
            
            if specific_funds:
                print(f"找到 {len(specific_funds)} 条510300/沪深300相关记录:")
                print(f"{'基金代码':15s} {'基金名称':25s} {'年份':>6s} {'季度':>6s} {'最新':>6s}")
                print("-" * 70)
                for fund in specific_funds:
                    print(f"{fund[0]:15s} {fund[1][:23]:25s} {fund[2]:6d} {fund[3]:6d} {str(fund[4]):>6s}")
            else:
                print("未找到510300/沪深300相关数据")
                
            # 4. 检查持仓数据
            print(f"\n【4. 检查510300持仓数据】")
            sql4 = text("""
                SELECT ts_code, end_date, COUNT(*) as stock_count
                FROM tushare_fund_portfolio 
                WHERE ts_code LIKE '%510300%'
                GROUP BY ts_code, end_date
                ORDER BY end_date DESC
                LIMIT 10
            """)
            
            result4 = conn.execute(sql4)
            holdings = result4.fetchall()
            
            if holdings:
                print(f"找到 {len(holdings)} 条510300持仓记录:")
                print(f"{'基金代码':15s} {'日期':>12s} {'股票数量':>8s}")
                print("-" * 40)
                for holding in holdings:
                    print(f"{holding[0]:15s} {str(holding[1]):>12s} {holding[2]:8d}")
            else:
                print("未找到510300持仓数据")
                
    except Exception as e:
        print(f"查询失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 检查特定基金
    check_specific_fund("017488.OF")

    print("\n" + "="*80)
    print("如果需要查看所有基金，请取消下面的注释")
    print("="*80)
    # check_available_funds()
