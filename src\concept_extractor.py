import logging
import json
import re
from typing import List, Dict, Set
from datetime import datetime
import os

logger = logging.getLogger(__name__)

class ConceptExtractor:
    """
    动态概念标签提取器

    功能：
    1. 从基金经理文本中自动提取投资概念
    2. 维护动态概念库
    3. 提供概念标签的智能匹配和分类
    """

    def __init__(self, config_path: str = "output/concept_tags_dynamic.json"):
        self.config_path = config_path
        self.base_concepts = self._load_base_concepts()
        self.dynamic_concepts = self._load_dynamic_concepts()

    def _load_base_concepts(self) -> Dict[str, List[str]]:
        """
        不再使用硬编码的基础概念标签池。
        采用开放性提取方式，完全基于文本内容和动态学习。
        """
        logger.info("采用开放性概念提取方式，不使用预设的概念标签池。")
        return {}

    def _load_dynamic_concepts(self) -> Dict[str, any]:
        """加载动态学习的概念标签"""
        if os.path.exists(self.config_path):
            try:
                with open(self.config_path, "r", encoding="utf-8") as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"无法加载动态概念标签: {e}")

        return {
            "learned_concepts": {},
            "concept_frequency": {},
            "last_updated": datetime.now().isoformat(),
            "total_extractions": 0
        }

    def extract_concepts_from_text(self, text: str, fund_code: str = None) -> List[str]:
        """
        @deprecated 此方法已废弃，不应在新流程中使用。
        请使用新的精简概念提取策略，从LLM JSON输出中提取核心概念。

        从文本中提取概念标签

        Args:
            text: 要分析的文本（市场展望 + 运作分析）
            fund_code: 基金代码（用于记录学习历史）

        Returns:
            提取到的概念标签列表
        """
        logger.warning(f"extract_concepts_from_text 方法已废弃，不应在新流程中使用。基金: {fund_code or 'N/A'}")
        return []

    def _extract_by_patterns(self, text: str) -> List[str]:
        """
        @deprecated 此方法已废弃，不应在新流程中使用。
        基于正则表达式模式提取概念
        """
        logger.warning("_extract_by_patterns 方法已废弃，不应在新流程中使用。")
        return []

    def _match_base_concepts(self, text: str) -> List[str]:
        """
        @deprecated 此方法已废弃，不应在新流程中使用。
        匹配基础概念库中的标签
        """
        logger.warning("_match_base_concepts 方法已废弃，不应在新流程中使用。")
        return []

    def _match_dynamic_concepts(self, text: str) -> List[str]:
        """
        @deprecated 此方法已废弃，不应在新流程中使用。
        匹配动态学习的概念
        """
        logger.warning("_match_dynamic_concepts 方法已废弃，不应在新流程中使用。")
        return []

    def _record_extraction(self, concepts: List[str], fund_code: str = None):
        """记录概念提取历史，用于学习"""
        if not concepts:
            return

        # 更新频率统计
        frequency = self.dynamic_concepts.get("concept_frequency", {})
        for concept in concepts:
            frequency[concept] = frequency.get(concept, 0) + 1

        # 更新学习的概念
        learned = self.dynamic_concepts.get("learned_concepts", {})
        for concept in concepts:
            if concept not in learned:
                learned[concept] = {
                    "first_seen": datetime.now().isoformat(),
                    "category": "auto_extracted",
                    "source_funds": []
                }

            if fund_code and fund_code not in learned[concept]["source_funds"]:
                learned[concept]["source_funds"].append(fund_code)

        # 更新统计信息
        self.dynamic_concepts["concept_frequency"] = frequency
        self.dynamic_concepts["learned_concepts"] = learned
        self.dynamic_concepts["last_updated"] = datetime.now().isoformat()
        self.dynamic_concepts["total_extractions"] = self.dynamic_concepts.get("total_extractions", 0) + 1

        # 保存到文件
        self._save_dynamic_concepts()

    def _save_dynamic_concepts(self):
        """保存动态概念到文件"""
        try:
            with open(self.config_path, "w", encoding="utf-8") as f:
                json.dump(self.dynamic_concepts, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存动态概念标签失败: {e}")

    def get_concept_statistics(self) -> Dict:
        """获取概念标签统计信息"""
        frequency = self.dynamic_concepts.get("concept_frequency", {})
        learned = self.dynamic_concepts.get("learned_concepts", {})

        # 按频率排序
        sorted_concepts = sorted(frequency.items(), key=lambda x: x[1], reverse=True)

        return {
            "total_concepts": len(learned),
            "total_extractions": self.dynamic_concepts.get("total_extractions", 0),
            "last_updated": self.dynamic_concepts.get("last_updated"),
            "top_concepts": sorted_concepts[:20],
            "recent_concepts": [
                concept for concept, info in learned.items()
                if datetime.fromisoformat(info["first_seen"]).date() == datetime.now().date()
            ]
        }

    def suggest_concept_categories(self) -> Dict[str, List[str]]:
        """基于学习的概念，建议新的分类"""
        learned = self.dynamic_concepts.get("learned_concepts", {})
        frequency = self.dynamic_concepts.get("concept_frequency", {})

        # 简单的分类逻辑（可以进一步优化）
        categories = {
            "investment_themes": [],
            "strategy_keywords": [],
            "market_styles": [],
            "industry_concepts": [],
            "others": []
        }

        for concept, freq in frequency.items():
            if freq < 2:  # 频率太低的概念暂不分类
                continue

            concept_lower = concept.lower()

            if any(keyword in concept_lower for keyword in ["应用", "概念", "主题", "新", "智能", "数字"]):
                categories["investment_themes"].append(concept)
            elif any(keyword in concept_lower for keyword in ["确定性", "赔率", "估值", "买入", "持有"]):
                categories["strategy_keywords"].append(concept)
            elif any(keyword in concept_lower for keyword in ["成长", "价值", "红利", "风格"]):
                categories["market_styles"].append(concept)
            elif any(keyword in concept_lower for keyword in ["行业", "板块", "赛道", "制造", "金融"]):
                categories["industry_concepts"].append(concept)
            else:
                categories["others"].append(concept)

        return categories

# 全局实例
concept_extractor = ConceptExtractor()

def extract_concepts_from_fund_text(market_outlook: str, operation_analysis: str, fund_code: str = None) -> List[str]:
    """
    @deprecated 此函数已废弃，不应在新流程中使用。
    请使用新的精简概念提取策略，从LLM JSON输出中提取核心概念。

    便捷函数：从基金文本中提取概念标签

    Args:
        market_outlook: 市场展望文本
        operation_analysis: 运作分析文本
        fund_code: 基金代码

    Returns:
        概念标签列表
    """
    logger.warning(f"extract_concepts_from_fund_text 函数已废弃，不应在新流程中使用。基金: {fund_code or 'N/A'}")
    return []

def get_concept_statistics() -> Dict:
    """获取概念标签统计信息"""
    return concept_extractor.get_concept_statistics()

def suggest_new_categories() -> Dict[str, List[str]]:
    """建议新的概念分类"""
    return concept_extractor.suggest_concept_categories()

def record_and_save_core_concepts(core_concepts: List[str], fund_code: str = None):
    """
    公共接口函数，用于接收一组预先提取的核心概念列表，
    并利用全局的 concept_extractor 实例来记录和保存这些概念到动态知识库。

    Args:
        core_concepts: 从外部（如LLM JSON输出）提取的核心概念字符串列表。
        fund_code: 相关的基金代码。
    """
    global concept_extractor  # 使用文件底部的全局实例
    if not core_concepts:
        logger.info(f"record_and_save_core_concepts: 未提供核心概念列表 (基金: {fund_code or 'N/A'})，不执行操作。")
        return

    logger.info(f"基金 {fund_code or 'N/A'}: 准备记录核心概念: {core_concepts}")
    try:
        # 调用 ConceptExtractor 实例的内部记录方法
        # _record_extraction 内部会处理频率更新、新概念学习和调用保存
        concept_extractor._record_extraction(core_concepts, fund_code)
        logger.info(f"基金 {fund_code or 'N/A'}: 核心概念已成功记录和保存。")
    except Exception as e:
        logger.error(f"基金 {fund_code or 'N/A'}: 记录和保存核心概念失败: {e}", exc_info=True)

def record_and_save_enriched_analysis(extracted_data: dict, fund_code: str = None):
    """
    扩展的知识图谱记录函数，处理多维度分析信息

    Args:
        extracted_data: 从LLM JSON输出中提取的多维度信息字典
        fund_code: 相关的基金代码
    """
    global concept_extractor

    if not extracted_data:
        logger.info(f"record_and_save_enriched_analysis: 未提供分析数据 (基金: {fund_code or 'N/A'})，不执行操作。")
        return

    logger.info(f"基金 {fund_code or 'N/A'}: 准备记录多维度分析信息...")

    try:
        # 1. 先处理概念信息（保持现有逻辑）
        concepts_data = extracted_data.get("concepts", {})
        if concepts_data:
            core_concepts = []

            # 提取核心概念
            focus_exp_tag = concepts_data.get("focus_exp_tag")
            if focus_exp_tag and isinstance(focus_exp_tag, str):
                cleaned_focus_exp_tag = focus_exp_tag.strip('# ')
                if cleaned_focus_exp_tag:
                    core_concepts.append(cleaned_focus_exp_tag)

            # 提取基金经理标签
            manager_tags = concepts_data.get("manager_tags")
            if manager_tags and isinstance(manager_tags, str):
                manager_tags_list = [tag.strip() for tag in manager_tags.split(',') if tag.strip()]
                MAX_MANAGER_TAGS = 2
                tags_added = 0
                for tag in manager_tags_list:
                    if tags_added < MAX_MANAGER_TAGS and tag not in core_concepts:
                        core_concepts.append(tag)
                        tags_added += 1
                    if tags_added >= MAX_MANAGER_TAGS:
                        break

            # 记录概念
            if core_concepts:
                concept_extractor._record_extraction(core_concepts, fund_code)

        # 2. 处理基金经理信息
        _update_manager_info(extracted_data.get("manager_info", {}), fund_code)

        # 3. 处理投资策略信息
        _update_strategy_info(extracted_data.get("investment_strategy", {}), fund_code)

        # 4. 处理组合特征信息
        _update_portfolio_info(extracted_data.get("portfolio_features", {}), fund_code)

        # 5. 处理行业信息
        _update_industry_info(extracted_data.get("industry_info", {}), fund_code)

        # 6. 处理洞察信息
        _update_insights_info(extracted_data.get("insights", {}), fund_code)

        logger.info(f"基金 {fund_code or 'N/A'}: 多维度分析信息已成功记录和保存。")

    except Exception as e:
        logger.error(f"基金 {fund_code or 'N/A'}: 记录和保存多维度分析信息失败: {e}", exc_info=True)

def _update_manager_info(manager_info: dict, fund_code: str):
    """更新基金经理信息到知识图谱"""
    global concept_extractor

    if not manager_info or not manager_info.get("name"):
        return

    manager_name = manager_info.get("name")
    if not manager_name or manager_name in ["[PLACEHOLDER_MANAGER_NAME]", "[未提供]"]:
        return

    # 扩展动态概念数据结构，添加基金经理信息
    if "managers" not in concept_extractor.dynamic_concepts:
        concept_extractor.dynamic_concepts["managers"] = {}

    managers = concept_extractor.dynamic_concepts["managers"]

    if manager_name not in managers:
        managers[manager_name] = {
            "first_seen": datetime.now().isoformat(),
            "managed_funds": [],
            "analysis_count": 0
        }

    # 更新基金经理信息
    if fund_code and fund_code not in managers[manager_name]["managed_funds"]:
        managers[manager_name]["managed_funds"].append(fund_code)

    managers[manager_name]["analysis_count"] = managers[manager_name].get("analysis_count", 0) + 1
    managers[manager_name]["last_seen"] = datetime.now().isoformat()

    # 保存更新
    concept_extractor._save_dynamic_concepts()
    logger.debug(f"已更新基金经理信息: {manager_name}")

def _update_strategy_info(strategy_info: dict, fund_code: str):
    """更新投资策略信息到知识图谱"""
    global concept_extractor

    if not strategy_info:
        return

    # 扩展动态概念数据结构，添加投资策略信息
    if "investment_strategies" not in concept_extractor.dynamic_concepts:
        concept_extractor.dynamic_concepts["investment_strategies"] = {}

    strategies = concept_extractor.dynamic_concepts["investment_strategies"]

    # 提取策略特征
    strategy_features = {}
    for key, value in strategy_info.items():
        if value and value not in ["[未提供]", ""]:
            strategy_features[key] = value

    if strategy_features and fund_code:
        if fund_code not in strategies:
            strategies[fund_code] = {
                "first_seen": datetime.now().isoformat(),
                "strategy_history": []
            }

        # 添加策略记录
        strategy_record = {
            "timestamp": datetime.now().isoformat(),
            "features": strategy_features
        }
        strategies[fund_code]["strategy_history"].append(strategy_record)
        strategies[fund_code]["last_updated"] = datetime.now().isoformat()

        # 保存更新
        concept_extractor._save_dynamic_concepts()
        logger.debug(f"已更新基金 {fund_code} 的投资策略信息")

def _update_portfolio_info(portfolio_info: dict, fund_code: str):
    """更新组合特征信息到知识图谱"""
    global concept_extractor

    if not portfolio_info:
        return

    # 扩展动态概念数据结构，添加组合特征信息
    if "portfolio_features" not in concept_extractor.dynamic_concepts:
        concept_extractor.dynamic_concepts["portfolio_features"] = {}

    portfolios = concept_extractor.dynamic_concepts["portfolio_features"]

    # 提取组合特征
    portfolio_features = {}
    for key, value in portfolio_info.items():
        if value and value not in ["[未提供]", ""]:
            portfolio_features[key] = value

    if portfolio_features and fund_code:
        if fund_code not in portfolios:
            portfolios[fund_code] = {
                "first_seen": datetime.now().isoformat(),
                "portfolio_history": []
            }

        # 添加组合记录
        portfolio_record = {
            "timestamp": datetime.now().isoformat(),
            "features": portfolio_features
        }
        portfolios[fund_code]["portfolio_history"].append(portfolio_record)
        portfolios[fund_code]["last_updated"] = datetime.now().isoformat()

        # 保存更新
        concept_extractor._save_dynamic_concepts()
        logger.debug(f"已更新基金 {fund_code} 的组合特征信息")

def _update_industry_info(industry_info: dict, fund_code: str):
    """更新行业信息到知识图谱"""
    global concept_extractor

    if not industry_info:
        return

    sw_l1 = industry_info.get("sw_l1")
    if not sw_l1 or sw_l1 in ["[未提供]", ""]:
        return

    # 扩展动态概念数据结构，添加行业信息
    if "industries" not in concept_extractor.dynamic_concepts:
        concept_extractor.dynamic_concepts["industries"] = {}

    industries = concept_extractor.dynamic_concepts["industries"]

    if sw_l1 not in industries:
        industries[sw_l1] = {
            "first_seen": datetime.now().isoformat(),
            "related_funds": [],
            "frequency": 0
        }

    # 更新行业信息
    if fund_code and fund_code not in industries[sw_l1]["related_funds"]:
        industries[sw_l1]["related_funds"].append(fund_code)

    industries[sw_l1]["frequency"] = industries[sw_l1].get("frequency", 0) + 1
    industries[sw_l1]["last_seen"] = datetime.now().isoformat()

    # 保存更新
    concept_extractor._save_dynamic_concepts()
    logger.debug(f"已更新行业信息: {sw_l1}")

def _update_insights_info(insights_info: dict, fund_code: str):
    """更新洞察信息到知识图谱"""
    global concept_extractor

    if not insights_info:
        return

    # 扩展动态概念数据结构，添加洞察信息
    if "insights" not in concept_extractor.dynamic_concepts:
        concept_extractor.dynamic_concepts["insights"] = {}

    insights = concept_extractor.dynamic_concepts["insights"]

    # 提取洞察特征
    insight_features = {}
    for key, value in insights_info.items():
        if value and value not in ["[未提供]", ""]:
            insight_features[key] = value

    if insight_features and fund_code:
        if fund_code not in insights:
            insights[fund_code] = {
                "first_seen": datetime.now().isoformat(),
                "insights_history": []
            }

        # 添加洞察记录
        insight_record = {
            "timestamp": datetime.now().isoformat(),
            "features": insight_features
        }
        insights[fund_code]["insights_history"].append(insight_record)
        insights[fund_code]["last_updated"] = datetime.now().isoformat()

        # 保存更新
        concept_extractor._save_dynamic_concepts()
        logger.debug(f"已更新基金 {fund_code} 的洞察信息")

def record_concept_mentions(extracted_data: dict, fund_code: str, manager_name: str, report_date: str):
    """
    记录概念提及事件，用于基金经理追踪和关系分析

    Args:
        extracted_data: 从LLM JSON输出中提取的多维度信息字典
        fund_code: 基金代码
        manager_name: 基金经理姓名
        report_date: 报告日期
    """
    if not extracted_data or not fund_code:
        return

    # 确保输出目录存在
    output_dir = "output"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    mentions_file = os.path.join(output_dir, "concept_mentions.jsonl")

    # 提取概念列表
    concepts_data = extracted_data.get("concepts", {})
    extracted_concepts = []

    # 从focus_exp_tag提取概念
    focus_exp_tag = concepts_data.get("focus_exp_tag")
    if focus_exp_tag and isinstance(focus_exp_tag, str):
        cleaned_focus_exp_tag = focus_exp_tag.strip('# ')
        if cleaned_focus_exp_tag:
            extracted_concepts.append(cleaned_focus_exp_tag)

    # 从manager_tags提取概念
    manager_tags = concepts_data.get("manager_tags")
    if manager_tags:
        if isinstance(manager_tags, str):
            # 尝试解析JSON数组字符串
            try:
                import json
                manager_tags_list = json.loads(manager_tags)
                if isinstance(manager_tags_list, list):
                    for tag in manager_tags_list[:2]:  # 取前2个
                        if tag and tag not in extracted_concepts:
                            extracted_concepts.append(tag)
                else:
                    # 如果不是数组，按逗号分割
                    manager_tags_list = [tag.strip() for tag in manager_tags.split(',') if tag.strip()]
                    for tag in manager_tags_list[:2]:  # 取前2个
                        if tag not in extracted_concepts:
                            extracted_concepts.append(tag)
            except json.JSONDecodeError:
                # JSON解析失败，按逗号分割
                manager_tags_list = [tag.strip() for tag in manager_tags.split(',') if tag.strip()]
                for tag in manager_tags_list[:2]:  # 取前2个
                    if tag not in extracted_concepts:
                        extracted_concepts.append(tag)
        elif isinstance(manager_tags, list):
            # 如果已经是列表
            for tag in manager_tags[:2]:  # 取前2个
                if tag and tag not in extracted_concepts:
                    extracted_concepts.append(tag)

    # 记录每个概念的提及事件
    timestamp = datetime.now().isoformat()

    try:
        with open(mentions_file, 'a', encoding='utf-8') as f:
            for concept_name in extracted_concepts:
                mention_event = {
                    "timestamp": timestamp,
                    "fund_code": fund_code,
                    "manager_name": manager_name or "[未知]",
                    "report_date": report_date or "[未知]",
                    "concept_name": concept_name,
                    "extraction_source": "llm_json"
                }
                f.write(json.dumps(mention_event, ensure_ascii=False) + '\n')

        logger.debug(f"已记录 {len(extracted_concepts)} 个概念提及事件到 {mentions_file}")

    except Exception as e:
        logger.error(f"记录概念提及事件失败: {e}", exc_info=True)
