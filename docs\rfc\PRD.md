# 代码库结构、映射关系与产品需求文档

## 一、代码库结构树状图

```
.
├── .specstory/  (主要功能: 行为驱动开发(BDD)或规范化测试/文档产物)
│   ├── .what-is-this.md (说明性文件，解释此目录用途)
│   └── history/ (主要功能: 规范/文档变更历史)
│       ├── 2025-05-15_14-18-untitled.md (历史文档)
│       └── 2025-05-15_10-49-数据库查询与港股功能扩展.md (历史文档)
├── .git/ (主要功能: Git 版本控制系统目录 - 通常不直接交互)
├── .roo/ (主要功能: 名为 'roo' 的工具的配置或元数据)
│   ├── mcp.json (配置文件)
│   └── rules-architect/ (主要功能: 'roo' 架构组件的规则定义)
│       └── rules.md (规则文档)
├── .venv/ (主要功能: Python 虚拟环境文件 - 管理项目依赖)
│   ├── Include/ (用于已编译扩展的 C 头文件)
│   ├── Lib/ (虚拟环境的 Python 库)
│   ├── Scripts/ (环境的可执行脚本和 Python 解释器)
│   └── pyvenv.cfg (虚拟环境的配置文件)
├── memory-bank/ (主要功能: 项目文档和AI助手上下文)
│   ├── activeContext.md (当前工作焦点和上下文)
│   ├── decisionLog.md (决策日志)
│   ├── productContext.md (产品背景和目标)
│   ├── progress.md (项目进展和状态)
│   └── systemPatterns.md (系统架构和设计模式)
├── reports/ (主要功能: 生成的报告，可能来自数据分析或测试)
│   ├── 005620_OF_20250515_213211.md (基金分析报告)
│   ├── 005620_OF_20250515_213211_summary.csv (基金分析摘要CSV)
│   ├── 519915_OF_20250515_213224.md (基金分析报告)
│   ├── 519915_OF_20250515_213224_summary.csv (基金分析摘要CSV)
│   ├── batch_summary_20250515_210934.txt (批量处理摘要)
│   ├── batch_summary_20250515_211618.txt (批量处理摘要)
│   └── batch_summary_20250515_213224.txt (批量处理摘要)
├── src/ (主要功能: 项目的源代码 - 核心业务逻辑)
│   ├── __pycache__/ (主要功能: Python 字节码缓存 - 自动生成)
│   │   ├── data_fetcher.cpython-311.pyc (data_fetcher模块的字节码)
│   │   ├── llm_analyzer.cpython-311.pyc (llm_analyzer模块的字节码)
│   │   └── report_generator.cpython-311.pyc (report_generator模块的字节码)
│   ├── data_fetcher.py (主要功能: 数据获取模块; **映射关系**: 可能被 `llm_analyzer.py` 或 `main.py` 调用以获取数据，并将数据传递给 `report_generator.py`)
│   ├── llm_analyzer.py (主要功能: 大语言模型分析模块; **映射关系**: 可能调用 `data_fetcher.py` 获取数据，处理后结果可能由 `report_generator.py` 生成报告或被 `main.py` 调用)
│   └── report_generator.py (主要功能: 报告生成模块; **映射关系**: 接收来自 `data_fetcher.py` 或 `llm_analyzer.py` 的数据，生成报告并存储于 `reports/` 目录)
├── .cursorindexingignore (主要功能: 指定 Cursor AI 在索引时应忽略的文件/目录)
├── .gitignore (主要功能: 指定 Git 应忽略的、有意不去追踪的文件)
├── RFC.md (主要功能: 请求意见稿 - 设计文档或提案)
├── fund_list.txt (主要功能: 基金代码列表 - 很可能作为 `data_fetcher.py` 的输入数据)
├── implementation_plan_v2.md (主要功能: 项目V2版本实施计划文档)
├── main.py (主要功能: 主程序入口; **映射关系**: 协调 `src/` 目录下的各个模块，例如调用 `data_fetcher.py` 和 `llm_analyzer.py`，并可能触发 `report_generator.py` 来生成最终报告)
├── requirements.txt (主要功能: Python 项目依赖列表 - 用于设置 `.venv/` 虚拟环境)
├── tusharedb_data_dictionary.md (主要功能: Tushare 数据库的数据字典或说明文档)
└── 前置rules.md (主要功能: 项目的前置规则、指南或约束条件文档)
```

## 二、推断的映射关系图谱 (高层级)

*   **`main.py`**: 作为项目的总指挥。它很可能调用 `src/` 目录中的模块来完成主要任务流程。
*   **`src/data_fetcher.py`**: 负责获取数据。输入可能来自 `fund_list.txt`，数据源的定义可能参考 `tusharedb_data_dictionary.md`。获取到的数据会被传递给其他模块进行处理或分析。
*   **`src/llm_analyzer.py`**: 进行数据分析，特别是可能利用大语言模型的能力。它处理由 `data_fetcher.py` 提供的数据。
*   **`src/report_generator.py`**: 接收来自 `data_fetcher.py` 或 `llm_analyzer.py` 的处理后数据，并负责生成结构化的报告，这些报告最终存储在 `reports/` 目录下。
*   **`requirements.txt`**: 定义了项目所需的Python库，这些库被安装在 `.venv/` 虚拟环境中，供 `main.py` 及 `src/` 目录下的模块使用。
*   **`memory-bank/`**: 包含一系列Markdown文件，用于记录项目的背景信息、当前进展、设计决策等，主要供AI助手（例如我）理解项目并协助开发。
*   **`.specstory/`**: 包含与行为驱动开发(BDD)或需求规格说明相关的文档，可能用于定义系统（如 `main.py` 和 `src/` 模块）的预期行为。
*   **`.roo/`**: 似乎是某个特定工具（可能名为"roo"）的配置文件目录，其中的 `rules.md` 可能定义了一些规则，影响 `main.py` 或系统其他部分的行为。

## 三、产品需求文档 (PRD) - 模板

### 1. 引言

本文档阐述了"金融数据分析与报告工具"的产品需求。该工具旨在获取金融数据，执行分析（可能利用大语言模型），并生成结构化报告。

### 2. 项目目标

*   自动化获取指定实体（例如 `fund_list.txt` 中列出的基金）的金融数据流程。
*   对获取的数据提供分析洞察，可能利用先进的AI/LLM能力。
*   基于分析结果生成全面且易于理解的报告。
*   维护清晰、文档化的代码库 (`src/`) 和操作上下文 (`memory-bank/`)。

### 3. 目标用户

*   金融分析师
*   数据科学家
*   投资研究员
*   管理金融投资组合的个人

### 4. 产品功能

#### 4.1. 数据获取 (`data_fetcher.py`)
    *   **需求 1.1**: 系统必须能够从指定数据源获取数据（数据源细节待定义，可能与 `tusharedb_data_dictionary.md` 相关）。
    *   **需求 1.2**: 系统应能接受一个实体列表（例如来自 `fund_list.txt` 的基金代码）作为数据获取的目标。
    *   **需求 1.3**: 数据获取过程应具备鲁棒性，能处理潜在的网络错误或API限制。

#### 4.2. 数据分析 (`llm_analyzer.py`)
    *   **需求 2.1**: 系统应提供分析已获取金融数据的模块。
    *   **需求 2.2**: (若适用) 系统应集成大语言模型以提供定性或定量分析。
    *   **需求 2.3**: 分析参数应可配置。

#### 4.3. 报告生成 (`report_generator.py`, `reports/`)
    *   **需求 3.1**: 系统必须能根据执行的分析生成报告。
    *   **需求 3.2**: 报告应存储在指定的目录 (`reports/`) 中。
    *   **需求 3.3**: 报告应支持人类可读的格式 (例如 `reports/` 目录中见到的 Markdown, CSV, TXT)。
    *   **需求 3.4**: 报告应清晰呈现关键发现，并在适用时包含数据可视化。

#### 4.4. 任务调度与执行 (`main.py`)
    *   **需求 4.1**: 应有一个主脚本协调数据获取、分析和报告生成的工作流程。
    *   **需求 4.2**: 主脚本应能处理命令行参数或配置文件，以支持不同的操作模式。

#### 4.5. 配置与管理
    *   **需求 5.1**: 系统应使用虚拟环境 (`.venv/`) 管理 `requirements.txt` 中列出的Python依赖。
    *   **需求 5.2**: 必须使用版本控制 (`.git/`) 追踪代码变更。
    *   **需求 5.3**: 应维护项目文档和上下文信息 (如 `memory-bank/` 中的文件, `RFC.md`, `implementation_plan_v2.md`)。

### 5. 非功能性需求

*   **NFR 1 (性能)**: 数据获取和分析应在可接受的时间限制内完成。
*   **NFR 2 (可靠性)**: 系统应稳定可靠，并产出一致的结果。
*   **NFR 3 (可维护性)**: 代码库应结构良好、注释清晰且易于维护。
*   **NFR 4 (易用性)**: 工具应相对易于使用、配置和运行。

### 6. 未来考虑 (推测)

*   为简化交互提供Web界面。
*   支持更多类型的数据源。
*   在报告中提供更高级的可视化选项。
*   为敏感金融信息提供用户认证和数据安全机制。
*   基于 `.roo/` 目录实现更复杂的"规则"引擎。
*   集成 `.specstory/` 所暗示的BDD测试框架。 