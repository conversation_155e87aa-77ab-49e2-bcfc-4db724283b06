#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多维度新老模型对比测试工具
进行全面的质量、效率、一致性等多维度分析
"""

import os
import json
import logging
import time
import statistics
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv

# 导入项目模块
from src.llm_analyzer import analyze_with_llm
from src.data_fetcher import fetch_fund_data, fetch_fund_portfolio_data
from src.external_data_provider import get_external_stock_details
from src.report_generator import _extract_json_from_report as extract_json_from_report

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_analysis_rules():
    """加载分析规则"""
    try:
        with open("docs/深度分析专精.md", "r", encoding="utf-8") as f:
            return f.read()
    except FileNotFoundError:
        logger.error("分析规则文件未找到")
        return ""

class MultiDimensionalComparator:
    """多维度模型对比器"""
    
    def __init__(self):
        load_dotenv()
        
        # 老模型配置
        self.old_model_config = {
            "name": "vertexai/gemini-2.5-pro-preview-05-06",
            "api_key": "sk-mqWRZm6zFLW04WRL72FeEcF983634a5c8bF7Ef1b955eC3Ab",
            "api_base": "http://206.189.40.22:3009/v1"
        }
        
        # 新模型配置
        self.new_model_config = {
            "name": "gemini-2.5-pro-preview-06-05",
            "api_key": "sk-gRq2nHouhXUgRqVzdnKbQYVDwPYIhLj94NmG2rgTjAuZA8SC",
            "api_base": "https://axhixwxzsmtd.us-west-1.clawcloudrun.com/v1"
        }
        
        self.analysis_rules = load_analysis_rules()
        self.results_dir = Path("quick_test_results")
        self.results_dir.mkdir(exist_ok=True)
        
        # 测试基金列表 - 从reports文件夹随机选择
        self.test_funds = [
            "630005.OF",  # 随机选择基金1
            "003993.OF",  # 随机选择基金2
            "009861.OF"   # 随机选择基金3
        ]
        
    def prepare_fund_data(self, fund_code):
        """准备基金数据"""
        logger.info(f"准备基金 {fund_code} 的数据...")
        
        # 获取基金基本数据
        fund_data = fetch_fund_data(fund_code)
        if not fund_data:
            logger.error(f"无法获取基金 {fund_code} 的基本数据")
            return None
            
        # 获取持仓数据
        portfolio_data = fetch_fund_portfolio_data(fund_code, fund_data.get('report_end_date', '2025-03-31'))
        if not portfolio_data:
            logger.warning(f"无法获取基金 {fund_code} 的持仓数据")
            portfolio_data = []
            
        # 获取外部估值数据（简化版）
        try:
            external_data = {}
            for item in portfolio_data[:5]:  # 只处理前5个持仓以节省时间
                symbol = item.get('symbol')
                if symbol:
                    stock_external_data = get_external_stock_details(symbol)
                    if stock_external_data:
                        external_data[symbol] = stock_external_data
            logger.info(f"成功获取 {len(external_data)} 只股票的外部估值数据")
        except Exception as e:
            logger.warning(f"获取外部估值数据失败: {e}")
            external_data = {}
            
        # 合并估值数据
        for item in portfolio_data:
            symbol = item.get('symbol')
            if symbol in external_data:
                item.update(external_data[symbol])
                
        return {
            'fund_data': fund_data,
            'portfolio_data': portfolio_data
        }
        
    def test_single_model(self, model_config, fund_code, prepared_data, test_round):
        """测试单个模型"""
        logger.info(f"第{test_round}轮测试 - 使用模型 {model_config['name']} 分析基金 {fund_code}")
        
        fund_data = prepared_data['fund_data']
        portfolio_data = prepared_data['portfolio_data']
        
        # 格式化持仓数据
        formatted_portfolio_items = []
        for item in portfolio_data:
            total_mv_wanyuan = item.get('total_mv', 0)
            if isinstance(total_mv_wanyuan, (int, float)) and total_mv_wanyuan > 0:
                total_mv_str = f"{total_mv_wanyuan / 10000:.2f}亿元"
            else:
                total_mv_str = "未知市值"
                
            market_cap_style_str = item.get('market_cap_style', '未知风格')
            pe_str = f"{item.get('pe', 'N/A')}" if item.get('pe') is not None else "N/A"
            pe_ttm_str = f"{item.get('pe_ttm', 'N/A')}" if item.get('pe_ttm') is not None else "N/A"
            pb_str = f"{item.get('pb', 'N/A')}" if item.get('pb') is not None else "N/A"
            
            formatted_portfolio_items.append(
                f"- {item['symbol']} ({item.get('stock_name', '未知名称')} - {item.get('sw_l1_industry', '未知行业')}): "
                f"总市值: {total_mv_str}, 市值风格: {market_cap_style_str}, "
                f"PE: {pe_str}, PE(TTM): {pe_ttm_str}, PB: {pb_str}, "
                f"占净值比例 {item.get('stk_mkv_ratio', 'N/A')}%"
            )
            
        portfolio_data_str = "\n".join(formatted_portfolio_items) if formatted_portfolio_items else "未提供持仓数据。"
        
        # 记录开始时间
        start_time = time.time()
        
        # 调用LLM分析
        try:
            result = analyze_with_llm(
                fund_code=fund_code,
                fund_name=fund_data.get('fund_name', ''),
                fund_manager=fund_data.get('fund_manager', ''),
                report_end_date=fund_data.get('report_end_date', ''),
                market_outlook=fund_data.get('market_outlook', ''),
                operation_analysis=fund_data.get('operation_analysis', ''),
                portfolio_data=portfolio_data_str,
                sw_industry_info="多维度测试用申万行业信息",
                valuation_summary="多维度测试用估值信息",
                scale_analysis="多维度测试用规模分析",
                analysis_rules=self.analysis_rules,
                model_name=model_config['name'],
                api_key=model_config['api_key'],
                api_base=model_config['api_base']
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            if result:
                logger.info(f"模型 {model_config['name']} 分析成功，报告长度: {len(result)} 字符，耗时: {duration:.1f}秒")
                return {
                    'success': True,
                    'result': result,
                    'duration': duration,
                    'length': len(result),
                    'fund_code': fund_code,
                    'model_name': model_config['name'],
                    'test_round': test_round
                }
            else:
                logger.error(f"模型 {model_config['name']} 分析失败")
                return {
                    'success': False,
                    'error': 'Analysis failed',
                    'duration': duration,
                    'fund_code': fund_code,
                    'model_name': model_config['name'],
                    'test_round': test_round
                }
                
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            logger.error(f"模型 {model_config['name']} 分析出错: {e}")
            return {
                'success': False,
                'error': str(e),
                'duration': duration,
                'fund_code': fund_code,
                'model_name': model_config['name'],
                'test_round': test_round
            }
            
    def analyze_quality_dimensions(self, result_text):
        """多维度质量分析"""
        analysis = {}
        
        # 1. 基础指标
        analysis['length'] = len(result_text)
        analysis['lines'] = len(result_text.split('\n'))
        analysis['words'] = len(result_text.split())
        
        # 2. JSON格式检查
        json_data = extract_json_from_report(result_text)
        analysis['json_valid'] = json_data is not None
        if json_data:
            analysis['json_fields_count'] = len(json_data)
            analysis['json_completeness'] = len(json_data) / 33  # 期望33个字段
        else:
            analysis['json_fields_count'] = 0
            analysis['json_completeness'] = 0
            
        # 3. 结构完整性
        required_sections = [
            "核心洞察与关键评估摘要",
            "核心投资逻辑摘要", 
            "投资组合与变动分析",
            "业绩归因与关键驱动",
            "关键洞察与风险提示",
            "其他重要信息",
            "表格数据"
        ]
        found_sections = sum(1 for section in required_sections if section in result_text)
        analysis['structure_completeness'] = found_sections / len(required_sections)
        
        # 4. 重点标识使用
        emoji_indicators = ['🔴', '🟡', '🟢']
        analysis['emoji_count'] = sum(result_text.count(emoji) for emoji in emoji_indicators)
        analysis['emoji_density'] = analysis['emoji_count'] / max(analysis['length'] / 1000, 1)  # 每千字符的标识数
        
        # 5. 专业术语密度
        professional_terms = [
            '投资逻辑', '持仓', '估值', '基本面', '风险', '收益', '配置', '选股',
            '市值', '行业', '组合', '策略', '管理', '分析', '评估', '预期'
        ]
        term_count = sum(result_text.count(term) for term in professional_terms)
        analysis['professional_density'] = term_count / max(analysis['words'], 1)
        
        # 6. 信息密度评估
        analysis['info_density'] = analysis['words'] / max(analysis['length'], 1)
        
        # 7. 综合质量评分
        quality_score = (
            min(analysis['length'] / 28000, 1.0) * 0.2 +  # 长度评分
            analysis['json_completeness'] * 0.3 +  # JSON完整性
            analysis['structure_completeness'] * 0.2 +  # 结构完整性
            min(analysis['emoji_count'] / 10, 1.0) * 0.1 +  # 标识使用
            min(analysis['professional_density'] * 10, 1.0) * 0.2  # 专业性
        )
        analysis['overall_quality'] = quality_score
        
        return analysis
        
    def run_multi_round_test(self, rounds=3):
        """运行多轮测试"""
        print(f"🚀 开始多维度新老模型对比测试 ({rounds}轮)")
        print(f"老模型: {self.old_model_config['name']}")
        print(f"新模型: {self.new_model_config['name']}")
        print(f"测试基金: {', '.join(self.test_funds)}")
        print("=" * 80)
        
        all_results = []
        
        for round_num in range(1, rounds + 1):
            print(f"\n🔄 第 {round_num}/{rounds} 轮测试")
            print("-" * 60)
            
            for fund_code in self.test_funds:
                print(f"\n📊 测试基金: {fund_code}")
                
                # 准备数据
                prepared_data = self.prepare_fund_data(fund_code)
                if not prepared_data:
                    print(f"❌ 基金 {fund_code} 数据准备失败")
                    continue
                
                # 测试老模型
                print(f"  🔄 测试老模型...")
                old_result = self.test_single_model(self.old_model_config, fund_code, prepared_data, round_num)
                
                # 等待一下避免API限制
                time.sleep(2)
                
                # 测试新模型
                print(f"  🔄 测试新模型...")
                new_result = self.test_single_model(self.new_model_config, fund_code, prepared_data, round_num)
                
                # 保存结果
                if old_result['success'] and new_result['success']:
                    # 保存原始报告
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    
                    old_file = self.results_dir / f"round{round_num}_{fund_code}_old_{timestamp}.md"
                    new_file = self.results_dir / f"round{round_num}_{fund_code}_new_{timestamp}.md"
                    
                    with open(old_file, 'w', encoding='utf-8') as f:
                        f.write(old_result['result'])
                    with open(new_file, 'w', encoding='utf-8') as f:
                        f.write(new_result['result'])
                    
                    # 质量分析
                    old_analysis = self.analyze_quality_dimensions(old_result['result'])
                    new_analysis = self.analyze_quality_dimensions(new_result['result'])
                    
                    # 记录结果
                    comparison_result = {
                        'round': round_num,
                        'fund_code': fund_code,
                        'timestamp': timestamp,
                        'old_model': {
                            'name': self.old_model_config['name'],
                            'duration': old_result['duration'],
                            'analysis': old_analysis,
                            'file': str(old_file)
                        },
                        'new_model': {
                            'name': self.new_model_config['name'],
                            'duration': new_result['duration'],
                            'analysis': new_analysis,
                            'file': str(new_file)
                        }
                    }
                    
                    all_results.append(comparison_result)
                    
                    # 显示简要结果
                    print(f"    ✅ 老模型: {old_analysis['overall_quality']:.3f}分, {old_result['duration']:.1f}秒")
                    print(f"    ✅ 新模型: {new_analysis['overall_quality']:.3f}分, {new_result['duration']:.1f}秒")
                    
                else:
                    print(f"    ❌ 测试失败")
                    
                # 等待一下避免API限制
                time.sleep(3)
        
        # 生成综合分析报告
        self.generate_comprehensive_report(all_results)
        
        return all_results
        
    def generate_comprehensive_report(self, all_results):
        """生成综合分析报告"""
        if not all_results:
            print("❌ 没有有效的测试结果")
            return
            
        print(f"\n📊 生成综合分析报告...")
        
        # 分离老模型和新模型的结果
        old_results = [r['old_model'] for r in all_results]
        new_results = [r['new_model'] for r in all_results]
        
        # 计算统计数据
        def calculate_stats(results, metric):
            values = [r['analysis'][metric] for r in results]
            return {
                'mean': statistics.mean(values),
                'median': statistics.median(values),
                'stdev': statistics.stdev(values) if len(values) > 1 else 0,
                'min': min(values),
                'max': max(values)
            }
        
        # 生成报告
        report = {
            'test_summary': {
                'total_tests': len(all_results),
                'test_rounds': max(r['round'] for r in all_results),
                'test_funds': list(set(r['fund_code'] for r in all_results)),
                'timestamp': datetime.now().isoformat()
            },
            'performance_comparison': {
                'duration': {
                    'old_model': calculate_stats(old_results, 'duration'),
                    'new_model': calculate_stats(new_results, 'duration')
                }
            },
            'quality_comparison': {
                'overall_quality': {
                    'old_model': calculate_stats(old_results, 'overall_quality'),
                    'new_model': calculate_stats(new_results, 'overall_quality')
                },
                'length': {
                    'old_model': calculate_stats(old_results, 'length'),
                    'new_model': calculate_stats(new_results, 'length')
                },
                'json_completeness': {
                    'old_model': calculate_stats(old_results, 'json_completeness'),
                    'new_model': calculate_stats(new_results, 'json_completeness')
                },
                'structure_completeness': {
                    'old_model': calculate_stats(old_results, 'structure_completeness'),
                    'new_model': calculate_stats(new_results, 'structure_completeness')
                },
                'emoji_count': {
                    'old_model': calculate_stats(old_results, 'emoji_count'),
                    'new_model': calculate_stats(new_results, 'emoji_count')
                }
            },
            'detailed_results': all_results
        }
        
        # 保存报告
        report_file = self.results_dir / f"comprehensive_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
            
        # 生成可读性报告
        self.generate_readable_report(report)
        
        print(f"✅ 综合报告已保存: {report_file}")
        
    def generate_readable_report(self, report_data):
        """生成可读性报告"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        readable_file = self.results_dir / f"readable_report_{timestamp}.md"
        
        with open(readable_file, 'w', encoding='utf-8') as f:
            f.write("# 新老模型多维度对比测试报告\n\n")
            
            # 测试概览
            summary = report_data['test_summary']
            f.write(f"## 测试概览\n\n")
            f.write(f"- **测试轮数**: {summary['test_rounds']}轮\n")
            f.write(f"- **测试基金**: {len(summary['test_funds'])}只\n")
            f.write(f"- **总测试次数**: {summary['total_tests']}次\n")
            f.write(f"- **测试时间**: {summary['timestamp']}\n\n")
            
            # 性能对比
            perf = report_data['performance_comparison']['duration']
            f.write(f"## 性能对比\n\n")
            f.write(f"| 指标 | 老模型 | 新模型 | 提升 |\n")
            f.write(f"|------|--------|--------|---------|\n")
            f.write(f"| 平均耗时 | {perf['old_model']['mean']:.1f}秒 | {perf['new_model']['mean']:.1f}秒 | {((perf['old_model']['mean'] - perf['new_model']['mean']) / perf['old_model']['mean'] * 100):+.1f}% |\n")
            f.write(f"| 最快耗时 | {perf['old_model']['min']:.1f}秒 | {perf['new_model']['min']:.1f}秒 | - |\n")
            f.write(f"| 最慢耗时 | {perf['old_model']['max']:.1f}秒 | {perf['new_model']['max']:.1f}秒 | - |\n\n")
            
            # 质量对比
            quality = report_data['quality_comparison']
            f.write(f"## 质量对比\n\n")
            f.write(f"### 综合质量评分\n")
            f.write(f"- **老模型平均**: {quality['overall_quality']['old_model']['mean']:.3f}\n")
            f.write(f"- **新模型平均**: {quality['overall_quality']['new_model']['mean']:.3f}\n")
            f.write(f"- **提升幅度**: {((quality['overall_quality']['new_model']['mean'] - quality['overall_quality']['old_model']['mean']) / quality['overall_quality']['old_model']['mean'] * 100):+.1f}%\n\n")
            
            f.write(f"### 详细指标对比\n\n")
            f.write(f"| 维度 | 老模型均值 | 新模型均值 | 提升 |\n")
            f.write(f"|------|------------|------------|------|\n")
            
            for metric, data in quality.items():
                if metric == 'overall_quality':
                    continue
                old_mean = data['old_model']['mean']
                new_mean = data['new_model']['mean']
                improvement = ((new_mean - old_mean) / old_mean * 100) if old_mean > 0 else 0
                f.write(f"| {metric} | {old_mean:.3f} | {new_mean:.3f} | {improvement:+.1f}% |\n")
            
            f.write(f"\n## 结论\n\n")
            
            # 自动生成结论
            overall_improvement = ((quality['overall_quality']['new_model']['mean'] - quality['overall_quality']['old_model']['mean']) / quality['overall_quality']['old_model']['mean'] * 100)
            
            if overall_improvement > 10:
                f.write(f"✅ **新模型显著优于老模型** (提升{overall_improvement:.1f}%)\n\n")
            elif overall_improvement > 0:
                f.write(f"🟡 **新模型略优于老模型** (提升{overall_improvement:.1f}%)\n\n")
            else:
                f.write(f"🔴 **老模型表现更好** (新模型下降{abs(overall_improvement):.1f}%)\n\n")
                
        print(f"✅ 可读性报告已保存: {readable_file}")

def main():
    """主函数"""
    comparator = MultiDimensionalComparator()
    
    # 运行3轮测试
    results = comparator.run_multi_round_test(rounds=3)
    
    print(f"\n🎉 多维度对比测试完成！")
    print(f"📁 结果保存在: {comparator.results_dir}")
    print(f"📊 共完成 {len(results)} 次有效对比")

if __name__ == "__main__":
    main()
