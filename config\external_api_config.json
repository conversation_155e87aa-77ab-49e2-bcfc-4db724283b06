{"external_data": {"enabled": true, "default_source": "wind", "description": "外部数据获取的全局配置，专注于补充港股估值数据"}, "external_data_wind": {"enabled": true, "description": "Wind Python API配置 - 仅获取港股PE和PB估值数据", "connection": {"auto_start": true, "timeout_seconds": 30, "description": "Wind API连接配置，需要Wind终端已登录"}, "fetch_valuation_hk": true, "fetch_valuation_a": false, "fetch_financial_details_hk": false, "fetch_financial_details_a": false, "fetch_real_time_hk": false, "fetch_real_time_a": false, "description_fetch_rules": {"fetch_valuation_hk": "为港股获取PE和PB估值数据（不包括市值）", "fetch_valuation_a": "为A股获取估值数据（本地数据库已有，禁用）", "fetch_financial_details_hk": "为港股获取详细财务数据（暂时禁用）", "fetch_financial_details_a": "为A股获取详细财务数据（本地数据库已有，禁用）", "fetch_real_time_hk": "为港股获取实时行情数据（暂时禁用）", "fetch_real_time_a": "为A股获取实时行情数据（暂时禁用）"}}, "external_data_wind_fields": {"description": "Wind API字段代码映射配置 - 仅包含PE和PB字段", "valuation_hk": ["pe_ttm", "pb_mrq"], "valuation_a": ["pe_ttm", "pb_mrq"], "financial_details_hk": ["S_FA_ROE", "S_FA_ROIC", "S_FA_GROSSMARGIN", "S_FA_NETPROFITMARGIN"], "financial_details_a": ["S_FA_ROE", "S_FA_ROIC", "S_FA_GROSSMARGIN", "S_FA_NETPROFITMARGIN"], "real_time_hk": ["RT_LAST", "RT_CHG_PCT", "RT_VOLUME"], "real_time_a": ["RT_LAST", "RT_CHG_PCT", "RT_VOLUME"], "field_descriptions": {"pe_ttm": "市盈率TTM", "pb_mrq": "市净率MRQ（最近季度）", "S_FA_ROE": "净资产收益率", "S_FA_ROIC": "投入资本回报率", "S_FA_GROSSMARGIN": "毛利率", "S_FA_NETPROFITMARGIN": "净利率", "RT_LAST": "最新价", "RT_CHG_PCT": "涨跌幅%", "RT_VOLUME": "成交量"}, "removed_fields": {"mkt_cap_ard": "总市值字段已移除，避免与本地数据库市值数据冲突", "RT_MKT_CAP": "实时市值字段已移除，使用本地数据库市值数据"}}, "external_data_wind_options": {"description": "Wind API调用选项配置", "valuation_hk_options": "tradeDate=20241231", "valuation_a_options": "tradeDate=20241231", "financial_details_hk_options": "rptType=1;Period=Q;Days=Alldays", "financial_details_a_options": "rptType=1;Period=Q;Days=Alldays", "real_time_hk_options": "unit=1", "real_time_a_options": "unit=1", "options_descriptions": {"unit=1": "数据单位设置", "currency=HKD": "港币计价", "currency=CNY": "人民币计价", "tradeDate=20241231": "交易日期设置（格式：YYYYMMDD）", "rptType=1": "报告类型：合并报表", "Period=Q": "报告期间：季报", "Days=Alldays": "交易日设置：所有日期"}}, "data_quality": {"description": "数据质量控制配置", "max_retry_attempts": 3, "timeout_seconds": 30, "cache_duration_minutes": 60, "validation_rules": {"pe_ttm_range": [-100, 1000], "pb_range": [0, 100], "note": "移除了市值验证规则，因为不再获取市值数据"}}, "logging": {"description": "外部数据获取的日志配置", "log_api_requests": true, "log_api_responses": false, "log_data_transformations": true, "log_cache_operations": false}, "optimization_notes": {"description": "优化说明", "changes": ["移除了mkt_cap_ard字段，避免与本地数据库市值数据冲突", "简化了Wind API调用，只获取PE和PB估值数据", "减少了数据重复和单位混淆问题", "保持了系统的简洁性和可维护性"], "benefits": ["避免市值数据重复获取", "消除单位不一致问题", "减少Wind API调用次数", "提高数据质量和一致性"]}}