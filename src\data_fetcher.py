import logging
import os
import json
from dotenv import load_dotenv
from sqlalchemy import create_engine, text, exc
from typing import Dict, Any, List, Optional

# 导入外部数据提供者
try:
    from .external_data_provider import get_external_stock_details
    EXTERNAL_DATA_AVAILABLE = True
except ImportError:
    try:
        from external_data_provider import get_external_stock_details
        EXTERNAL_DATA_AVAILABLE = True
    except ImportError:
        logger = logging.getLogger(__name__)
        logger.warning("外部数据提供者模块未找到，外部数据功能将被禁用。")
        EXTERNAL_DATA_AVAILABLE = False

# 加载 .env 文件中的环境变量
load_dotenv()
logger = logging.getLogger(__name__)

# 全局变量，用于存储加载的市值配置
MARKET_CAP_CONFIG = None

def _load_market_cap_config() -> dict | None:
    """
    读取项目根目录下的 market_cap_config.json 文件，并返回其解析后的JSON内容。
    """
    global MARKET_CAP_CONFIG
    if MARKET_CAP_CONFIG is not None:
        return MARKET_CAP_CONFIG

    # 获取当前脚本文件所在的目录
    current_script_dir = os.path.dirname(os.path.abspath(__file__))
    # 项目根目录是 src 目录的上一级
    project_root_dir = os.path.dirname(current_script_dir)
    config_file_path = os.path.join(project_root_dir, "config", "market_cap_config.json")

    try:
        with open(config_file_path, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
            MARKET_CAP_CONFIG = config_data # 缓存配置
            logger.info(f"成功从 {config_file_path} 加载市值风格配置。")
            return config_data
    except FileNotFoundError:
        logger.error(f"市值风格配置文件 {config_file_path} 未找到。")
        return None
    except json.JSONDecodeError:
        logger.error(f"解析市值风格配置文件 {config_file_path} 失败。请检查JSON格式。")
        return None
    except Exception as e:
        logger.error(f"加载市值风格配置文件 {config_file_path} 时发生未知错误: {e}", exc_info=True)
        return None

# 在模块加载时调用一次，将配置加载到全局变量中
_load_market_cap_config()


def convert_fund_code_suffix(fund_code: str) -> list[str]:
    """
    基金代码后缀转换函数，用于解决不同数据源中基金代码后缀不一致的问题。

    Args:
        fund_code: 原始基金代码（如 "163302.OF"）

    Returns:
        可能的替代基金代码列表（如 ["163302.SZ", "163302.SH"]）
    """
    if not fund_code or '.' not in fund_code:
        return []

    base_code, suffix = fund_code.rsplit('.', 1)
    suffix = suffix.upper()

    # 转换规则：
    # .OF -> .SZ, .SH (LOF/ETF基金可能在深交所或上交所交易)
    # .SZ -> .OF (深交所基金可能有场外版本)
    # .SH -> .OF (上交所基金可能有场外版本)
    if suffix == 'OF':
        return [f"{base_code}.SZ", f"{base_code}.SH"]
    elif suffix == 'SZ':
        return [f"{base_code}.OF"]
    elif suffix == 'SH':
        return [f"{base_code}.OF"]
    else:
        return []


def _classify_market_cap_style(market_cap: float | None, market_type: str, currency: str = "CNY") -> str:
    """
    根据总市值(万元)、市场类型和货币判断股票的市值风格。
    """
    if MARKET_CAP_CONFIG is None:
        logger.error("市值风格配置未加载，无法进行分类。")
        return "未知风格(配置未加载)"

    if market_cap is None:
        return "未知风格(市值为空)"

    if market_type not in MARKET_CAP_CONFIG.get("market_cap_styles", {}):
        logger.warning(f"未知的市场类型: {market_type}。无法分类。")
        return f"未知风格(市场类型 {market_type} 无配置)"

    config_for_market = MARKET_CAP_CONFIG["market_cap_styles"][market_type]
    thresholds_config = config_for_market.get("thresholds", {})
    labels_config = config_for_market.get("labels", {})
    config_currency = config_for_market.get("currency", "CNY")
    config_unit = config_for_market.get("unit", "万") # 假设配置中的单位总是“万”

    # 确保比较时单位一致，函数入参 market_cap 期望是“万”为单位
    # 如果配置文件的单位不是“万”，这里可能需要转换，但题目要求传入的 market_cap 也是“万”

    # t = thresholds_config, lbl = labels_config
    # 确保比较顺序是从最大风格到最小风格 (Mega, Large, Mid, Small, Micro)

    # 获取阈值和标签，如果特定风格的配置不存在，则使用安全默认值
    t_mega = thresholds_config.get("Mega")
    t_large = thresholds_config.get("Large")
    t_mid = thresholds_config.get("Mid")
    t_small = thresholds_config.get("Small")
    t_micro = thresholds_config.get("Micro") # Micro 通常是最小定义的类别，其阈值通常是0

    lbl_mega = labels_config.get("Mega", "未知Mega")
    lbl_large = labels_config.get("Large", "未知Large")
    lbl_mid = labels_config.get("Mid", "未知Mid")
    lbl_small = labels_config.get("Small", "未知Small")
    lbl_micro = labels_config.get("Micro", "未知Micro")

    final_label_str = f"未知风格(低于Micro下限)" # 默认标签

    # 检查配置完整性，如果关键阈值缺失，记录警告并返回
    if any(th is None for th in [t_mega, t_large, t_mid, t_small, t_micro]):
        logger.warning(f"市场类型 {market_type} 的市值风格阈值配置不完整。Mega: {t_mega}, Large: {t_large}, Mid: {t_mid}, Small: {t_small}, Micro: {t_micro}")
        # 即使配置不完整，也尝试基于已有的配置进行分类
        # 但如果所有阈值都缺失，下面的逻辑会自然回退到默认标签

    # 核心分类逻辑
    if t_mega is not None and market_cap >= t_mega:
        final_label_str = lbl_mega
    elif t_large is not None and market_cap >= t_large: # 隐含 market_cap < t_mega (如果t_mega存在)
        final_label_str = lbl_large
    elif t_mid is not None and market_cap >= t_mid:   # 隐含 market_cap < t_large (如果t_large存在)
        final_label_str = lbl_mid
    elif t_small is not None and market_cap >= t_small: # 隐含 market_cap < t_mid (如果t_mid存在)
        final_label_str = lbl_small
    elif t_micro is not None and market_cap >= t_micro: # 隐含 market_cap < t_small (如果t_small存在)
        final_label_str = lbl_micro
    # else: market_cap < t_micro (或所有相关阈值均为None)，由 final_label_str 的默认值处理

    label = final_label_str # 将最终确定的标签赋值给原变量名，以便后续币种处理逻辑复用


    # 处理币种不匹配的情况
    if currency.upper() != config_currency.upper():
        return f"{label} ({currency.upper()})"
    return label

def _get_quarter_end_date(date_str: str) -> str | None:
   """
   将 "YYYY-MM-DD" 格式的日期字符串转换为该日期所在季度的最后一天，格式仍为 "YYYY-MM-DD"。
   如果输入无效，则返回 None。
   """
   from datetime import datetime
   try:
       dt = datetime.strptime(date_str, "%Y-%m-%d")
       quarter = (dt.month - 1) // 3 + 1
       if quarter == 1:
           return f"{dt.year}-03-31"
       elif quarter == 2:
           return f"{dt.year}-06-30"
       elif quarter == 3:
           return f"{dt.year}-09-30"
       elif quarter == 4:
           return f"{dt.year}-12-31"
       else: # Should not happen
           return None
   except ValueError:
       logger.warning(f"无效的日期格式: {date_str}，无法计算季度末日期。")
       return None

def _preprocess_hk_stock_code(ts_code: str) -> str:
    """
    预处理港股代码，确保数字部分为5位（不足则前补零）。
    非港股代码直接返回。
    """
    if ts_code and isinstance(ts_code, str) and ts_code.endswith(".HK"):
        parts = ts_code.split('.')
        if len(parts) == 2:
            numeric_part = parts[0]
            suffix = parts[1]
            if len(numeric_part) < 5:
                numeric_part = numeric_part.zfill(5) # zfill 会在前补零
            return f"{numeric_part}.{suffix}"
    return ts_code

# 从环境变量获取数据库连接信息
DB_USER = os.getenv("DB_USER")
DB_PASSWORD = os.getenv("DB_PASSWORD")
DB_HOST = os.getenv("DB_HOST", "localhost")
DB_PORT = os.getenv("DB_PORT", "5432")
DB_NAME = os.getenv("DB_NAME")

# 构建数据库连接 URL
DATABASE_URL = None
if DB_USER and DB_PASSWORD and DB_HOST and DB_PORT and DB_NAME:
    DATABASE_URL = f"postgresql+psycopg2://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
else:
    logger.warning("数据库连接信息未在环境变量中完全配置。请检查 .env 文件。")
    # 可以选择在此处引发错误或返回默认值，取决于应用需求
    # raise ValueError("数据库连接信息不完整，请配置 .env 文件。")

# 创建 SQLAlchemy 引擎 (如果 DATABASE_URL 有效)
engine = None
if DATABASE_URL:
    try:
        engine = create_engine(DATABASE_URL)
        logger.info("数据库引擎创建成功。")
    except Exception as e:
        logger.error(f"创建数据库引擎失败: {e}", exc_info=True)
        engine = None # 确保引擎无效

def fetch_fund_data(fund_code: str, report_year: int = None, report_quarter: int = None) -> dict | None:
    """
    从数据库获取指定基金代码的季报数据。

    使用 SQLAlchemy Core 连接数据库并执行查询。

    Args:
        fund_code: 要查询的基金代码 (例如 "005682.OF")。
        report_year: 可选，指定报告年份 (例如 2025)。如果不指定，获取最新数据。
        report_quarter: 可选，指定报告季度 (1-4)。如果不指定，获取最新数据。

    Returns:
        包含 "fund_name", "fund_manager", "market_outlook", "operation_analysis", "report_end_date" 的字典，如果成功。
        如果数据未找到、不完整或发生错误，则返回 None。
    """
    if not engine:
        logger.error("数据库引擎未初始化，无法获取季报数据。")
        return None

    if report_year and report_quarter:
        logger.info(f"开始为基金 {fund_code} 获取 {report_year}年Q{report_quarter} 季报文本数据...")
    else:
        logger.info(f"开始为基金 {fund_code} 获取最新季报文本数据...")

    # 使用 SQLAlchemy text 对象进行参数绑定，防止SQL注入
    # 支持指定季度或获取最新数据
    if report_year and report_quarter:
        # 获取指定季度的数据
        sql_query = text(
            "SELECT fund_name, fund_manager, market_outlook, market_analysis AS operation_analysis, report_year, report_quarter "
            "FROM fund_quarterly_data "
            "WHERE fund_code = :fund_code_param AND report_year = :report_year_param AND report_quarter = :report_quarter_param "
            "LIMIT 1;"
        )
    else:
        # 获取最新数据（按时间排序）
        sql_query = text(
            "SELECT fund_name, fund_manager, market_outlook, market_analysis AS operation_analysis, report_year, report_quarter "
            "FROM fund_quarterly_data "
            "WHERE fund_code = :fund_code_param "
            "ORDER BY report_year DESC, report_quarter DESC "
            "LIMIT 1;"
        )

    conn = None # 初始化连接变量
    try:
        # 从引擎获取连接
        conn = engine.connect()
        logger.debug(f"数据库连接成功 for 获取季报文本 {fund_code}.")

        # 执行查询
        if report_year and report_quarter:
            result = conn.execute(sql_query, {
                "fund_code_param": fund_code,
                "report_year_param": report_year,
                "report_quarter_param": report_quarter
            })
            logger.debug(f"SQL 查询已执行 for 获取季报文本 {fund_code} ({report_year}年Q{report_quarter}).")
        else:
            result = conn.execute(sql_query, {"fund_code_param": fund_code})
            logger.debug(f"SQL 查询已执行 for 获取季报文本 {fund_code} (最新数据).")

        # 获取查询结果 (fetchone 因为我们用了 LIMIT 1)
        fund_data_row = result.fetchone()

        if fund_data_row is None: # 查询成功，但未找到匹配数据
            if report_year and report_quarter:
                logger.info(f"数据库中未找到基金 {fund_code} 在 {report_year}年Q{report_quarter} 的季报文本数据。")
            else:
                logger.info(f"数据库中未找到基金 {fund_code} 的最新季报文本数据。")
            return None # MVP策略：未找到数据则终止

        # 通过索引访问，假设顺序与 SELECT 语句一致
        fund_name = fund_data_row[0]
        fund_manager = fund_data_row[1]
        market_outlook = fund_data_row[2]
        operation_analysis = fund_data_row[3]
        report_year = fund_data_row[4]
        report_quarter = fund_data_row[5]

        # MVP策略：即使字段为空，也记录警告并返回数据
        if market_outlook is None:
            logger.warning(f"基金 {fund_code} 的 'market_outlook' 数据为 NULL。")
        if operation_analysis is None:
            logger.warning(f"基金 {fund_code} 的 'operation_analysis' (market_analysis) 数据为 NULL。")
        if fund_name is None:
             logger.warning(f"基金 {fund_code} 的 'fund_name' 数据为 NULL。")
        if fund_manager is None:
             logger.warning(f"基金 {fund_code} 的 'fund_manager' 数据为 NULL。")
        if report_year is None or report_quarter is None:
            logger.warning(f"基金 {fund_code} 的 'report_year' 或 'report_quarter' 数据为 NULL。无法确定报告期截止日。")
            report_end_date_str = "未知报告日期"
        else:
            # 根据年份和季度计算报告期截止日
            if report_quarter == 1:
                report_end_date_str = f"{report_year}-03-31"
            elif report_quarter == 2:
                report_end_date_str = f"{report_year}-06-30"
            elif report_quarter == 3:
                report_end_date_str = f"{report_year}-09-30"
            elif report_quarter == 4:
                report_end_date_str = f"{report_year}-12-31"
            else:
                logger.warning(f"基金 {fund_code} 的 'report_quarter' ({report_quarter}) 无效。无法确定报告期截止日。")
                report_end_date_str = "未知报告日期"


        logger.info(f"成功获取并解析了基金 {fund_code} 的季报文本数据。报告期: {report_end_date_str}")
        return {
            "fund_name": fund_name if fund_name is not None else "未知基金名称",
            "fund_manager": fund_manager if fund_manager is not None else "未知基金经理",
            "market_outlook": market_outlook if market_outlook is not None else "",
            "operation_analysis": operation_analysis if operation_analysis is not None else "",
            "report_end_date": report_end_date_str
        }

    except exc.SQLAlchemyError as e: # 捕获 SQLAlchemy 相关错误
        logger.error(f"数据库操作失败 (获取季报文本 for 基金: {fund_code}): {e}", exc_info=True)
        return None
    except Exception as e: # 捕获其他意外错误
        logger.error(f"获取基金 {fund_code} 数据时发生意外错误: {e}", exc_info=True)
        return None
    finally:
        # 确保连接在使用后关闭
        if conn:
            try:
                conn.close()
                logger.debug(f"数据库连接已关闭 for {fund_code}.")
            except Exception as e:
                logger.error(f"关闭数据库连接时出错 for {fund_code}: {e}", exc_info=True)


def fetch_fund_portfolio_data(fund_code: str, report_end_date: str = None, report_year: int = None, report_quarter: int = None) -> list[dict] | None:
    """
    从数据库获取指定基金代码和报告期的前十大持仓数据，
    并包含股票名称、申万一级行业、总市值(万元)和市值风格。
    对于港股，会自动获取其中文名称。

    Args:
        fund_code: 要查询的基金代码 (例如 "017749.OF")。
        report_end_date: 报告期截止日期 (格式 "YYYY-MM-DD")。优先使用此参数。
        report_year: 可选，指定报告年份 (例如 2025)。当report_end_date为None时使用。
        report_quarter: 可选，指定报告季度 (1-4)。当report_end_date为None时使用。

    Returns:
        包含前十大持仓股票信息的字典列表，每个字典包含:
        "symbol", "stock_name", "sw_l1_industry", "mkv", "stk_mkv_ratio", "total_mv", "market_cap_style"。
        如果数据未找到或发生错误，则返回 None。
    """
    if not engine:
        logger.error("数据库引擎未初始化，无法获取持仓数据。")
        return None

    # 处理报告期参数：优先使用report_end_date，否则根据年份季度计算
    if report_end_date is None and report_year and report_quarter:
        # 根据年份和季度计算报告期截止日期
        if report_quarter == 1:
            report_end_date = f"{report_year}-03-31"
        elif report_quarter == 2:
            report_end_date = f"{report_year}-06-30"
        elif report_quarter == 3:
            report_end_date = f"{report_year}-09-30"
        elif report_quarter == 4:
            report_end_date = f"{report_year}-12-31"
        else:
            logger.error(f"无效的季度参数: {report_quarter}，必须为1-4之间的整数。")
            return None

    if report_end_date == "未知报告日期" or report_end_date is None:
        logger.warning(f"报告期截止日期未知 for {fund_code}，无法获取持仓数据。")
        return None

    logger.info(f"开始为基金 {fund_code} (报告期: {report_end_date}) 获取前十大持仓数据(含股票名称、行业、市值和风格)...")

    # 将 YYYY-MM-DD 格式的 report_end_date 转换为 YYYYMMDD 格式用于查询
    report_end_date_yyyymmdd = report_end_date.replace("-", "") if report_end_date != "未知报告日期" else None
    # 获取报告期对应的季度末日期，用于港股行业市值查询
    hk_trade_date_condition = _get_quarter_end_date(report_end_date)

    if not report_end_date_yyyymmdd:
        logger.warning(f"无法将报告期截止日 {report_end_date} 转换为 YYYYMMDD 格式 for {fund_code}，无法获取持仓数据。")
        return None

    if not hk_trade_date_condition:
        logger.warning(f"无法为报告期截止日 {report_end_date} 计算季度末日期 for {fund_code}，港股行业市值可能无法准确获取。")
        # 即使无法获取季度末日期，也尝试继续，只是港股部分数据可能不完整

    # SQL 查询不再直接 JOIN 港股行业市值表，将在后续步骤中单独查询
    # V3.1: 添加 a_circ_mv 和 a_turnover_rate 用于规模估算
    sql_query_str = (
        "SELECT "
        "    tfp.symbol, "
        "    COALESCE(tsb.name, thb.name, tfp.symbol) AS stock_name, "
        "    tis.l1_name AS a_sw_l1_industry, "     # A股申万行业
        "    tfp.mkv, "
        "    tfp.stk_mkv_ratio, "
        "    sdb.total_mv AS a_total_mv, "          # A股总市值
        "    sdb.circ_mv AS a_circ_mv, "            # A股流通市值 (Added for V3.1)
        "    sdb.turnover_rate AS a_turnover_rate, " # A股换手率 (Added for V3.1)
        "    sdb.pe AS a_pe, "                      # A股市盈率
        "    sdb.pe_ttm AS a_pe_ttm, "              # A股市盈率TTM
        "    sdb.pb AS a_pb "                       # A股市净率
        # 移除了 hks 相关字段的直接查询
        "FROM "
        "    tushare_fund_portfolio tfp "
        "LEFT JOIN " # A股基本信息
        "    tushare_stock_basic tsb ON tfp.symbol = tsb.ts_code AND tfp.symbol NOT LIKE '%.HK' "
        "LEFT JOIN " # 港股基本信息
        "    tushare_hk_basic thb ON tfp.symbol = thb.ts_code AND tfp.symbol LIKE '%.HK' "
        "LEFT JOIN " # A股申万行业成员
        "    tushare_index_swmember tis ON tfp.symbol = tis.ts_code AND tis.is_new = 'Y' AND tfp.symbol NOT LIKE '%.HK' "
        "LEFT JOIN " # A股每日市值和估值数据
        "    tushare_stock_dailybasic sdb ON tfp.symbol = sdb.ts_code AND tfp.symbol NOT LIKE '%.HK' "
        "    AND sdb.trade_date = ( "
        "        SELECT MAX(sdb_inner.trade_date) "
        "        FROM tushare_stock_dailybasic sdb_inner "
        "        WHERE sdb_inner.ts_code = tfp.symbol "
        "          AND sdb_inner.trade_date <= TO_DATE(:report_end_date_param_yyyymmdd, 'YYYY-MM-DD') "
        "    ) "
        # 移除了对 hk_stock_sw_quarterly 的 LEFT JOIN
        "WHERE "
        "    tfp.ts_code = :fund_code_param AND tfp.end_date = :report_end_date_param_yyyymmdd "
        "ORDER BY "
        "    tfp.stk_mkv_ratio DESC "
        "LIMIT 10;"
    )
    sql_query = text(sql_query_str)

    conn = None
    try:
        conn = engine.connect()
        logger.debug(f"数据库连接成功 for 获取持仓 {fund_code}.")

        result = conn.execute(sql_query, {
            "fund_code_param": fund_code,
            "report_end_date_param_yyyymmdd": report_end_date
            # 使用原始的YYYY-MM-DD格式，因为数据库中end_date是date类型
        })
        logger.debug(f"SQL 查询已执行 for 获取持仓 {fund_code}.")

        portfolio_data_rows = result.fetchall()

        if not portfolio_data_rows:
            logger.info(f"数据库中未找到基金 {fund_code} 在报告期 {report_end_date} 的持仓数据。")

            # 尝试基金代码后缀转换
            alternative_codes = convert_fund_code_suffix(fund_code)
            if alternative_codes:
                logger.info(f"尝试基金代码后缀转换：{fund_code} -> {alternative_codes}")

                for alt_code in alternative_codes:
                    try:
                        alt_result = conn.execute(sql_query, {
                            "fund_code_param": alt_code,
                            "report_end_date_param_yyyymmdd": report_end_date
                        })
                        alt_portfolio_data_rows = alt_result.fetchall()

                        if alt_portfolio_data_rows:
                            logger.info(f"基金代码转换成功：{fund_code} -> {alt_code}，找到 {len(alt_portfolio_data_rows)} 条持仓数据")
                            portfolio_data_rows = alt_portfolio_data_rows
                            break
                    except Exception as e:
                        logger.warning(f"使用转换代码 {alt_code} 查询时发生错误: {e}")
                        continue

            if not portfolio_data_rows:
                logger.info(f"基金代码转换后仍未找到持仓数据，返回None")
                return None

        parsed_portfolio_items = []
        hk_symbols_for_sw_lookup = [] # 用于收集需要查询行业市值的港股代码（原始，不补零）
        hk_symbols_for_name_lookup = [] # 用于收集需要查询名称的港股代码（后续会补零）

        for row_index, row in enumerate(portfolio_data_rows):
            # 根据新的 SELECT 顺序调整索引
            # tfp.symbol (0), stock_name (1), a_sw_l1_industry (2), tfp.mkv (3),
            # tfp.stk_mkv_ratio (4), a_total_mv (5), a_circ_mv (6), a_turnover_rate (7),
            # a_pe (8), a_pe_ttm (9), a_pb (10)
            symbol = row[0]
            stock_name_val = row[1]
            a_sw_l1_industry_val = row[2] if row[2] is not None else "未知行业"
            mkv_val = row[3]
            stk_mkv_ratio_val = row[4]
            a_total_mv_value = row[5] if row[5] is not None else None
            a_circ_mv_value = row[6] if row[6] is not None else None # New
            a_turnover_rate_value = row[7] if row[7] is not None else None # New
            a_pe_value = row[8] if row[8] is not None else None # Shifted index
            a_pe_ttm_value = row[9] if row[9] is not None else None # Shifted index
            a_pb_value = row[10] if row[10] is not None else None # Shifted index

            # 初始化港股相关字段，后续通过二次查询填充
            hk_sw_l1_industry_val = "未知行业"
            hk_total_mv_for_style = None
            hk_market_cap_style_value = "未知风格(港股)"

            current_total_mv_for_style = None
            final_sw_l1_industry = a_sw_l1_industry_val
            market_cap_style_value = "未知风格"

            item_data = {
                "symbol": symbol,
                "stock_name": stock_name_val, # 初始名称，港股可能后续更新
                "sw_l1_industry": final_sw_l1_industry, # A股行业或港股的初始"未知行业"
                "mkv": mkv_val,
                "stk_mkv_ratio": stk_mkv_ratio_val,
                "total_mv": None, # 统一后续填充
                "circ_mv": None, # 新增: 流通市值，统一后续填充
                "turnover_rate": None, # 新增: 换手率，统一后续填充
                "market_cap_style": market_cap_style_value, # A股风格或港股的初始"未知风格"
                "pe": None, # 市盈率，统一后续填充
                "pe_ttm": None, # 市盈率TTM，统一后续填充
                "pb": None # 市净率，统一后续填充
            }

            if symbol and isinstance(symbol, str) and symbol.endswith(".HK"):
                hk_symbols_for_sw_lookup.append(symbol) # 收集原始港股代码用于行业市值查询
                item_data["sw_l1_industry"] = hk_sw_l1_industry_val # 先设为未知，待二次查询填充
                item_data["market_cap_style"] = hk_market_cap_style_value # 先设为未知，待二次查询填充
                # 港股的 circ_mv 和 turnover_rate 默认为 None
                if stock_name_val == symbol: # 如果主查询未能从thb获取名称，则加入名称补查列表
                    hk_symbols_for_name_lookup.append(symbol)
            else:
                # A股逻辑
                item_data["total_mv"] = a_total_mv_value # A股的 total_mv 已经是万元
                item_data["circ_mv"] = a_circ_mv_value # 分配A股流通市值
                item_data["turnover_rate"] = a_turnover_rate_value # 分配A股换手率
                item_data["market_cap_style"] = _classify_market_cap_style(a_total_mv_value, market_type="A-Share", currency="CNY")
                item_data["pe"] = a_pe_value # A股市盈率
                item_data["pe_ttm"] = a_pe_ttm_value # A股市盈率TTM
                item_data["pb"] = a_pb_value # A股市净率

            parsed_portfolio_items.append(item_data)

        # --- 开始二次查询港股行业和市值 ---
        hk_sw_data_map = {} # 键为原始港股代码，值为行业市值等信息
        if hk_symbols_for_sw_lookup and hk_trade_date_condition:
            unique_hk_sw_symbols = sorted(list(set(hk_symbols_for_sw_lookup)))
            # 注意：这里使用原始代码，不进行 _preprocess_hk_stock_code

            hk_sw_lookup_params = {f"code_{i}": code for i, code in enumerate(unique_hk_sw_symbols)}
            hk_sw_placeholders = ", ".join([f":{param_name}" for param_name in hk_sw_lookup_params.keys()])

            if hk_sw_placeholders:
                sql_hk_sw_query_str = (
                    "SELECT ts_code, l1_name, total_mv, mv_unit, mv_currency "
                    "FROM hk_stock_sw_quarterly "
                    "WHERE ts_code IN ({}) AND trade_date = ("
                    "   SELECT MAX(hks_inner.trade_date) "
                    "   FROM hk_stock_sw_quarterly hks_inner "
                    "   WHERE hks_inner.ts_code = hk_stock_sw_quarterly.ts_code " # 确保关联正确
                    "     AND hks_inner.trade_date <= :hk_trade_date_param"
                    ")"
                ).format(hk_sw_placeholders) # 使用 format 插入占位符列表

                sql_hk_sw_query = text(sql_hk_sw_query_str)

                # 合并参数
                final_hk_sw_params = hk_sw_lookup_params.copy()
                final_hk_sw_params["hk_trade_date_param"] = hk_trade_date_condition

                try:
                    with engine.connect() as secondary_conn:
                        hk_sw_result = secondary_conn.execute(sql_hk_sw_query, final_hk_sw_params)
                        for r_code, r_l1, r_mv, r_unit, r_curr in hk_sw_result.fetchall():
                            hk_sw_data_map[r_code] = {
                                "l1_name": r_l1,
                                "total_mv_raw": r_mv,
                                "mv_unit": r_unit,
                                "mv_currency": r_curr
                            }
                        # secondary_conn.commit() # SELECT-only, no commit needed
                    logger.info(f"成功为 {len(hk_sw_data_map)} 个港股代码补查到行业和市值数据。")
                except exc.SQLAlchemyError as e_hk_sw:
                    logger.error(f"批量补查港股行业市值时发生数据库错误: {e_hk_sw}", exc_info=True)
                except Exception as e_hk_sw_generic:
                    logger.error(f"批量补查港股行业市值时发生意外错误: {e_hk_sw_generic}", exc_info=True)
        elif not hk_trade_date_condition and hk_symbols_for_sw_lookup:
             logger.warning(f"无法为报告期 {report_end_date} 计算季度末日期，跳过港股行业市值二次查询。")


        # --- 开始二次查询港股名称 (逻辑基本不变，但从 hk_symbols_for_name_lookup 获取列表) ---
        hk_names_map = {}
        if hk_symbols_for_name_lookup: # 使用新的列表
            unique_hk_name_symbols = sorted(list(set(hk_symbols_for_name_lookup)))
            # 查询港股名称时，需要对原始代码进行补零处理
            processed_hk_codes_for_name_lookup = [_preprocess_hk_stock_code(s) for s in unique_hk_name_symbols]

            valid_processed_name_codes = [code for code in processed_hk_codes_for_name_lookup if code]

            if valid_processed_name_codes:
                hk_name_lookup_params = {f"code_{i}": code for i, code in enumerate(valid_processed_name_codes)}
                hk_name_placeholders = ", ".join([f":{param_name}" for param_name in hk_name_lookup_params.keys()])

                if hk_name_placeholders:
                    sql_hk_names_query = text(
                        f"SELECT ts_code, name FROM tushare_hk_basic WHERE ts_code IN ({hk_name_placeholders})"
                    )
                    try:
                        with engine.connect() as secondary_conn:
                            hk_names_result = secondary_conn.execute(sql_hk_names_query, hk_name_lookup_params)
                            for r_code, r_name in hk_names_result.fetchall():
                                hk_names_map[r_code] = r_name # 键是补零后的代码
                            # secondary_conn.commit() # SELECT-only
                        logger.info(f"成功为 {len(hk_names_map)} 个港股代码补查到名称。")
                    except exc.SQLAlchemyError as e_hk_name:
                        logger.error(f"批量补查港股名称时发生数据库错误: {e_hk_name}", exc_info=True)
                    except Exception as e_hk_name_generic:
                        logger.error(f"批量补查港股名称时发生意外错误: {e_hk_name_generic}", exc_info=True)

        # --- 合并数据到 final_portfolio_list ---
        final_portfolio_list = []
        for item in parsed_portfolio_items:
            symbol = item["symbol"]

            if symbol and isinstance(symbol, str) and symbol.endswith(".HK"):
                # 更新港股行业和市值
                if symbol in hk_sw_data_map: # 使用原始 symbol 查 map
                    hk_data = hk_sw_data_map[symbol]
                    item["sw_l1_industry"] = hk_data.get("l1_name") if hk_data.get("l1_name") else "未知行业"

                    hk_total_mv_raw = hk_data.get("total_mv_raw")
                    hk_mv_unit_val = hk_data.get("mv_unit")
                    hk_mv_currency_val = hk_data.get("mv_currency")
                    current_hk_total_mv_for_style = None

                    if hk_total_mv_raw is not None and hk_mv_unit_val:
                        try:
                            hk_total_mv_numeric = float(hk_total_mv_raw)
                            if hk_mv_unit_val == "元":
                                current_hk_total_mv_for_style = hk_total_mv_numeric / 10000
                            elif hk_mv_unit_val == "万":
                                current_hk_total_mv_for_style = hk_total_mv_numeric
                            elif hk_mv_unit_val == "亿":
                                current_hk_total_mv_for_style = hk_total_mv_numeric * 10000
                            elif hk_mv_unit_val == "亿元": # 新增处理“亿元”的条件
                                current_hk_total_mv_for_style = hk_total_mv_numeric * 10000
                            else:
                                logger.warning(f"港股 {symbol} 未知的市值单位: {hk_mv_unit_val}。无法计算市值风格。")
                        except ValueError:
                            logger.warning(f"港股 {symbol} 的总市值 {hk_total_mv_raw} 无法转换为数字。")

                    item["total_mv"] = current_hk_total_mv_for_style
                    if current_hk_total_mv_for_style is not None:
                        # 调用新的分类函数，传入市场类型和实际币种
                        item["market_cap_style"] = _classify_market_cap_style(
                            current_hk_total_mv_for_style,
                            market_type="HK-Share",
                            currency=hk_mv_currency_val if hk_mv_currency_val else "HKD" # 默认HKD
                        )
                        # 注意：新的 _classify_market_cap_style 内部会处理币种后缀
                    else:
                        item["market_cap_style"] = "未知风格(港股)" # 如果二次查询后仍无市值
                else:
                    # 如果二次查询未查到该港股的行业市值数据
                    item["sw_l1_industry"] = "未知行业"
                    item["total_mv"] = None
                    item["market_cap_style"] = "未知风格(港股)"
                    logger.debug(f"港股 {symbol} 在二次查询中未找到行业市值数据。")

                # 更新港股名称 (如果需要)
                if symbol in hk_symbols_for_name_lookup: # 检查原始symbol是否在待查列表
                    processed_symbol_for_name = _preprocess_hk_stock_code(symbol) # 名称查询用补零代码
                    if processed_symbol_for_name in hk_names_map:
                        item["stock_name"] = hk_names_map[processed_symbol_for_name]
                    else:
                        logger.warning(f"港股 {symbol} (处理后: {processed_symbol_for_name}) 在二次查询中仍未找到名称。")

                # --- 尝试从外部API获取补充数据 ---
                if EXTERNAL_DATA_AVAILABLE:
                    try:
                        external_data = get_external_stock_details(symbol)
                        if external_data:
                            logger.debug(f"为港股 {symbol} 获取到外部补充数据: {list(external_data.keys())}")

                            # 合并外部估值数据，仅使用PE和PB数据
                            if "api_pe_ttm" in external_data and external_data["api_pe_ttm"] is not None:
                                item["pe_ttm"] = external_data["api_pe_ttm"]
                            if "api_pb" in external_data and external_data["api_pb"] is not None:
                                item["pb"] = external_data["api_pb"]
                            # 移除了市值数据的合并，避免与本地数据库数据冲突和单位混淆

                            # 添加数据源标记
                            item["external_data_source"] = external_data.get("data_source", "external_api")
                            item["external_data_timestamp"] = external_data.get("fetch_timestamp")
                        else:
                            logger.debug(f"港股 {symbol} 未获取到外部补充数据。")
                    except Exception as e:
                        logger.warning(f"为港股 {symbol} 获取外部数据时发生错误: {e}")
                        # 外部数据获取失败不影响主流程
                        pass

            final_portfolio_list.append(item)

        logger.info(f"成功获取基金 {fund_code} (报告期: {report_end_date}) 的 {len(final_portfolio_list)} 条持仓数据（含二次查询修正）。")
        return final_portfolio_list

    except exc.SQLAlchemyError as e:
        logger.error(f"数据库操作失败 (获取持仓 for 基金: {fund_code}, 报告期: {report_end_date}): {e}", exc_info=True)
        return None
    except Exception as e:
        logger.error(f"获取基金 {fund_code} (报告期: {report_end_date}) 持仓数据时发生意外错误: {e}", exc_info=True)
        return None
    finally:
        if conn:
            try:
                conn.close()
                logger.debug(f"数据库连接已关闭 for 获取持仓 {fund_code}.")
            except Exception as e:
                logger.error(f"关闭数据库连接时出错 for 获取持仓 {fund_code}: {e}", exc_info=True)

def fetch_batch_fund_data(fund_codes: list[str], report_year: int = None, report_quarter: int = None) -> dict[str, dict]:
    """
    为基金代码列表中的每个基金批量获取季报文本和持仓数据。

    Args:
        fund_codes: 包含基金代码字符串的列表。
        report_year: 可选，指定报告年份 (例如 2025)。如果不指定，获取最新数据。
        report_quarter: 可选，指定报告季度 (1-4)。如果不指定，获取最新数据。

    Returns:
        一个字典，键是基金代码，值是包含 'quarterly_data' 和 'portfolio_data' 的字典。
        如果某个基金的数据获取失败，其对应的值中相关数据将为 None。
    """
    batch_results = {}
    if not engine:
        logger.error("数据库引擎未初始化，无法执行批量数据获取。")
        # 为所有基金代码标记为失败
        for code in fund_codes:
            batch_results[code] = {"quarterly_data": None, "portfolio_data": None, "error": "Database engine not initialized"}
        return batch_results

    logger.info(f"开始为 {len(fund_codes)} 个基金批量获取数据...")

    for code in fund_codes:
        logger.info(f"--- 批量处理基金: {code} ---")
        quarterly_data = None
        portfolio_data = None
        error_message = None
        try:
            quarterly_data = fetch_fund_data(code, report_year, report_quarter)
            if quarterly_data and quarterly_data.get("report_end_date") != "未知报告日期":
                portfolio_data = fetch_fund_portfolio_data(code, quarterly_data["report_end_date"])
            elif quarterly_data:
                logger.warning(f"基金 {code} 的季报数据中报告期未知，无法获取持仓。")
                error_message = "报告期未知，无法获取持仓"
            else:
                if report_year and report_quarter:
                    logger.warning(f"未能获取基金 {code} 在 {report_year}年Q{report_quarter} 的季报数据。")
                    error_message = f"未能获取 {report_year}年Q{report_quarter} 季报数据"
                else:
                    logger.warning(f"未能获取基金 {code} 的最新季报数据。")
                    error_message = "未能获取最新季报数据"

        except Exception as e:
            logger.error(f"处理基金 {code} 时发生意外错误: {e}", exc_info=True)
            error_message = f"处理时发生意外错误: {str(e)}"

        batch_results[code] = {
            "quarterly_data": quarterly_data,
            "portfolio_data": portfolio_data,
            "error": error_message
        }
        if error_message:
            logger.info(f"基金 {code} 数据获取完成，但存在错误: {error_message}")
        else:
            logger.info(f"基金 {code} 数据获取成功。")

    logger.info(f"批量数据获取完成。成功处理 {sum(1 for res in batch_results.values() if not res['error'])} 个基金，失败 {sum(1 for res in batch_results.values() if res['error'])} 个。")
    return batch_results

def fetch_sw_industry_mapping() -> dict[str, dict] | None:
    """
    从数据库获取申万行业分类映射，包含1,2,3级行业信息。

    Returns:
        包含申万行业分类映射的字典，键为股票代码，值为包含l1_code, l1_name, l2_code, l2_name, l3_code, l3_name的字典。
        如果获取失败，则返回 None。
    """
    if not engine:
        logger.error("数据库引擎未初始化，无法获取申万行业分类映射。")
        return None

    logger.info("开始获取申万行业分类映射...")

    # 获取最新的申万行业分类数据
    sql_query = text("""
        SELECT DISTINCT ts_code, l1_code, l1_name, l2_code, l2_name, l3_code, l3_name
        FROM tushare_index_swmember
        WHERE l1_code IS NOT NULL AND is_new = 'Y'
        ORDER BY ts_code
    """)

    conn = None
    try:
        conn = engine.connect()
        logger.debug("数据库连接成功 for 获取申万行业分类映射.")

        result = conn.execute(sql_query)
        logger.debug("SQL 查询已执行 for 获取申万行业分类映射.")

        sw_mapping = {}
        row_count = 0

        for row in result.fetchall():
            ts_code = row[0]
            l1_code = row[1]
            l1_name = row[2]
            l2_code = row[3]
            l2_name = row[4]
            l3_code = row[5]
            l3_name = row[6]

            sw_mapping[ts_code] = {
                "l1_code": l1_code,
                "l1_name": l1_name,
                "l2_code": l2_code,
                "l2_name": l2_name,
                "l3_code": l3_code,
                "l3_name": l3_name
            }
            row_count += 1

        logger.info(f"成功获取 {row_count} 条申万行业分类映射数据。")
        return sw_mapping

    except exc.SQLAlchemyError as e:
        logger.error(f"数据库操作失败 (获取申万行业分类映射): {e}", exc_info=True)
        return None
    except Exception as e:
        logger.error(f"获取申万行业分类映射时发生意外错误: {e}", exc_info=True)
        return None
    finally:
        if conn:
            try:
                conn.close()
                logger.debug("数据库连接已关闭 for 获取申万行业分类映射.")
            except Exception as e:
                logger.error(f"关闭数据库连接时出错 for 获取申万行业分类映射: {e}", exc_info=True)

def _get_quarter_end_date(report_end_date: str) -> str | None:
    """
    根据报告期截止日期计算对应的季度末日期。

    Args:
        report_end_date: 报告期截止日期 (格式 "YYYY-MM-DD")

    Returns:
        季度末日期字符串 (格式 "YYYYMMDD")，如果计算失败则返回 None。
    """
    try:
        from datetime import datetime

        # 解析报告期截止日期
        date_obj = datetime.strptime(report_end_date, "%Y-%m-%d")
        year = date_obj.year
        month = date_obj.month

        # 确定季度末月份
        if month <= 3:
            quarter_end_month = 3
        elif month <= 6:
            quarter_end_month = 6
        elif month <= 9:
            quarter_end_month = 9
        else:
            quarter_end_month = 12

        # 构造季度末日期
        if quarter_end_month == 3:
            quarter_end_date = f"{year}0331"
        elif quarter_end_month == 6:
            quarter_end_date = f"{year}0630"
        elif quarter_end_month == 9:
            quarter_end_date = f"{year}0930"
        else:
            quarter_end_date = f"{year}1231"

        return quarter_end_date

    except Exception as e:
        logger.error(f"计算季度末日期时发生错误: {e}")
        return None

def get_fund_hk_sw_industry_info(fund_code: str, report_end_date: str) -> dict | None:
    """
    获取基金持仓港股的申万行业分类信息。

    Args:
        fund_code: 基金代码
        report_end_date: 报告期截止日期 (格式 "YYYY-MM-DD")

    Returns:
        包含基金持仓港股申万行业分类信息的字典，如果获取失败则返回 None。
    """
    if not engine:
        logger.error("数据库引擎未初始化，无法获取基金港股申万行业信息。")
        return None

    if report_end_date == "未知报告日期":
        logger.warning(f"报告期截止日期未知 for {fund_code}，无法获取港股申万行业信息。")
        return None

    logger.info(f"开始为基金 {fund_code} (报告期: {report_end_date}) 获取港股申万行业分类信息...")

    # 保持 YYYY-MM-DD 格式，因为数据库中的日期字段是date类型
    report_end_date_formatted = report_end_date
    # 获取报告期对应的季度末日期
    hk_trade_date_condition = _get_quarter_end_date(report_end_date)

    if not hk_trade_date_condition:
        logger.warning(f"无法为报告期截止日 {report_end_date} 计算季度末日期 for {fund_code}，无法获取港股申万行业信息。")
        return None

    sql_query = text("""
        SELECT
            tfp.symbol,
            hks.name as stock_name,
            hks.l1_name,
            hks.l2_name,
            hks.l3_name,
            tfp.stk_mkv_ratio
        FROM tushare_fund_portfolio tfp
        LEFT JOIN hk_stock_sw_quarterly hks ON tfp.symbol = hks.ts_code
        AND hks.trade_date = (
            SELECT MAX(hks_inner.trade_date)
            FROM hk_stock_sw_quarterly hks_inner
            WHERE hks_inner.ts_code = tfp.symbol
            AND hks_inner.trade_date <= :hk_trade_date_param
        )
        WHERE tfp.ts_code = :fund_code_param
        AND tfp.end_date = :report_end_date_param
        AND tfp.symbol LIKE '%.HK'
        ORDER BY tfp.stk_mkv_ratio DESC
        LIMIT 10
    """)

    conn = None
    try:
        conn = engine.connect()
        logger.debug(f"数据库连接成功 for 获取基金港股申万行业信息 {fund_code}.")

        result = conn.execute(sql_query, {
            "fund_code_param": fund_code,
            "report_end_date_param": report_end_date_formatted,
            "hk_trade_date_param": hk_trade_date_condition
        })
        logger.debug(f"SQL 查询已执行 for 获取基金港股申万行业信息 {fund_code}.")

        holdings_hk_sw_info = []

        for row in result.fetchall():
            symbol = row[0]
            stock_name = row[1] if row[1] else "未知名称"
            l1_name = row[2] if row[2] else "未知一级行业"
            l2_name = row[3] if row[3] else "未知二级行业"
            l3_name = row[4] if row[4] else "未知三级行业"
            stk_mkv_ratio = row[5]

            holdings_hk_sw_info.append({
                "symbol": symbol,
                "stock_name": stock_name,
                "l1_name": l1_name,
                "l2_name": l2_name,
                "l3_name": l3_name,
                "stk_mkv_ratio": stk_mkv_ratio
            })

        if not holdings_hk_sw_info:
            logger.info(f"基金 {fund_code} 在报告期 {report_end_date} 未找到港股申万行业分类信息。")
            return None

        logger.info(f"成功获取基金 {fund_code} 的 {len(holdings_hk_sw_info)} 条港股申万行业分类信息。")
        return {
            "fund_code": fund_code,
            "report_end_date": report_end_date,
            "holdings_hk_sw_info": holdings_hk_sw_info
        }

    except exc.SQLAlchemyError as e:
        logger.error(f"数据库操作失败 (获取基金港股申万行业信息 for {fund_code}): {e}", exc_info=True)
        return None
    except Exception as e:
        logger.error(f"获取基金 {fund_code} 港股申万行业信息时发生意外错误: {e}", exc_info=True)
        return None
    finally:
        if conn:
            try:
                conn.close()
                logger.debug(f"数据库连接已关闭 for 获取基金港股申万行业信息 {fund_code}.")
            except Exception as e:
                logger.error(f"关闭数据库连接时出错 for 获取基金港股申万行业信息 {fund_code}: {e}", exc_info=True)

def get_fund_sw_industry_info(fund_code: str, report_end_date: str) -> dict | None:
    """
    获取基金持仓股票的申万行业分类信息。

    Args:
        fund_code: 基金代码
        report_end_date: 报告期截止日期 (格式 "YYYY-MM-DD")

    Returns:
        包含基金持仓股票申万行业分类信息的字典，如果获取失败则返回 None。
    """
    if not engine:
        logger.error("数据库引擎未初始化，无法获取基金申万行业信息。")
        return None

    if report_end_date == "未知报告日期":
        logger.warning(f"报告期截止日期未知 for {fund_code}，无法获取申万行业信息。")
        return None

    logger.info(f"开始为基金 {fund_code} (报告期: {report_end_date}) 获取申万行业分类信息...")

    # 保持 YYYY-MM-DD 格式，因为数据库中的日期字段是date类型
    report_end_date_formatted = report_end_date

    # 获取报告期对应的季度末日期，用于港股行业查询
    hk_trade_date_condition = _get_quarter_end_date(report_end_date)

    # 先获取A股的申万行业分类
    sql_query_a_stock = text("""
        SELECT
            tfp.symbol,
            tsb.name as stock_name,
            sim.l1_code,
            sim.l1_name,
            sim.l2_code,
            sim.l2_name,
            sim.l3_code,
            sim.l3_name,
            tfp.stk_mkv_ratio
        FROM tushare_fund_portfolio tfp
        LEFT JOIN tushare_stock_basic tsb ON tfp.symbol = tsb.ts_code
        LEFT JOIN tushare_index_swmember sim ON tfp.symbol = sim.ts_code AND sim.is_new = 'Y'
        WHERE tfp.ts_code = :fund_code_param
        AND tfp.end_date = :report_end_date_param
        AND tfp.symbol NOT LIKE '%.HK'
        ORDER BY tfp.stk_mkv_ratio DESC
        LIMIT 10
    """)

    conn = None
    try:
        conn = engine.connect()
        logger.debug(f"数据库连接成功 for 获取基金申万行业信息 {fund_code}.")

        # 执行A股查询
        result_a_stock = conn.execute(sql_query_a_stock, {
            "fund_code_param": fund_code,
            "report_end_date_param": report_end_date_formatted
        })
        logger.debug(f"A股申万行业SQL查询已执行 for {fund_code}.")

        holdings_sw_info = []

        # 处理A股数据
        for row in result_a_stock.fetchall():
            symbol = row[0]
            stock_name = row[1] if row[1] else "未知名称"
            l1_code = row[2]
            l1_name = row[3] if row[3] else "未知一级行业"
            l2_code = row[4]
            l2_name = row[5] if row[5] else "未知二级行业"
            l3_code = row[6]
            l3_name = row[7] if row[7] else "未知三级行业"
            stk_mkv_ratio = row[8]

            holdings_sw_info.append({
                "symbol": symbol,
                "stock_name": stock_name,
                "l1_code": l1_code,
                "l1_name": l1_name,
                "l2_code": l2_code,
                "l2_name": l2_name,
                "l3_code": l3_code,
                "l3_name": l3_name,
                "stk_mkv_ratio": stk_mkv_ratio
            })

        # 获取港股申万行业分类
        if hk_trade_date_condition:
            sql_query_hk_stock = text("""
                SELECT
                    tfp.symbol,
                    COALESCE(thb.name, tfp.symbol) as stock_name,
                    NULL as l1_code,
                    hks.l1_name,
                    NULL as l2_code,
                    hks.l2_name,
                    NULL as l3_code,
                    hks.l3_name,
                    tfp.stk_mkv_ratio
                FROM tushare_fund_portfolio tfp
                LEFT JOIN tushare_hk_basic thb ON tfp.symbol = thb.ts_code
                LEFT JOIN hk_stock_sw_quarterly hks ON tfp.symbol = hks.ts_code
                    AND hks.trade_date = (
                        SELECT MAX(hks_inner.trade_date)
                        FROM hk_stock_sw_quarterly hks_inner
                        WHERE hks_inner.ts_code = tfp.symbol
                        AND hks_inner.trade_date <= :hk_trade_date_param
                    )
                WHERE tfp.ts_code = :fund_code_param
                AND tfp.end_date = :report_end_date_param
                AND tfp.symbol LIKE '%.HK'
                ORDER BY tfp.stk_mkv_ratio DESC
                LIMIT 10
            """)

            result_hk_stock = conn.execute(sql_query_hk_stock, {
                "fund_code_param": fund_code,
                "report_end_date_param": report_end_date_formatted,
                "hk_trade_date_param": hk_trade_date_condition
            })
            logger.debug(f"港股申万行业SQL查询已执行 for {fund_code}.")

            # 处理港股数据
            for row in result_hk_stock.fetchall():
                symbol = row[0]
                stock_name = row[1] if row[1] else "未知名称"
                l1_code = row[2]
                l1_name = row[3] if row[3] else "未知一级行业"
                l2_code = row[4]
                l2_name = row[5] if row[5] else "未知二级行业"
                l3_code = row[6]
                l3_name = row[7] if row[7] else "未知三级行业"
                stk_mkv_ratio = row[8]

                holdings_sw_info.append({
                    "symbol": symbol,
                    "stock_name": stock_name,
                    "l1_code": l1_code,
                    "l1_name": l1_name,
                    "l2_code": l2_code,
                    "l2_name": l2_name,
                    "l3_code": l3_code,
                    "l3_name": l3_name,
                    "stk_mkv_ratio": stk_mkv_ratio
                })

        # 按持仓比例排序
        holdings_sw_info.sort(key=lambda x: x.get('stk_mkv_ratio', 0), reverse=True)

        if not holdings_sw_info:
            logger.info(f"基金 {fund_code} 在报告期 {report_end_date} 未找到申万行业分类信息。")
            return None

        logger.info(f"成功获取基金 {fund_code} 的 {len(holdings_sw_info)} 条申万行业分类信息（包含A股和港股）。")
        return {
            "fund_code": fund_code,
            "report_end_date": report_end_date,
            "holdings_sw_info": holdings_sw_info
        }

    except exc.SQLAlchemyError as e:
        logger.error(f"数据库操作失败 (获取基金申万行业信息 for {fund_code}): {e}", exc_info=True)
        return None
    except Exception as e:
        logger.error(f"获取基金 {fund_code} 申万行业信息时发生意外错误: {e}", exc_info=True)
        return None
    finally:
        if conn:
            try:
                conn.close()
                logger.debug(f"数据库连接已关闭 for 获取基金申万行业信息 {fund_code}.")
            except Exception as e:
                logger.error(f"关闭数据库连接时出错 for 获取基金申万行业信息 {fund_code}: {e}", exc_info=True)

def get_fund_valuation_summary(fund_code: str, report_end_date: str, portfolio_data: list = None) -> dict | None:
    """
    获取基金持仓股票的估值统计摘要，包含A股和港股数据。
    港股估值数据通过external_data_provider获取。

    Args:
        fund_code: 基金代码
        report_end_date: 报告期截止日期 (格式 "YYYY-MM-DD")
        portfolio_data: 可选，已获取的持仓数据。如果提供则直接使用，否则重新获取

    Returns:
        包含估值统计信息的字典，如果获取失败则返回 None。
    """
    if not engine:
        logger.error("数据库引擎未初始化，无法获取基金估值统计。")
        return None

    if report_end_date == "未知报告日期":
        logger.warning(f"报告期截止日期未知 for {fund_code}，无法获取估值统计。")
        return None

    # 检查是否提供了缓存的持仓数据
    if portfolio_data is None:
        logger.info(f"开始为基金 {fund_code} (报告期: {report_end_date}) 获取估值统计（包含港股数据）...")
        # 只有在没有提供数据时才重新获取
        portfolio_data = fetch_fund_portfolio_data(fund_code, report_end_date)
        if not portfolio_data:
            logger.warning(f"无法获取基金 {fund_code} 的持仓数据，无法计算估值统计。")
            return None
    else:
        logger.info(f"使用缓存数据为基金 {fund_code} (报告期: {report_end_date}) 计算估值统计...")
        logger.debug(f"缓存数据包含 {len(portfolio_data)} 只持仓股票")

    # 从持仓数据中提取估值信息
    pe_values = []
    pe_ttm_values = []
    pb_values = []
    total_weight = 0
    pe_weight_sum = 0
    pe_ttm_weight_sum = 0
    pb_weight_sum = 0

    for item in portfolio_data:
        weight = item.get('stk_mkv_ratio', 0)
        total_weight += weight

        pe = item.get('pe')
        pe_ttm = item.get('pe_ttm')
        pb = item.get('pb')

        if pe is not None:
            pe_values.append(float(pe))
            pe_weight_sum += weight

        if pe_ttm is not None:
            pe_ttm_values.append(float(pe_ttm))
            pe_ttm_weight_sum += weight

        if pb is not None:
            pb_values.append(float(pb))
            pb_weight_sum += weight

    # 计算统计指标
    total_stocks = len(portfolio_data)

    # 平均值
    avg_pe = sum(pe_values) / len(pe_values) if pe_values else None
    avg_pe_ttm = sum(pe_ttm_values) / len(pe_ttm_values) if pe_ttm_values else None
    avg_pb = sum(pb_values) / len(pb_values) if pb_values else None

    # 中位数
    def calculate_median(values):
        if not values:
            return None
        sorted_values = sorted(values)
        n = len(sorted_values)
        if n % 2 == 0:
            return (sorted_values[n//2 - 1] + sorted_values[n//2]) / 2
        else:
            return sorted_values[n//2]

    median_pe = calculate_median(pe_values)
    median_pe_ttm = calculate_median(pe_ttm_values)
    median_pb = calculate_median(pb_values)

    # 覆盖率（基于权重）
    pe_coverage_ratio = pe_weight_sum if total_weight > 0 else 0
    pe_ttm_coverage_ratio = pe_ttm_weight_sum if total_weight > 0 else 0
    pb_coverage_ratio = pb_weight_sum if total_weight > 0 else 0

    valuation_summary = {
        "fund_code": fund_code,
        "report_end_date": report_end_date,
        "total_stocks": total_stocks,
        "avg_pe": round(float(avg_pe), 2) if avg_pe is not None else None,
        "avg_pe_ttm": round(float(avg_pe_ttm), 2) if avg_pe_ttm is not None else None,
        "avg_pb": round(float(avg_pb), 2) if avg_pb is not None else None,
        "median_pe": round(float(median_pe), 2) if median_pe is not None else None,
        "median_pe_ttm": round(float(median_pe_ttm), 2) if median_pe_ttm is not None else None,
        "median_pb": round(float(median_pb), 2) if median_pb is not None else None,
        "pe_coverage_ratio": round(float(pe_coverage_ratio), 2),
        "pe_ttm_coverage_ratio": round(float(pe_ttm_coverage_ratio), 2),
        "pb_coverage_ratio": round(float(pb_coverage_ratio), 2)
    }

    logger.info(f"成功获取基金 {fund_code} 的估值统计数据（包含 {total_stocks} 只股票）。")
    return valuation_summary

def read_fund_list_from_file(file_path: str = "data/fund_list.txt") -> list[str] | None:
    """
    从指定文件读取基金代码列表。

    Args:
        file_path: 包含基金代码的文本文件路径，每行一个基金代码。

    Returns:
        包含基金代码的列表，如果成功。
        如果文件未找到或发生其他错误，则返回 None。
    """
    logger.info(f"尝试从文件 {file_path} 读取基金列表...")
    fund_codes = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                code = line.strip()
                if code: # 确保不是空行
                    fund_codes.append(code)
        if not fund_codes:
            logger.warning(f"文件 {file_path} 为空或不包含任何有效的基金代码。")
            return None # 或者返回空列表，取决于后续逻辑
        logger.info(f"成功从 {file_path} 读取 {len(fund_codes)} 个基金代码。")
        return fund_codes
    except FileNotFoundError:
        logger.error(f"基金列表文件 {file_path} 未找到。")
        return None
    except Exception as e:
        logger.error(f"读取基金列表文件 {file_path} 时发生错误: {e}", exc_info=True)
        return None

if __name__ == '__main__':
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(levelname)s - [%(name)s.%(funcName)s:%(lineno)d] - %(message)s',
        handlers=[logging.StreamHandler()]
    )

    if not DATABASE_URL or not engine:
        logger.critical("数据库未配置或引擎初始化失败，无法执行测试。请检查 .env 文件和错误日志。")
    else:
        # 从文件读取基金代码列表进行测试
        test_fund_codes = read_fund_list_from_file() # 默认使用 "data/fund_list.txt"

        if not test_fund_codes:
            logger.error("未能从文件读取基金代码列表，或列表为空。测试终止。")
        else:
            logger.info(f"将使用以下基金代码进行批量获取测试: {test_fund_codes}")
            batch_data_results = fetch_batch_fund_data(test_fund_codes)

            logger.info("\n--- 批量获取结果总结 ---")
            for fund_code, data in batch_data_results.items():
                if data.get("error"):
                    logger.error(f"基金 {fund_code}: 获取失败 - {data['error']}")
                else:
                    logger.info(f"基金 {fund_code}: 获取成功")
                    q_data = data.get("quarterly_data")
                    p_data = data.get("portfolio_data")
                    if q_data:
                        logger.info(f"  季报: name='{q_data.get('fund_name')}', manager='{q_data.get('fund_manager')}', report_date='{q_data.get('report_end_date')}'")
                    if p_data:
                        logger.info(f"  持仓 ({len(p_data)} 条):")
                        for item in p_data[:2]: # 最多显示2条持仓以保持日志简洁
                            logger.info(f"    Symbol: {item.get('symbol')}, Name: {item.get('stock_name')}, Industry: {item.get('sw_l1_industry')}, MKV: {item.get('mkv')}, Ratio: {item.get('stk_mkv_ratio')}, TotalMV: {item.get('total_mv')}万, Style: {item.get('market_cap_style')}")
                        if len(p_data) > 2:
                            logger.info("    ...")
                    elif q_data and q_data.get("report_end_date") != "未知报告日期":
                         logger.info(f"  持仓: 未获取到数据。")

            logger.info("\n--- 市值风格分类边界测试 ---")
            # A-Share Tests
            test_cases_a_share = [
                (25000, "A-Share", "CNY", "超大盘"),      # 高于 Mega
                (20000, "A-Share", "CNY", "超大盘"),      # 等于 Mega
                (10000, "A-Share", "CNY", "大盘"),       # Mega 和 Large 之间
                (5000, "A-Share", "CNY", "大盘"),        # 等于 Large
                (4999.99, "A-Share", "CNY", "中盘"),     # 略低于 Large
                (3000, "A-Share", "CNY", "中盘"),        # Large 和 Mid 之间
                (2000, "A-Share", "CNY", "中盘"),        # 等于 Mid
                (1999.99, "A-Share", "CNY", "小盘"),     # 略低于 Mid
                (1000, "A-Share", "CNY", "小盘"),        # Mid 和 Small 之间
                (500, "A-Share", "CNY", "小盘"),         # 等于 Small
                (499.99, "A-Share", "CNY", "微盘"),      # 略低于 Small
                (100, "A-Share", "CNY", "微盘"),         # Small 和 Micro 之间
                (0, "A-Share", "CNY", "微盘"),           # 等于 Micro
                (-1, "A-Share", "CNY", "未知风格(低于Micro下限)"), # 低于 Micro (理论上是0)
                (None, "A-Share", "CNY", "未知风格(市值为空)"), # 市值为空
                (5000, "Unknown-Market", "CNY", "未知风格(市场类型 Unknown-Market 无配置)"), # 未知市场
            ]
            logger.info("--- A股市场风格分类测试 ---")
            for mc, m_type, curr, expected_label in test_cases_a_share:
                actual_label = _classify_market_cap_style(mc, m_type, curr)
                result_str = "通过" if actual_label == expected_label else f"失败 (预期: '{expected_label}', 实际: '{actual_label}')"
                logger.info(f"测试: 市值={mc}, 市场={m_type}, 币种={curr} -> 风格='{actual_label}' --- {result_str}")

            # HK-Share Tests
            test_cases_hk_share = [
                (15000, "HK-Share", "HKD", "超大市值股"),       # 高于 Mega (HK)
                (10000, "HK-Share", "HKD", "超大市值股"),       # 等于 Mega (HK)
                (9999.99, "HK-Share", "HKD", "大市值股"),      # 略低于 Mega (HK)
                (5000, "HK-Share", "HKD", "大市值股"),        # 等于 Large (HK)
                (1000, "HK-Share", "HKD", "中市值股"),        # 等于 Mid (HK)
                (100, "HK-Share", "HKD", "小市值股"),         # 等于 Small (HK)
                (0, "HK-Share", "HKD", "微市值股"),           # 等于 Micro (HK)
                (5000, "HK-Share", "CNY", "大市值股 (CNY)"),  # 测试不同币种
                (None, "HK-Share", "HKD", "未知风格(市值为空)"), # 市值为空
            ]
            logger.info("--- 港股市场风格分类测试 ---")
            for mc, m_type, curr, expected_label in test_cases_hk_share:
                actual_label = _classify_market_cap_style(mc, m_type, curr)
                result_str = "通过" if actual_label == expected_label else f"失败 (预期: '{expected_label}', 实际: '{actual_label}')"
                logger.info(f"测试: 市值={mc}, 市场={m_type}, 币种={curr} -> 风格='{actual_label}' --- {result_str}")

            # 测试配置不完整的情况 (模拟)
            original_config = MARKET_CAP_CONFIG
            try:
                # 模拟 A-Share 的 Large 阈值缺失
                if MARKET_CAP_CONFIG and "A-Share" in MARKET_CAP_CONFIG.get("market_cap_styles", {}) and \
                   "thresholds" in MARKET_CAP_CONFIG["market_cap_styles"]["A-Share"]:

                    temp_config_a_share = json.loads(json.dumps(MARKET_CAP_CONFIG["market_cap_styles"]["A-Share"])) # 深拷贝
                    if "Large" in temp_config_a_share["thresholds"]:
                        del temp_config_a_share["thresholds"]["Large"]

                    # 替换全局配置以进行测试
                    MARKET_CAP_CONFIG["market_cap_styles"]["A-Share-Test-Missing-Large"] = temp_config_a_share

                    logger.info("--- A股市场风格分类测试 (Large阈值缺失) ---")
                    mc_test_missing = 6000 # 期望应落入 Mega (如果 Mega 存在) 或 中盘 (如果 Mid 存在且 Mega 不存在)
                                          # 根据新逻辑，如果 Large 缺失，会跳过 Large 的判断
                                          # 6000 >= Mega(20000) -> False
                                          # 6000 >= Mid(2000) -> True, 所以应该是 中盘
                    expected_label_missing = "中盘" # 因为 Large 缺失，6000 会跳过 Large 判断，然后 >= Mid(2000)

                    # 确保 Mega 存在，否则预期会不同
                    if not MARKET_CAP_CONFIG["market_cap_styles"]["A-Share-Test-Missing-Large"]["thresholds"].get("Mega"):
                         # 如果 Mega 也不存在，6000 >= Mid(2000) -> 中盘
                         pass # 预期不变
                    elif MARKET_CAP_CONFIG["market_cap_styles"]["A-Share-Test-Missing-Large"]["thresholds"].get("Mega") and \
                         mc_test_missing >= MARKET_CAP_CONFIG["market_cap_styles"]["A-Share-Test-Missing-Large"]["thresholds"]["Mega"]:
                         expected_label_missing = MARKET_CAP_CONFIG["market_cap_styles"]["A-Share-Test-Missing-Large"]["labels"].get("Mega", "未知Mega")


                    actual_label_missing = _classify_market_cap_style(mc_test_missing, "A-Share-Test-Missing-Large", "CNY")
                    result_str_missing = "通过" if actual_label_missing == expected_label_missing else f"失败 (预期: '{expected_label_missing}', 实际: '{actual_label_missing}')"
                    logger.info(f"测试: 市值={mc_test_missing}, 市场=A-Share-Test-Missing-Large, 币种=CNY -> 风格='{actual_label_missing}' --- {result_str_missing}")

                    # 测试所有阈值都缺失的情况
                    MARKET_CAP_CONFIG["market_cap_styles"]["A-Share-Test-All-Missing"] = {
                        "currency": "CNY", "unit": "万", "thresholds": {}, "labels": {}
                    }
                    actual_label_all_missing = _classify_market_cap_style(5000, "A-Share-Test-All-Missing", "CNY")
                    expected_label_all_missing = "未知风格(低于Micro下限)" # 因为所有阈值都是None
                    result_str_all_missing = "通过" if actual_label_all_missing == expected_label_all_missing else f"失败 (预期: '{expected_label_all_missing}', 实际: '{actual_label_all_missing}')"
                    logger.info(f"测试: 市值=5000, 市场=A-Share-Test-All-Missing -> 风格='{actual_label_all_missing}' --- {result_str_all_missing}")


            except Exception as e_test:
                logger.error(f"市值风格配置模拟测试中发生错误: {e_test}")
            finally:
                # 恢复原始配置
                MARKET_CAP_CONFIG = original_config
                # 确保全局配置被正确加载回来，或者通过 _load_market_cap_config() 强制重新加载
                if MARKET_CAP_CONFIG is None or "A-Share-Test-Missing-Large" in MARKET_CAP_CONFIG["market_cap_styles"]:
                    _load_market_cap_config() # 强制重新加载原始配置


# ==================== 简化的季度配置支持 ====================

def get_quarterly_data_config():
    """
    读取季度数据配置

    Returns:
        (year, quarter) 元组，None表示使用最新数据
    """
    try:
        config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "config", "quarterly_data_config.json")
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)

        settings = config.get('quarterly_data', {})
        year = settings.get('default_year')
        quarter = settings.get('default_quarter')

        return year, quarter
    except Exception as e:
        logger.warning(f"读取季度数据配置失败，使用默认设置: {e}")
        return None, None


def fetch_fund_data_with_config(fund_code: str, year: int = None, quarter: int = None) -> dict | None:
    """
    使用季度数据配置的基金数据获取函数

    Args:
        fund_code: 基金代码
        year: 年份，None表示使用配置文件或最新数据
        quarter: 季度，None表示使用配置文件或最新数据

    Returns:
        基金数据字典或None
    """
    # 如果没有指定年份季度，尝试从配置文件读取
    if year is None or quarter is None:
        config_year, config_quarter = get_quarterly_data_config()
        year = year or config_year
        quarter = quarter or config_quarter

    return fetch_fund_data(fund_code, year, quarter)


