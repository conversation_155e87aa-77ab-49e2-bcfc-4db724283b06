#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的多基金模型对比测试
基于快速测试的成功经验，扩展到多个基金
"""

import os
import sys
import json
import datetime
from pathlib import Path
from dotenv import load_dotenv

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 加载环境变量
load_dotenv()

from src.llm_analyzer import analyze_with_llm
from src.data_fetcher import fetch_fund_data, fetch_fund_portfolio_data, get_fund_sw_industry_info, get_fund_hk_sw_industry_info, get_fund_valuation_summary
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_analysis_rules():
    """加载分析规则"""
    rules_path = project_root / "docs" / "深度分析专精.md"
    try:
        with open(rules_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        logger.error(f"加载分析规则失败: {e}")
        return ""

def get_real_fund_data(fund_code):
    """获取真实的基金数据"""
    logger.info(f"正在获取基金 {fund_code} 的真实数据...")

    try:
        # 1. 获取基金基本信息
        fund_data_raw = fetch_fund_data(fund_code)
        if not fund_data_raw:
            logger.error(f"无法获取基金 {fund_code} 的基本信息")
            return None

        fund_name = fund_data_raw.get("fund_name", "未知基金名称")
        fund_manager = fund_data_raw.get("fund_manager", "未知基金经理")
        market_outlook = fund_data_raw.get("market_outlook", "")
        operation_analysis = fund_data_raw.get("operation_analysis", "")
        report_end_date = fund_data_raw.get("report_end_date")

        if not report_end_date:
            logger.error(f"无法获取基金 {fund_code} 的报告期截止日")
            return None

        # 2. 获取持仓数据
        portfolio_data = fetch_fund_portfolio_data(fund_code, report_end_date)
        portfolio_data_str = "未获取到持仓数据。"

        if portfolio_data:
            formatted_portfolio_items = []
            for item in portfolio_data:
                total_mv_wanyuan = item.get('total_mv')
                if total_mv_wanyuan is not None:
                    try:
                        total_mv_float = float(total_mv_wanyuan)
                        total_mv_yiyuan = total_mv_float / 10000
                        total_mv_str = f"{total_mv_yiyuan:.2f}亿元"
                    except (ValueError, TypeError):
                        total_mv_str = "无效市值数据"
                else:
                    total_mv_str = "未知市值"

                formatted_portfolio_items.append(
                    f"股票代码: {item['symbol']}, 股票名称: {item.get('stock_name', '未知名称')}, "
                    f"占净值比例: {item.get('stk_mkv_ratio', 'N/A')}%, 持股数量: {item.get('shares', 'N/A')}, "
                    f"市值: {total_mv_str}"
                )
            portfolio_data_str = "\n".join(formatted_portfolio_items)

        # 3. 获取申万行业分类信息
        sw_industry_info = get_fund_sw_industry_info(fund_code, report_end_date)
        sw_industry_info_str = "未获取到申万行业分类信息。"

        if sw_industry_info and sw_industry_info.get("holdings_sw_info"):
            sw_items = []
            for item in sw_industry_info["holdings_sw_info"]:
                sw_items.append(f'"{item.get("stock_name", "未知")}": {{"一级行业": "{item.get("l1_name", "未知")}", "二级行业": "{item.get("l2_name", "未知")}", "三级行业": "{item.get("l3_name", "未知")}"}}')
            sw_industry_info_str = "{" + ", ".join(sw_items) + "}"

        # 4. 获取港股申万行业分类信息
        hk_sw_industry_info = get_fund_hk_sw_industry_info(fund_code, report_end_date)
        if hk_sw_industry_info and hk_sw_industry_info.get("holdings_hk_sw_info"):
            hk_sw_items = []
            for item in hk_sw_industry_info["holdings_hk_sw_info"]:
                hk_sw_items.append(f'"{item.get("stock_name", "未知")}": {{"一级行业": "{item.get("l1_name", "未知")}", "二级行业": "{item.get("l2_name", "未知")}", "三级行业": "{item.get("l3_name", "未知")}"}}')
            if hk_sw_items:
                if sw_industry_info_str != "未获取到申万行业分类信息。":
                    sw_industry_info_str = sw_industry_info_str[:-1] + ", " + ", ".join(hk_sw_items) + "}"
                else:
                    sw_industry_info_str = "{" + ", ".join(hk_sw_items) + "}"

        # 5. 获取估值统计信息
        valuation_summary = get_fund_valuation_summary(fund_code, report_end_date)
        valuation_summary_str = "未获取到估值统计信息。"

        if valuation_summary:
            valuation_summary_str = f'{{"平均PE": {valuation_summary.get("avg_pe", "null")}, "中位数PE": {valuation_summary.get("median_pe", "null")}, "平均PB": {valuation_summary.get("avg_pb", "null")}}}'

        # 6. 规模约束分析（简化版）
        scale_analysis_str = '{"整体规模上限": "数据不足", "瓶颈股票": "无法分析"}'

        return {
            "fund_name": fund_name,
            "fund_manager": fund_manager,
            "test_data": {
                "report_end_date": report_end_date,
                "market_outlook": market_outlook,
                "operation_analysis": operation_analysis,
                "portfolio_data": portfolio_data_str,
                "sw_industry_info": sw_industry_info_str,
                "valuation_summary": valuation_summary_str,
                "scale_analysis": scale_analysis_str
            }
        }

    except Exception as e:
        logger.error(f"获取基金 {fund_code} 真实数据时出错: {e}")
        return None

def test_fund_with_both_models(fund_code, fund_name, fund_manager, test_data):
    """使用新旧模型测试单个基金"""
    
    # 模型配置 - 新旧模型使用不同的API地址和密钥
    old_model = {
        "name": "vertexai/gemini-2.5-pro-preview-05-06",
        "api_key": "sk-mqWRZm6zFLW04WRL72FeEcF983634a5c8bF7Ef1b955eC3Ab",
        "api_base": "http://206.189.40.22:3009/v1"
    }
    
    new_model = {
        "name": "gemini-2.5-pro-preview-06-05",
        "api_key": "sk-gRq2nHouhXUgRqVzdnKbQYVDwPYIhLj94NmG2rgTjAuZA8SC",
        "api_base": "https://axhixwxzsmtd.us-west-1.clawcloudrun.com/v1"
    }
    
    analysis_rules = load_analysis_rules()
    
    print(f"🔄 测试基金: {fund_name} ({fund_code})")
    
    results = {}
    
    # 测试旧模型
    print("  🔵 测试旧模型...")
    try:
        old_report = analyze_with_llm(
            fund_code=fund_code,
            fund_name=fund_name,
            fund_manager=fund_manager,
            report_end_date=test_data["report_end_date"],
            market_outlook=test_data["market_outlook"],
            operation_analysis=test_data["operation_analysis"],
            portfolio_data=test_data["portfolio_data"],
            sw_industry_info=test_data["sw_industry_info"],
            valuation_summary=test_data["valuation_summary"],
            scale_analysis=test_data["scale_analysis"],
            analysis_rules=analysis_rules,
            model_name=old_model["name"],
            api_key=old_model["api_key"],
            api_base=old_model["api_base"]
        )
        
        if old_report:
            print("    ✅ 旧模型成功")
            results["old_report"] = old_report
            results["old_length"] = len(old_report)
        else:
            print("    ❌ 旧模型失败")
            return None
            
    except Exception as e:
        print(f"    ❌ 旧模型出错: {e}")
        return None
    
    # 测试新模型
    print("  🟢 测试新模型...")
    try:
        new_report = analyze_with_llm(
            fund_code=fund_code,
            fund_name=fund_name,
            fund_manager=fund_manager,
            report_end_date=test_data["report_end_date"],
            market_outlook=test_data["market_outlook"],
            operation_analysis=test_data["operation_analysis"],
            portfolio_data=test_data["portfolio_data"],
            sw_industry_info=test_data["sw_industry_info"],
            valuation_summary=test_data["valuation_summary"],
            scale_analysis=test_data["scale_analysis"],
            analysis_rules=analysis_rules,
            model_name=new_model["name"],
            api_key=new_model["api_key"],
            api_base=new_model["api_base"]
        )
        
        if new_report:
            print("    ✅ 新模型成功")
            results["new_report"] = new_report
            results["new_length"] = len(new_report)
        else:
            print("    ❌ 新模型失败")
            return None
            
    except Exception as e:
        print(f"    ❌ 新模型出错: {e}")
        return None
    
    return results

def save_comparison_results(fund_code, fund_name, results):
    """保存对比结果"""
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 创建输出目录
    output_dir = Path("multi_test_results")
    output_dir.mkdir(exist_ok=True)
    
    # 保存旧模型报告
    old_report_path = output_dir / f"{fund_code}_OLD_{timestamp}.md"
    with open(old_report_path, 'w', encoding='utf-8') as f:
        f.write(f"# 旧模型报告 - {fund_name}\n\n")
        f.write(results["old_report"])
    
    # 保存新模型报告
    new_report_path = output_dir / f"{fund_code}_NEW_{timestamp}.md"
    with open(new_report_path, 'w', encoding='utf-8') as f:
        f.write(f"# 新模型报告 - {fund_name}\n\n")
        f.write(results["new_report"])
    
    # 简单质量对比
    old_has_json = "```json" in results["old_report"]
    new_has_json = "```json" in results["new_report"]
    old_has_indicators = any(indicator in results["old_report"] for indicator in ["🔴", "🟡", "🟢"])
    new_has_indicators = any(indicator in results["new_report"] for indicator in ["🔴", "🟡", "🟢"])
    
    # 生成对比摘要
    comparison_path = output_dir / f"{fund_code}_COMPARISON_{timestamp}.md"
    with open(comparison_path, 'w', encoding='utf-8') as f:
        f.write(f"# 模型对比摘要 - {fund_name}\n\n")
        f.write(f"**基金代码**: {fund_code}\n")
        f.write(f"**测试时间**: {timestamp}\n\n")
        
        f.write("## 📊 基本指标对比\n\n")
        f.write(f"| 指标 | 旧模型 | 新模型 |\n")
        f.write(f"|------|--------|--------|\n")
        f.write(f"| 报告长度 | {results['old_length']:,} 字符 | {results['new_length']:,} 字符 |\n")
        f.write(f"| JSON结构 | {'✅' if old_has_json else '❌'} | {'✅' if new_has_json else '❌'} |\n")
        f.write(f"| 重点标识 | {'✅' if old_has_indicators else '❌'} | {'✅' if new_has_indicators else '❌'} |\n\n")
        
        f.write("## 📁 报告文件\n\n")
        f.write(f"- **旧模型报告**: {old_report_path.name}\n")
        f.write(f"- **新模型报告**: {new_report_path.name}\n\n")
        
        f.write("## 🔍 质量评估清单\n\n")
        f.write("请手动检查以下方面：\n\n")
        f.write("### 内容质量\n")
        f.write("- [ ] 分析逻辑深度\n")
        f.write("- [ ] 核心洞察质量\n")
        f.write("- [ ] 风险识别准确性\n")
        f.write("- [ ] 投资建议合理性\n\n")
        f.write("### 表达质量\n")
        f.write("- [ ] 中文表达流畅度\n")
        f.write("- [ ] 专业术语准确性\n")
        f.write("- [ ] 逻辑表达清晰度\n")
        f.write("- [ ] 重点标识使用恰当性\n\n")
        f.write("### 结构完整性\n")
        f.write("- [ ] 7个部分结构完整\n")
        f.write("- [ ] JSON数据格式正确\n")
        f.write("- [ ] Markdown格式规范\n\n")
        f.write("## 💡 推荐结论\n\n")
        f.write("- [ ] 保持旧模型\n")
        f.write("- [ ] 切换到新模型\n")
        f.write("- [ ] 需要更多测试\n\n")
    
    print(f"  📁 对比结果已保存: {comparison_path.name}")
    return comparison_path

def main():
    """主测试函数 - 使用真实数据"""

    # 要测试的基金代码列表 - 从reports目录中选择真实存在的基金
    fund_codes_to_test = [
        "008186.OF",  # 淳厚信睿核心精选A
        "019820.OF",  # 鹏华远见精选A
        "000573.OF",  # 天弘通利A
        "110009.OF",  # 易方达价值成长混合
        "161725.OF"   # 招商中证白酒指数分级
    ]

    print("🚀 开始多基金模型对比测试（使用真实数据）...")
    print(f"📊 测试基金数量: {len(fund_codes_to_test)}")
    print()

    test_funds = []

    # 获取真实数据
    for fund_code in fund_codes_to_test:
        print(f"📥 获取基金 {fund_code} 的真实数据...")
        real_data = get_real_fund_data(fund_code)

        if real_data:
            test_funds.append({
                "fund_code": fund_code,
                "fund_name": real_data["fund_name"],
                "fund_manager": real_data["fund_manager"],
                "test_data": real_data["test_data"]
            })
            print(f"  ✅ 成功获取 {real_data['fund_name']} 的数据")
        else:
            print(f"  ❌ 无法获取基金 {fund_code} 的数据，跳过测试")
        print()

    if not test_funds:
        print("❌ 没有成功获取任何基金的真实数据，测试终止")
        return

    print(f"🚀 开始模型对比测试，共 {len(test_funds)} 个基金...")
    print()
    
    all_results = {}
    successful_tests = 0
    
    for fund_info in test_funds:
        fund_code = fund_info["fund_code"]
        fund_name = fund_info["fund_name"]
        fund_manager = fund_info["fund_manager"]
        test_data = fund_info["test_data"]
        
        try:
            results = test_fund_with_both_models(fund_code, fund_name, fund_manager, test_data)
            
            if results:
                save_comparison_results(fund_code, fund_name, results)
                all_results[fund_code] = results
                successful_tests += 1
                print("  ✅ 测试成功")
            else:
                print("  ❌ 测试失败")
                
        except Exception as e:
            print(f"  ❌ 测试出错: {e}")
        
        print()
    
    # 生成总体摘要
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = Path("multi_test_results")
    summary_path = output_dir / f"OVERALL_SUMMARY_{timestamp}.md"
    
    with open(summary_path, 'w', encoding='utf-8') as f:
        f.write("# 多基金模型对比测试总结\n\n")
        f.write(f"**测试时间**: {timestamp}\n")
        f.write(f"**测试结果**: {successful_tests}/{len(test_funds)} 成功\n\n")
        
        f.write("## 📊 测试结果概览\n\n")
        for fund_info in test_funds:
            fund_code = fund_info["fund_code"]
            fund_name = fund_info["fund_name"]
            status = "✅ 成功" if fund_code in all_results else "❌ 失败"
            f.write(f"- **{fund_name}** ({fund_code}): {status}\n")
        
        if all_results:
            f.write("\n## 📈 质量对比统计\n\n")
            f.write("| 基金 | 旧模型长度 | 新模型长度 | 长度变化 |\n")
            f.write("|------|------------|------------|----------|\n")
            
            for fund_code, results in all_results.items():
                fund_name = next(f["fund_name"] for f in test_funds if f["fund_code"] == fund_code)
                old_len = results["old_length"]
                new_len = results["new_length"]
                change = ((new_len - old_len) / old_len * 100) if old_len > 0 else 0
                f.write(f"| {fund_name} | {old_len:,} | {new_len:,} | {change:+.1f}% |\n")
        
        f.write("\n## 🎯 总体建议\n\n")
        if successful_tests == len(test_funds):
            f.write("✅ 所有测试通过，请详细检查各基金的对比报告\n")
        elif successful_tests > 0:
            f.write("⚠️ 部分测试通过，请检查失败原因并重新测试\n")
        else:
            f.write("❌ 所有测试失败，请检查配置和网络连接\n")
        
        f.write("\n## 📁 详细报告\n\n")
        f.write("请查看各基金的详细对比报告文件进行质量评估。\n")
    
    print("🎉 多基金测试完成！")
    print(f"📊 成功率: {successful_tests}/{len(test_funds)}")
    print(f"📁 总结报告: {summary_path}")

if __name__ == "__main__":
    main()
