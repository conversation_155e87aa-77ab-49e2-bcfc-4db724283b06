#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量新老模型对比测试脚本
基于新提示词进行多个基金的新老模型对比分析
"""

import os
import sys
import json
import logging
import difflib
from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Optional, Tuple

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from llm_analyzer import analyze_with_llm
from data_fetcher import fetch_fund_data, fetch_fund_portfolio_data
from report_generator import _extract_json_from_report

class DecimalEncoder(json.JSONEncoder):
    """自定义JSON编码器，处理Decimal类型"""
    def default(self, obj):
        if isinstance(obj, Decimal):
            return float(obj)
        return super(DecimalEncoder, self).default(obj)

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('batch_model_comparison.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

def load_analysis_rules():
    """加载分析规则"""
    try:
        with open("docs/深度分析专精.md", "r", encoding="utf-8") as f:
            return f.read()
    except FileNotFoundError:
        logging.error("未找到分析规则文件: docs/深度分析专精.md")
        return None

def compare_single_fund(fund_code: str) -> Optional[Dict]:
    """对单个基金进行新老模型对比"""
    logger = logging.getLogger(__name__)
    
    # 模型配置
    old_model_config = {
        "name": "vertexai/gemini-2.5-pro-preview-05-06",
        "api_key": "sk-mqWRZm6zFLW04WRL72FeEcF983634a5c8bF7Ef1b955eC3Ab",
        "api_base": "http://206.189.40.22:3009/v1"
    }
    
    new_model_config = {
        "name": "gemini-2.5-pro-exp-03-25",
        "api_key": "sk-gRq2nHouhXUgRqVzdnKbQYVDwPYIhLj94NmG2rgTjAuZA8SC",
        "api_base": "https://axhixwxzsmtd.us-west-1.clawcloudrun.com/v1"
    }
    
    logger.info(f"开始对比基金 {fund_code}")
    
    # 1. 加载分析规则
    analysis_rules_content = load_analysis_rules()
    if not analysis_rules_content:
        logger.error("未能加载分析规则，跳过此基金")
        return None
    
    # 2. 获取基金数据
    fund_data_raw = fetch_fund_data(fund_code)
    if fund_data_raw is None:
        logger.error(f"未能获取基金 {fund_code} 的数据，跳过此基金")
        return None
    
    fund_name = fund_data_raw.get("fund_name", "未知基金名称")
    fund_manager = fund_data_raw.get("fund_manager", "未知基金经理")
    market_outlook = fund_data_raw.get("market_outlook", "")
    operation_analysis = fund_data_raw.get("operation_analysis", "")
    report_end_date = fund_data_raw.get("report_end_date", "")
    
    # 3. 获取持仓数据
    portfolio_data = fetch_fund_portfolio_data(fund_code, report_end_date)
    if not portfolio_data:
        logger.warning(f"基金 {fund_code} 未获取到持仓数据")
        portfolio_data_str = "未提供持仓数据"
    else:
        portfolio_data_str = json.dumps(portfolio_data, ensure_ascii=False, indent=2)
    
    # 4. 准备公共参数
    base_params = {
        "fund_code": fund_code,
        "fund_name": fund_name,
        "fund_manager": fund_manager,
        "report_end_date": report_end_date,
        "market_outlook": market_outlook,
        "operation_analysis": operation_analysis,
        "portfolio_data": portfolio_data_str,
        "sw_industry_info": "",
        "valuation_summary": "",
        "scale_analysis": "",
        "analysis_rules": analysis_rules_content
    }
    
    # 5. 调用老模型
    logger.info(f"正在使用老模型分析 {fund_code}...")
    old_params = {**base_params, **old_model_config}
    report_old = analyze_with_llm(**old_params)
    if not report_old:
        logger.error(f"老模型分析 {fund_code} 失败")
        return None
    
    # 6. 调用新模型
    logger.info(f"正在使用新模型分析 {fund_code}...")
    new_params = {**base_params, **new_model_config}
    report_new = analyze_with_llm(**new_params)
    if not report_new:
        logger.error(f"新模型分析 {fund_code} 失败")
        return None
    
    # 7. 生成对比结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存单独的报告文件
    old_report_path = f"新老模型对比分析/old_model_{fund_code}_{timestamp}.md"
    new_report_path = f"新老模型对比分析/new_model_{fund_code}_{timestamp}.md"
    
    with open(old_report_path, "w", encoding="utf-8") as f:
        f.write(report_old)
    with open(new_report_path, "w", encoding="utf-8") as f:
        f.write(report_new)
    
    # 8. 生成质量评估
    quality_assessment = assess_quality(report_old, report_new)
    
    # 9. 生成对比报告
    comparison_result = {
        "fund_code": fund_code,
        "fund_name": fund_name,
        "timestamp": timestamp,
        "old_model": old_model_config["name"],
        "new_model": new_model_config["name"],
        "old_report_path": old_report_path,
        "new_report_path": new_report_path,
        "quality_assessment": quality_assessment
    }
    
    # 保存对比结果JSON
    comparison_json_path = f"新老模型对比分析/comparison_result_{fund_code}_{timestamp}.json"
    with open(comparison_json_path, "w", encoding="utf-8") as f:
        json.dump(comparison_result, f, ensure_ascii=False, indent=2)
    
    logger.info(f"基金 {fund_code} 对比完成")
    return comparison_result

def assess_quality(report_old: str, report_new: str) -> Dict:
    """评估报告质量"""
    # 长度对比
    old_length = len(report_old)
    new_length = len(report_new)
    
    # JSON有效性检查
    old_json = _extract_json_from_report(report_old)
    new_json = _extract_json_from_report(report_new)
    
    old_json_valid = old_json is not None
    new_json_valid = new_json is not None
    
    # 结构完整性检查（简化版）
    old_has_structure = "### **" in report_old or "## " in report_old
    new_has_structure = "### **" in report_new or "## " in report_new
    
    # 专业术语检查
    professional_terms = ["GARP", "护城河", "竞争壁垒", "ROE", "PE(TTM)", "PB", "估值", "风险"]
    old_term_count = sum(1 for term in professional_terms if term in report_old)
    new_term_count = sum(1 for term in professional_terms if term in report_new)
    
    return {
        "length_comparison": {
            "old_length": old_length,
            "new_length": new_length,
            "length_diff": new_length - old_length,
            "length_ratio": new_length / old_length if old_length > 0 else 0
        },
        "json_comparison": {
            "old_json_valid": old_json_valid,
            "new_json_valid": new_json_valid,
            "json_improvement": new_json_valid and not old_json_valid
        },
        "structure_comparison": {
            "old_has_structure": old_has_structure,
            "new_has_structure": new_has_structure
        },
        "professional_terms": {
            "old_term_count": old_term_count,
            "new_term_count": new_term_count,
            "term_improvement": new_term_count - old_term_count
        }
    }

def batch_compare_models(fund_codes: List[str]) -> List[Dict]:
    """批量对比多个基金"""
    logger = logging.getLogger(__name__)
    results = []
    
    for i, fund_code in enumerate(fund_codes, 1):
        logger.info(f"处理第 {i}/{len(fund_codes)} 个基金: {fund_code}")
        try:
            result = compare_single_fund(fund_code)
            if result:
                results.append(result)
        except Exception as e:
            logger.error(f"处理基金 {fund_code} 时出错: {e}")
            continue
    
    return results

def generate_summary_report(results: List[Dict]):
    """生成汇总报告"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    summary_path = f"新老模型对比分析/BATCH_SUMMARY_{timestamp}.md"
    
    content = f"""# 批量新老模型对比分析汇总报告
生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## 总体统计
- 成功对比基金数量: {len(results)}
- 老模型: {results[0]['old_model'] if results else 'N/A'}
- 新模型: {results[0]['new_model'] if results else 'N/A'}

## 质量改进统计
"""
    
    if results:
        json_improvements = sum(1 for r in results if r['quality_assessment']['json_comparison']['json_improvement'])
        avg_length_ratio = sum(r['quality_assessment']['length_comparison']['length_ratio'] for r in results) / len(results)
        avg_term_improvement = sum(r['quality_assessment']['professional_terms']['term_improvement'] for r in results) / len(results)
        
        content += f"""
- JSON格式改进基金数: {json_improvements}/{len(results)} ({json_improvements/len(results)*100:.1f}%)
- 平均文本长度比例: {avg_length_ratio:.2f}
- 平均专业术语增加数: {avg_term_improvement:.1f}

## 详细结果
"""
        
        for result in results:
            qa = result['quality_assessment']
            content += f"""
### {result['fund_code']} - {result['fund_name']}
- JSON有效性: {qa['json_comparison']['old_json_valid']} → {qa['json_comparison']['new_json_valid']}
- 文本长度: {qa['length_comparison']['old_length']} → {qa['length_comparison']['new_length']} (比例: {qa['length_comparison']['length_ratio']:.2f})
- 专业术语: {qa['professional_terms']['old_term_count']} → {qa['professional_terms']['new_term_count']} (+{qa['professional_terms']['term_improvement']})
- 文件路径: 
  - 老模型: {result['old_report_path']}
  - 新模型: {result['new_report_path']}
"""
    
    with open(summary_path, "w", encoding="utf-8") as f:
        f.write(content)
    
    logging.info(f"汇总报告已生成: {summary_path}")

if __name__ == "__main__":
    setup_logging()
    logger = logging.getLogger(__name__)
    
    # 测试基金列表 - 选择多个代表性基金
    test_funds = [
        "017826.OF",  # 之前的高质量范本
        "000573.OF",  # 另一个测试基金
        "002350.OF",  # 第三个测试基金
        "006529.OF",  # 第四个测试基金
        "000418.OF"   # 第五个测试基金
    ]
    
    logger.info(f"开始批量对比 {len(test_funds)} 个基金")
    results = batch_compare_models(test_funds)
    
    if results:
        generate_summary_report(results)
        logger.info(f"批量对比完成，成功处理 {len(results)} 个基金")
    else:
        logger.error("批量对比失败，没有成功处理任何基金")
