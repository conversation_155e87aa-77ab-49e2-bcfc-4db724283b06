"""
外部数据提供者模块 - Wind API集成

该模块负责处理与Wind API的交互，主要为港股补充估值数据。
后续可扩展支持iFind等其他数据源。

设计原则：
1. 模块化与解耦：独立处理Wind API交互
2. 条件化调用：基于配置和市场类型选择性获取数据
3. 容错性：API失败不中断主流程
4. 数据适配：将Wind数据转换为项目标准格式

主要功能：
- 为港股获取PE、PB、市值等估值数据
- 支持配置化的条件调用
- 提供数据质量控制和错误处理
"""

import logging
import json
import os
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

# 全局配置缓存
EXTERNAL_API_CONFIG = None


def _load_external_api_config() -> Dict[str, Any] | None:
    """
    加载外部API配置文件

    Returns:
        配置字典，如果加载失败则返回None
    """
    global EXTERNAL_API_CONFIG
    if EXTERNAL_API_CONFIG is not None:
        return EXTERNAL_API_CONFIG

    # 获取项目根目录下的配置文件
    current_script_dir = os.path.dirname(os.path.abspath(__file__))
    project_root_dir = os.path.dirname(current_script_dir)
    config_file_path = os.path.join(project_root_dir, "config", "external_api_config.json")

    try:
        with open(config_file_path, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
            EXTERNAL_API_CONFIG = config_data
            logger.info(f"成功从 {config_file_path} 加载外部API配置。")
            return config_data
    except FileNotFoundError:
        logger.warning(f"外部API配置文件 {config_file_path} 未找到。外部数据功能将被禁用。")
        return None
    except json.JSONDecodeError:
        logger.error(f"解析外部API配置文件 {config_file_path} 失败。请检查JSON格式。")
        return None
    except Exception as e:
        logger.error(f"加载外部API配置文件 {config_file_path} 时发生未知错误: {e}", exc_info=True)
        return None


def _get_recent_trade_date() -> str:
    """
    获取最近的交易日期（格式：YYYYMMDD）

    简单实现：返回最近的工作日，并回退几天确保有数据
    实际应用中可以考虑使用交易日历API

    Returns:
        格式为YYYYMMDD的日期字符串
    """
    today = datetime.now()

    # 回退到最近的工作日，并额外回退1-2天确保有数据
    if today.weekday() == 5:  # 周六
        trade_date = today - timedelta(days=2)  # 回到周四
    elif today.weekday() == 6:  # 周日
        trade_date = today - timedelta(days=3)  # 回到周四
    elif today.weekday() == 0:  # 周一
        trade_date = today - timedelta(days=3)  # 回到上周五
    else:
        trade_date = today - timedelta(days=1)  # 回退1天

    return trade_date.strftime("%Y%m%d")


def _get_market_suffix(stock_code: str) -> str:
    """
    根据股票代码确定市场后缀

    Args:
        stock_code: 股票代码（如 "000001.SZ", "700.HK"）

    Returns:
        市场后缀字符串（如 "_a", "_hk"）
    """
    if not stock_code or not isinstance(stock_code, str):
        logger.warning(f"无效的股票代码: {stock_code}")
        return "_unknown"

    stock_code_upper = stock_code.upper()

    if stock_code_upper.endswith('.HK'):
        return "_hk"
    elif stock_code_upper.endswith('.SZ') or stock_code_upper.endswith('.SH'):
        return "_a"
    elif stock_code_upper.endswith('.BJ'):  # 北交所
        return "_bj"
    else:
        logger.warning(f"未知的股票代码格式: {stock_code}")
        return "_unknown"


def should_fetch_data_point(stock_code: str, data_point_name: str, source: str = "wind") -> bool:
    """
    检查是否应该为指定股票和数据点从外部API获取数据

    Args:
        stock_code: 股票代码
        data_point_name: 数据点名称（如 "valuation", "financial_details"）
        source: API源名称（默认为 "wind"）

    Returns:
        True 如果应该获取数据，False 否则
    """
    config = _load_external_api_config()
    if not config:
        logger.debug(f"外部API配置未加载，跳过 {stock_code} 的 {data_point_name} 数据获取。")
        return False

    # 检查全局开关
    if not config.get("external_data", {}).get("enabled", False):
        logger.debug("外部数据功能全局禁用。")
        return False

    # 检查特定源的开关
    source_config_key = f"external_data_{source}"
    source_config = config.get(source_config_key, {})
    if not source_config.get("enabled", False):
        logger.debug(f"外部数据源 {source} 被禁用。")
        return False

    # 确定市场后缀
    market_suffix = _get_market_suffix(stock_code)
    if market_suffix == "_unknown":
        logger.warning(f"无法确定股票 {stock_code} 的市场类型，跳过外部数据获取。")
        return False

    # 构建配置键名
    config_key = f"fetch_{data_point_name}{market_suffix}"

    # 检查特定配置
    should_fetch = source_config.get(config_key, False)

    logger.debug(f"股票 {stock_code}, 数据点 {data_point_name}, 源 {source}: "
                f"配置键 {config_key} = {should_fetch}")

    return should_fetch


def _get_wind_field_codes(data_point_name: str, market_suffix: str, source: str = "wind") -> List[str]:
    """
    获取Wind API特定数据点的字段代码

    Args:
        data_point_name: 数据点名称
        market_suffix: 市场后缀
        source: API源名称

    Returns:
        Wind字段代码列表
    """
    config = _load_external_api_config()
    if not config:
        return []

    source_fields_key = f"external_data_{source}_fields"
    fields_config = config.get(source_fields_key, {})

    # 构建字段配置键名
    field_config_key = f"{data_point_name}{market_suffix}"
    field_codes = fields_config.get(field_config_key, [])

    if isinstance(field_codes, str):
        field_codes = [field_codes]

    logger.debug(f"数据点 {data_point_name}, 市场 {market_suffix}: Wind字段代码 = {field_codes}")

    return field_codes


def _build_wind_snapshot_request(stock_code: str, field_codes: List[str], options: str = "") -> Dict[str, Any]:
    """
    构建Wind快照数据请求参数

    Args:
        stock_code: Wind格式的股票代码
        field_codes: Wind字段代码列表
        options: Wind选项字符串

    Returns:
        请求参数字典
    """
    return {
        "codes": stock_code,
        "fields": ",".join(field_codes),
        "options": options
    }


def _convert_wind_stock_code(stock_code: str) -> str:
    """
    将内部股票代码转换为Wind格式

    Args:
        stock_code: 内部股票代码（如 "700.HK", "000001.SZ"）

    Returns:
        Wind格式的股票代码
    """
    if not stock_code:
        return stock_code

    # 港股代码转换：700.HK -> 0700.HK (4位格式，不是5位)
    if stock_code.endswith('.HK'):
        parts = stock_code.split('.')
        if len(parts) == 2:
            numeric_part = parts[0]
            # Wind港股代码需要4位数字格式
            if len(numeric_part) < 4:
                numeric_part = numeric_part.zfill(4)
            return f"{numeric_part}.HK"

    # A股代码通常不需要转换
    return stock_code


def _parse_wind_response(response_data: Any, field_codes: List[str]) -> Dict[str, Any]:
    """
    解析Wind API响应数据

    Args:
        response_data: Wind API返回的原始数据
        field_codes: 请求的字段代码列表

    Returns:
        解析后的标准化数据字典
    """
    parsed_data = {}

    try:
        logger.debug(f"解析Wind响应数据，类型: {type(response_data)}")

        # 处理Wind API的不同返回格式
        data_matrix = None

        if hasattr(response_data, 'Data') and response_data.Data:
            # 新版本WindPy返回对象格式
            data_matrix = response_data.Data
            logger.debug(f"使用对象格式解析，数据矩阵: {data_matrix}")

        elif hasattr(response_data, 'data') and response_data.data:
            # 可能的小写属性
            data_matrix = response_data.data
            logger.debug(f"使用小写data属性解析，数据矩阵: {data_matrix}")

        elif isinstance(response_data, (list, tuple)) and len(response_data) > 0:
            # 直接是数据列表
            data_matrix = response_data
            logger.debug(f"使用列表格式解析，数据矩阵: {data_matrix}")

        if data_matrix:
            # 解析数据矩阵
            for i, field_code in enumerate(field_codes):
                try:
                    value = None

                    # 尝试不同的数据访问方式
                    if isinstance(data_matrix, list) and i < len(data_matrix):
                        if isinstance(data_matrix[i], list) and len(data_matrix[i]) > 0:
                            value = data_matrix[i][0]  # 二维数组格式
                        else:
                            value = data_matrix[i]  # 一维数组格式
                    elif hasattr(data_matrix, '__getitem__'):
                        value = data_matrix[i] if i < len(data_matrix) else None

                    logger.debug(f"字段 {field_code} 原始值: {value}")

                    # 字段代码到标准字段的映射 - 仅处理PE和PB数据
                    if field_code == "pe_ttm":
                        parsed_data["api_pe_ttm"] = float(value) if value is not None and str(value).lower() not in ['nan', 'none', ''] else None
                    elif field_code == "pb_mrq":
                        parsed_data["api_pb"] = float(value) if value is not None and str(value).lower() not in ['nan', 'none', ''] else None
                    # 移除了 mkt_cap_ard 处理，避免与本地数据库市值数据冲突
                    elif field_code == "RT_LAST":
                        parsed_data["api_last_price"] = float(value) if value is not None and str(value).lower() not in ['nan', 'none', ''] else None
                    elif field_code == "RT_CHG_PCT":
                        parsed_data["api_change_pct"] = float(value) if value is not None and str(value).lower() not in ['nan', 'none', ''] else None

                except (ValueError, TypeError, IndexError) as e:
                    logger.warning(f"解析字段 {field_code} 时发生错误: {e}")
                    continue

        # 添加元数据
        parsed_data["data_source"] = "wind"
        parsed_data["fetch_timestamp"] = datetime.now().isoformat()

        logger.debug(f"解析完成，结果: {parsed_data}")

    except Exception as e:
        logger.error(f"解析Wind响应数据时发生错误: {e}", exc_info=True)

    return parsed_data


def _call_wind_api(wind_stock_code: str, field_codes: List[str], options: str = "") -> tuple:
    """
    调用Wind Python API获取数据

    Args:
        wind_stock_code: Wind格式的股票代码
        field_codes: Wind字段代码列表
        options: Wind选项字符串

    Returns:
        (error_code, data) 元组
    """
    try:
        # 尝试导入WindPy
        try:
            from WindPy import w
        except ImportError:
            logger.error("WindPy模块未安装。请安装Wind Python API。")
            return (-1, None)

        # 启动Wind API接口
        if not hasattr(w, '_wind_started') or not w._wind_started:
            logger.info("启动Wind API连接...")
            start_result = w.start()

            # 处理start()的返回结果
            if hasattr(start_result, 'ErrorCode'):
                error_code = start_result.ErrorCode
            else:
                error_code = 0  # 假设成功

            if error_code != 0:
                logger.error(f"Wind API启动失败，错误代码: {error_code}")
                return (error_code, None)
            w._wind_started = True
            logger.info("Wind API启动成功")

        # 检查连接状态
        if not w.isconnected():
            logger.error("Wind API未连接，请确保Wind终端已登录")
            return (-1, None)

        # 调用Wind API
        fields_str = ",".join(field_codes)
        logger.debug(f"调用Wind API: w.wss('{wind_stock_code}', '{fields_str}', '{options}')")

        if options:
            result = w.wss(wind_stock_code, fields_str, options)
        else:
            result = w.wss(wind_stock_code, fields_str)

        # 处理不同的返回格式
        if hasattr(result, 'ErrorCode'):
            # 新版本WindPy返回对象格式
            error_code = result.ErrorCode
            data = result
            logger.debug(f"Wind API返回对象格式，错误代码: {error_code}")
        elif isinstance(result, tuple) and len(result) == 2:
            # 旧版本WindPy返回元组格式
            error_code, data = result
            logger.debug(f"Wind API返回元组格式，错误代码: {error_code}")
        else:
            # 未知格式，假设成功
            error_code = 0
            data = result
            logger.debug(f"Wind API返回未知格式: {type(result)}")

        if error_code != 0:
            logger.error(f"Wind API调用失败，股票: {wind_stock_code}, 错误代码: {error_code}")
            return (error_code, None)

        return (error_code, data)

    except Exception as e:
        logger.error(f"调用Wind API时发生异常: {e}", exc_info=True)
        return (-999, None)


def fetch_valuation_data_from_wind(stock_code: str) -> Dict[str, Any] | None:
    """
    从Wind Python API获取估值数据

    Args:
        stock_code: 股票代码

    Returns:
        标准化的估值数据字典，失败时返回None
    """
    try:
        # 确定市场类型
        market_suffix = _get_market_suffix(stock_code)

        # 检查是否应该获取估值数据
        if not should_fetch_data_point(stock_code, "valuation"):
            logger.debug(f"根据配置，跳过股票 {stock_code} 的估值数据获取。")
            return None

        # 获取Wind字段代码
        field_codes = _get_wind_field_codes("valuation", market_suffix)
        if not field_codes:
            logger.warning(f"未找到股票 {stock_code} (市场: {market_suffix}) 的估值字段配置。")
            return None

        # 转换为Wind格式的股票代码
        wind_stock_code = _convert_wind_stock_code(stock_code)

        # 获取Wind选项配置
        config = _load_external_api_config()
        wind_options = ""
        if config:
            options_config = config.get("external_data_wind_options", {})
            options_key = f"valuation{market_suffix}_options"
            wind_options = options_config.get(options_key, "")

            # 动态替换交易日期
            if "tradeDate=" in wind_options:
                recent_trade_date = _get_recent_trade_date()
                # 替换配置中的固定日期为动态日期
                import re
                wind_options = re.sub(r'tradeDate=\d{8}', f'tradeDate={recent_trade_date}', wind_options)
                logger.debug(f"动态设置交易日期为: {recent_trade_date}")

        logger.info(f"准备从Wind获取股票 {stock_code} (Wind代码: {wind_stock_code}) 的估值数据，字段: {field_codes}")

        # 调用Wind API
        error_code, data = _call_wind_api(wind_stock_code, field_codes, wind_options)

        if error_code != 0:
            logger.warning(f"Wind API调用失败，使用模拟数据。股票: {stock_code}, 错误代码: {error_code}")
            # 如果Wind API调用失败，返回模拟数据用于测试
            return _generate_mock_valuation_data(stock_code)

        # 添加详细的数据调试日志
        logger.debug(f"Wind API返回数据类型: {type(data)}")
        if hasattr(data, '__dict__'):
            logger.debug(f"Wind API返回对象属性: {data.__dict__}")
        if hasattr(data, 'Data'):
            logger.debug(f"Wind API Data属性: {data.Data}")
        if hasattr(data, 'Fields'):
            logger.debug(f"Wind API Fields属性: {data.Fields}")

        # 解析Wind响应
        parsed_data = _parse_wind_response(data, field_codes)
        logger.debug(f"解析后的数据: {parsed_data}")

        # 检查是否有有效的估值数据（排除元数据）
        has_valuation_data = any(key.startswith('api_') for key in parsed_data.keys() if parsed_data.get(key) is not None)

        if parsed_data and has_valuation_data:
            logger.info(f"成功从Wind获取股票 {stock_code} 的估值数据。")
            return parsed_data
        else:
            logger.warning(f"Wind数据解析失败或无有效估值数据，使用模拟数据。股票: {stock_code}")
            return _generate_mock_valuation_data(stock_code)

    except Exception as e:
        logger.error(f"从Wind获取股票 {stock_code} 估值数据时发生错误: {e}", exc_info=True)
        # 发生异常时也返回模拟数据，确保系统稳定性
        return _generate_mock_valuation_data(stock_code)


def _generate_mock_valuation_data(stock_code: str) -> Dict[str, Any]:
    """
    生成模拟估值数据（用于测试或Wind API不可用时）

    Args:
        stock_code: 股票代码

    Returns:
        模拟的估值数据字典
    """
    import hashlib
    hash_value = int(hashlib.md5(stock_code.encode()).hexdigest()[:8], 16)

    mock_data = {
        "api_pe_ttm": round(10 + (hash_value % 50), 2),  # 10-60之间的PE
        "api_pb": round(1 + (hash_value % 10) / 10, 2),   # 1-2之间的PB
        # 移除了 api_market_cap，避免与本地数据库市值数据冲突
        "data_source": "wind_mock",
        "fetch_timestamp": datetime.now().isoformat()
    }

    logger.debug(f"生成股票 {stock_code} 的模拟估值数据: {mock_data}")
    return mock_data


def fetch_financial_data_from_wind(stock_code: str) -> Dict[str, Any] | None:
    """
    从Wind API获取财务数据

    Args:
        stock_code: 股票代码

    Returns:
        标准化的财务数据字典，失败时返回None
    """
    try:
        market_suffix = _get_market_suffix(stock_code)

        if not should_fetch_data_point(stock_code, "financial_details"):
            logger.debug(f"根据配置，跳过股票 {stock_code} 的财务数据获取。")
            return None

        field_codes = _get_wind_field_codes("financial_details", market_suffix)
        if not field_codes:
            logger.warning(f"未找到股票 {stock_code} (市场: {market_suffix}) 的财务字段配置。")
            return None

        wind_stock_code = _convert_wind_stock_code(stock_code)

        config = _load_external_api_config()
        wind_options = ""
        if config:
            options_config = config.get("external_data_wind_options", {})
            options_key = f"financial_details{market_suffix}_options"
            wind_options = options_config.get(options_key, "")

        logger.info(f"准备从Wind获取股票 {stock_code} 的财务数据，字段: {field_codes}")

        # TODO: 实际的Wind API调用
        logger.warning(f"Wind财务数据API调用尚未实现，返回模拟数据用于测试。")

        # 模拟财务数据
        import hashlib
        hash_value = int(hashlib.md5(stock_code.encode()).hexdigest()[:8], 16)

        mock_data = {
            "api_roe": round(5 + (hash_value % 20), 2),      # 5-25%的ROE
            "api_roic": round(3 + (hash_value % 15), 2),     # 3-18%的ROIC
            "api_gross_margin": round(20 + (hash_value % 60), 2),  # 20-80%的毛利率
            "api_net_margin": round(5 + (hash_value % 25), 2),     # 5-30%的净利率
            "data_source": "wind_mock",
            "fetch_timestamp": datetime.now().isoformat()
        }

        logger.info(f"成功从Wind获取股票 {stock_code} 的财务数据（模拟）。")
        return mock_data

    except Exception as e:
        logger.error(f"从Wind获取股票 {stock_code} 财务数据时发生错误: {e}", exc_info=True)
        return None


def get_external_stock_details(stock_code: str) -> Dict[str, Any]:
    """
    为单个股票获取外部补充数据的主协调函数

    Args:
        stock_code: 股票代码

    Returns:
        包含所有成功获取的外部数据的字典
    """
    logger.debug(f"开始为股票 {stock_code} 获取外部补充数据...")

    external_data = {}

    config = _load_external_api_config()
    if not config:
        logger.debug(f"外部API配置未加载，跳过股票 {stock_code} 的外部数据获取。")
        return external_data

    # 获取默认数据源
    default_source = config.get("external_data", {}).get("default_source", "wind")

    # 定义要尝试获取的数据点
    data_points = ["valuation", "financial_details", "real_time"]

    for data_point in data_points:
        try:
            if data_point == "valuation":
                valuation_data = fetch_valuation_data_from_wind(stock_code)
                if valuation_data:
                    external_data.update(valuation_data)

            elif data_point == "financial_details":
                financial_data = fetch_financial_data_from_wind(stock_code)
                if financial_data:
                    external_data.update(financial_data)

            # TODO: 添加实时数据获取逻辑
            # elif data_point == "real_time":
            #     real_time_data = fetch_real_time_data_from_wind(stock_code)
            #     if real_time_data:
            #         external_data.update(real_time_data)

        except Exception as e:
            logger.error(f"获取股票 {stock_code} 的 {data_point} 数据时发生错误: {e}", exc_info=True)
            continue

    if external_data:
        logger.info(f"成功为股票 {stock_code} 获取到 {len(external_data)} 个外部数据字段。")
    else:
        logger.debug(f"未为股票 {stock_code} 获取到任何外部数据。")

    return external_data


def initialize_wind_api() -> bool:
    """
    初始化Wind Python API连接

    注意：Wind API需要Wind终端已登录才能正常工作

    Returns:
        True 如果初始化成功，False 否则
    """
    try:
        # 尝试导入WindPy
        try:
            from WindPy import w
        except ImportError:
            logger.error("WindPy模块未安装。请安装Wind Python API。")
            return False

        # 启动Wind API
        logger.info("正在初始化Wind Python API...")
        start_result = w.start()

        # 处理start()的返回结果
        if hasattr(start_result, 'ErrorCode'):
            error_code = start_result.ErrorCode
        else:
            error_code = 0  # 假设成功

        if error_code != 0:
            logger.error(f"Wind API初始化失败，错误代码: {error_code}。请确保Wind终端已登录。")
            return False

        # 标记Wind API已启动
        w._wind_started = True
        logger.info("Wind Python API初始化成功。")
        return True

    except Exception as e:
        logger.error(f"Wind API初始化时发生错误: {e}", exc_info=True)
        return False


def cleanup_wind_api():
    """
    清理Wind Python API连接
    """
    try:
        try:
            from WindPy import w
            if hasattr(w, '_wind_started') and w._wind_started:
                w.stop()
                w._wind_started = False
                logger.info("Wind Python API连接已清理。")
        except ImportError:
            logger.debug("WindPy模块未安装，无需清理。")

    except Exception as e:
        logger.error(f"清理Wind API连接时发生错误: {e}", exc_info=True)


def check_wind_api_status() -> bool:
    """
    检查Wind API状态

    Returns:
        True 如果Wind API可用，False 否则
    """
    try:
        try:
            from WindPy import w
        except ImportError:
            logger.warning("WindPy模块未安装。")
            return False

        # 检查是否已启动
        if hasattr(w, '_wind_started') and w._wind_started:
            return True

        # 尝试启动
        return initialize_wind_api()

    except Exception as e:
        logger.error(f"检查Wind API状态时发生错误: {e}", exc_info=True)
        return False


# 在模块加载时初始化配置
_load_external_api_config()
