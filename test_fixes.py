#!/usr/bin/env python3
"""
测试基金代码后缀转换与JSON解析错误修复功能
"""

import json
import re
import sys
import os

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_json_fix():
    """测试JSON自动修复逻辑"""
    print("=== 测试JSON自动修复逻辑 ===")
    
    # 测试用例1：属性名前有无效字符
    test_json_1 = '''
    {
        "基金代码": "163302.OF",
        - "基金名称": "大摩资源优选混合",
        "基金经理": "刘钊",
        - "风险洞察核心": "核心风险在于..."
    }
    '''
    
    print("原始JSON（有错误）:")
    print(test_json_1)
    
    # 应用修复逻辑
    json_str = test_json_1
    # 修复属性名前的无效字符（如 - "属性名" -> "属性名"）
    json_str = re.sub(r'(\s+)-\s*("[\w\u4e00-\u9fff]+":)', r'\1\2', json_str)
    # 移除尾随逗号
    json_str = re.sub(r',(\s*[}\]])', r'\1', json_str)
    
    print("\n修复后的JSON:")
    print(json_str)
    
    try:
        parsed = json.loads(json_str)
        print("\n✅ JSON解析成功！")
        print(f"解析结果: {parsed}")
        return True
    except json.JSONDecodeError as e:
        print(f"\n❌ JSON解析失败: {e}")
        return False

def test_fund_code_conversion():
    """测试基金代码后缀转换功能"""
    print("\n=== 测试基金代码后缀转换功能 ===")
    
    try:
        from data_fetcher import convert_fund_code_suffix
        
        # 测试用例
        test_cases = [
            ("163302.OF", ["163302.SZ", "163302.SH"]),
            ("169102.OF", ["169102.SZ", "169102.SH"]),
            ("163302.SZ", ["163302.OF"]),
            ("510050.SH", ["510050.OF"]),
            ("invalid", []),
            ("", [])
        ]
        
        all_passed = True
        for input_code, expected in test_cases:
            result = convert_fund_code_suffix(input_code)
            if result == expected:
                print(f"✅ {input_code} -> {result}")
            else:
                print(f"❌ {input_code} -> {result} (期望: {expected})")
                all_passed = False
        
        return all_passed
        
    except ImportError as e:
        print(f"❌ 无法导入convert_fund_code_suffix函数: {e}")
        return False

def test_json_error_handling():
    """测试JSON错误处理逻辑"""
    print("\n=== 测试JSON错误处理逻辑 ===")
    
    # 模拟JSONDecodeError处理
    invalid_json = '''
    {
        "基金代码": "163302.OF",
        "基金名称": "大摩资源优选混合"
        "基金经理": "刘钊"  // 缺少逗号
    }
    '''
    
    try:
        json.loads(invalid_json)
        print("❌ 应该抛出JSONDecodeError")
        return False
    except json.JSONDecodeError as e:
        print(f"✅ 正确捕获JSONDecodeError: {e}")
        print(f"错误位置: 第{e.lineno}行, 第{e.colno}列")
        
        # 模拟错误诊断逻辑
        lines = invalid_json.split('\n')
        start_line = max(0, e.lineno - 3)
        end_line = min(len(lines), e.lineno + 2)
        print("错误附近的JSON内容:")
        for i in range(start_line, end_line):
            line_num = i + 1
            marker = " >>> " if line_num == e.lineno else "     "
            print(f"{marker}第{line_num:2d}行: {lines[i]}")
        
        return True

def main():
    """主测试函数"""
    print("开始测试基金代码后缀转换与JSON解析错误修复功能\n")
    
    results = []
    
    # 测试JSON修复
    results.append(("JSON自动修复", test_json_fix()))
    
    # 测试基金代码转换
    results.append(("基金代码转换", test_fund_code_conversion()))
    
    # 测试JSON错误处理
    results.append(("JSON错误处理", test_json_error_handling()))
    
    # 汇总结果
    print("\n" + "="*50)
    print("测试结果汇总:")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！修复功能正常工作。")
        return True
    else:
        print("⚠️  部分测试失败，需要检查修复实现。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
