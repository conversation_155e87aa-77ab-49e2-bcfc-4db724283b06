{"description": "申万行业分类映射配置文件", "version": "2025", "data_source": "tushare_index_swmember表", "last_updated": "2025-05-26", "total_industries": {"level_1": 31, "level_2": 131, "level_3": 337}, "level_1_industries": {"801010.SI": "农林牧渔", "801030.SI": "基础化工", "801040.SI": "钢铁", "801050.SI": "有色金属", "801080.SI": "电子", "801110.SI": "家用电器", "801120.SI": "食品饮料", "801130.SI": "纺织服饰", "801140.SI": "轻工制造", "801150.SI": "医药生物", "801160.SI": "公用事业", "801170.SI": "交通运输", "801180.SI": "房地产", "801200.SI": "商贸零售", "801210.SI": "社会服务", "801230.SI": "综合", "801710.SI": "建筑材料", "801720.SI": "建筑装饰", "801730.SI": "电力设备", "801740.SI": "国防军工", "801750.SI": "计算机", "801760.SI": "传媒", "801770.SI": "通信", "801780.SI": "银行", "801790.SI": "非银金融", "801880.SI": "汽车", "801890.SI": "机械设备", "801950.SI": "煤炭", "801960.SI": "石油石化", "801970.SI": "环保", "801980.SI": "美容护理"}, "level_2_industries": {"801011.SI": "林业Ⅱ", "801012.SI": "农产品加工", "801014.SI": "饲料", "801015.SI": "渔业", "801016.SI": "种植业", "801017.SI": "养殖业", "801018.SI": "动物保健Ⅱ", "801019.SI": "农业综合Ⅱ", "801032.SI": "化学纤维", "801033.SI": "化学原料", "801034.SI": "化学制品", "801036.SI": "塑料", "801037.SI": "橡胶", "801038.SI": "农化制品", "801039.SI": "非金属材料Ⅱ", "801043.SI": "冶钢原料", "801044.SI": "普钢", "801045.SI": "特钢Ⅱ", "801051.SI": "金属新材料", "801053.SI": "贵金属", "801054.SI": "小金属", "801055.SI": "工业金属", "801056.SI": "能源金属", "801081.SI": "半导体", "801082.SI": "其他电子Ⅱ", "801083.SI": "元件", "801084.SI": "光学光电子", "801085.SI": "消费电子", "801086.SI": "电子化学品Ⅱ", "801111.SI": "白色家电", "801112.SI": "黑色家电", "801113.SI": "小家电", "801114.SI": "厨卫电器", "801115.SI": "照明设备Ⅱ", "801116.SI": "家电零部件Ⅱ", "801117.SI": "其他家电Ⅱ", "801124.SI": "食品加工", "801125.SI": "白酒Ⅱ", "801126.SI": "非白酒", "801127.SI": "饮料乳品", "801128.SI": "休闲食品", "801129.SI": "调味发酵品Ⅱ", "801131.SI": "纺织制造", "801132.SI": "服装家纺", "801133.SI": "饰品", "801141.SI": "包装印刷", "801142.SI": "家居用品", "801143.SI": "造纸", "801145.SI": "文娱用品", "801151.SI": "化学制药", "801152.SI": "生物制品", "801153.SI": "医疗器械", "801154.SI": "医药商业", "801155.SI": "中药Ⅱ", "801156.SI": "医疗服务", "801161.SI": "电力", "801163.SI": "燃气Ⅱ", "801178.SI": "物流", "801179.SI": "铁路公路", "801991.SI": "航空机场", "801992.SI": "航运港口", "801181.SI": "房地产开发", "801183.SI": "房地产服务", "801202.SI": "贸易Ⅱ", "801203.SI": "一般零售", "801204.SI": "专业连锁Ⅱ", "801206.SI": "互联网电商", "801207.SI": "旅游零售Ⅱ", "801216.SI": "体育Ⅱ", "801218.SI": "专业服务", "801219.SI": "酒店餐饮", "801993.SI": "旅游及景区", "801994.SI": "教育", "801231.SI": "综合Ⅱ", "801711.SI": "水泥", "801712.SI": "玻璃玻纤", "801713.SI": "装修建材", "801721.SI": "房屋建设Ⅱ", "801722.SI": "装修装饰Ⅱ", "801723.SI": "基础建设", "801724.SI": "专业工程", "801726.SI": "工程咨询服务Ⅱ", "801731.SI": "电机Ⅱ", "801733.SI": "其他电源设备Ⅱ", "801735.SI": "光伏设备", "801736.SI": "风电设备", "801737.SI": "电池", "801738.SI": "电网设备", "801741.SI": "航天装备Ⅱ", "801742.SI": "航空装备Ⅱ", "801743.SI": "地面兵装Ⅱ", "801744.SI": "航海装备Ⅱ", "801745.SI": "军工电子Ⅱ", "801101.SI": "计算机设备", "801103.SI": "IT服务Ⅱ", "801104.SI": "软件开发", "801764.SI": "游戏Ⅱ", "801765.SI": "广告营销", "801766.SI": "影视院线", "801767.SI": "数字媒体", "801769.SI": "出版", "801995.SI": "电视广播Ⅱ", "801102.SI": "通信设备", "801223.SI": "通信服务", "801782.SI": "国有大型银行Ⅱ", "801783.SI": "股份制银行Ⅱ", "801784.SI": "城商行Ⅱ", "801785.SI": "农商行Ⅱ", "801191.SI": "多元金融", "801193.SI": "证券Ⅱ", "801194.SI": "保险Ⅱ", "801092.SI": "汽车服务", "801093.SI": "汽车零部件", "801095.SI": "乘用车", "801096.SI": "商用车", "801881.SI": "摩托车及其他", "801072.SI": "通用设备", "801074.SI": "专用设备", "801076.SI": "轨交设备Ⅱ", "801077.SI": "工程机械", "801078.SI": "自动化设备", "801951.SI": "煤炭开采", "801952.SI": "焦炭Ⅱ", "801961.SI": "油气开采Ⅱ", "801962.SI": "油服工程", "801963.SI": "炼化及贸易", "801971.SI": "环境治理", "801972.SI": "环保设备Ⅱ", "801981.SI": "个护用品", "801982.SI": "化妆品", "801983.SI": "医疗美容"}, "industry_analysis_framework": {"growth_industries": ["801080.SI", "801730.SI", "801750.SI", "801150.SI", "801980.SI"], "value_industries": ["801780.SI", "801790.SI", "801120.SI", "801180.SI"], "cyclical_industries": ["801040.SI", "801050.SI", "801880.SI", "801890.SI", "801950.SI"], "defensive_industries": ["801160.SI", "801200.SI", "801210.SI"]}, "concept_tags_note": {"description": "概念标签采用开放性提取方式", "extraction_method": "基于深度分析专精V2.5，从基金经理季报文本中动态提取概念标签", "principles": ["所有概念标签必须有季报文本的明确支持", "不主动引入外部概念", "不限制于预设的标签池", "直接从基金经理的原始表述中提取"], "output_location": "在LLM分析结果的JSON中的Manager_Explicit_Concept_Tags字段", "examples": ["半导体自主可控", "消费电子芯片景气上行", "新质生产力", "科技创新", "估值性价比"]}, "industry_mapping_notes": {"data_quality": "基于tushare_index_swmember表的最新数据，包含31个一级行业、131个二级行业、337个三级行业", "update_frequency": "建议每季度更新一次，以保持与申万行业分类标准的同步", "usage_notes": ["使用is_new='Y'条件获取最新的行业分类", "三级行业分类提供最精细的行业划分", "概念标签可用于主题投资分析", "行业分析框架可用于风格分类"]}}