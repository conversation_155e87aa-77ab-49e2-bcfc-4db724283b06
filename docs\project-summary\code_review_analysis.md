# 代码审查分析报告

## 1. `main.py` 模块分析逻辑

`main.py` 是整个基金分析应用的主入口，它负责协调各个子模块（数据获取、LLM 分析、报告生成）的执行流程，并处理日志记录、配置加载以及单只基金和批量基金的分析任务。

### 核心功能：
*   **配置加载与日志设置:** 初始化日志系统，并从 `.env` 文件加载 LLM API 密钥、模型名称和数据库连接信息。
*   **分析规则加载:** 从 `前置rules.md` 文件加载 LLM 分析所需的规则。
*   **单基金分析 (`run_analysis`):**
    *   获取指定基金的最新季报文本数据和前十大持仓数据。
    *   调用 `llm_analyzer` 进行分析。
    *   调用 `report_generator` 保存 Markdown 和 CSV 报告。
*   **批量基金分析 (`run_batch_analysis`):**
    *   从 `fund_list.txt` 读取基金代码列表。
    *   批量调用数据获取、LLM 分析和报告生成流程。
    *   生成并保存批量执行摘要。
*   **错误处理:** 对数据获取失败、LLM 分析失败和报告保存失败进行日志记录和流程控制。

### 职责总结：
`main.py` 作为核心协调器，其分析逻辑清晰地分为初始化、数据准备、智能分析、报告输出和流程控制与摘要几个阶段。它专注于流程编排，而具体的数据操作、LLM 调用和报告格式化则由 `src` 目录下的相应模块负责。

## 2. `src/data_fetcher.py` 模块分析逻辑

`src/data_fetcher.py` 模块主要负责从 PostgreSQL 数据库获取基金相关的各种数据，包括基金季报文本数据、基金持仓数据，并处理市值风格分类。它还包含了读取基金代码列表文件的功能。

### 核心功能：
*   **配置管理:** 从 `market_cap_config.json` 加载市值风格配置，并缓存以提高效率。
*   **数据库连接:** 使用 SQLAlchemy 创建数据库引擎，并管理数据库连接的打开与关闭，确保资源有效利用和错误处理。
*   **基金季报数据获取 (`fetch_fund_data`):**
    *   从 `fund_quarterly_data` 表获取指定基金的最新季报数据。
    *   提取基金名称、经理、市场展望、运作分析和报告期截止日期。
    *   处理数据缺失和异常情况。
*   **基金持仓数据获取 (`fetch_fund_portfolio_data`):**
    *   获取指定基金和报告期日期的前十大持仓数据。
    *   **复杂数据处理:** 包含 A 股和港股的不同数据源和处理逻辑，特别是港股的名称补零和行业市值二次查询。
    *   调用 `_classify_market_cap_style` 为持仓股票分类市值风格。
*   **市值风格分类 (`_classify_market_cap_style`):** 根据股票总市值、市场类型和货币判断股票的市值风格。
*   **批量数据获取 (`fetch_batch_fund_data`):** 为基金代码列表中的每个基金批量获取季报文本和持仓数据，并汇总结果。
*   **基金列表读取 (`read_fund_list_from_file`):** 从文本文件读取基金代码列表。

### 职责总结：
`src/data_fetcher.py` 模块设计注重数据获取的健壮性和灵活性，通过外部化配置、安全的数据库交互、复杂数据处理和全面的错误处理机制，为整个基金分析系统提供了可靠的数据基础。

## 3. `src/llm_analyzer.py` 模块分析逻辑

`src/llm_analyzer.py` 模块的核心职责是与大型语言模型 (LLM) 进行交互，根据提供的基金数据和分析规则生成基金分析报告。它封装了 LLM 调用的细节，包括 Prompt 构建、API 调用和错误处理。

### 核心功能：
*   **Prompt 构建 (`_build_llm_prompt`):**
    *   根据市场展望、运作分析、持仓数据和分析规则，构建结构化且包含所有必要信息的 LLM Prompt 字符串。
    *   确保输入参数的空值安全处理。
*   **Token 估算 (`_estimate_input_tokens`):** 对输入 Prompt 进行初步的 token 数量估算，用于成本和限制监控。
*   **LLM 分析 (`analyze_with_llm`):**
    *   使用 `openai` 库与 LLM API 进行通信。
    *   支持通过环境变量配置模型名称、API Key 和可选的 API Base URL。
    *   记录 LLM API 返回的 token 消耗情况。
    *   **健壮的错误处理:** 捕获并记录各种 LLM API 相关的异常（如连接错误、速率限制、认证错误、超时等），确保模块的稳定性。

### 职责总结：
`src/llm_analyzer.py` 模块是基金分析系统的大脑，专注于 LLM 交互和报告内容的生成。它通过精心的 Prompt 工程和健壮的 API 交互，将原始数据转化为有价值的分析报告。

## 4. `src/report_generator.py` 模块分析逻辑

`src/report_generator.py` 模块专注于基金分析报告的生成和格式校验。它负责将 LLM 生成的原始文本内容转换为结构化的 Markdown 和 CSV 文件，并确保报告符合预期的格式要求。

### 核心功能：
*   **目录管理 (`_ensure_reports_dir_exists`):** 确保报告输出目录存在，如果不存在则创建。
*   **JSON 数据提取 (`_extract_json_from_report`):** 从 LLM 生成的报告文本中提取第七部分的 JSON 数据块，并进行解析。
*   **Markdown 报告保存 (`save_report_md`):**
    *   将 LLM 生成的报告内容保存为 Markdown 文件。
    *   在报告顶部添加基金名称、代码和经理信息。
*   **CSV 报告保存 (`save_report_csv`):**
    *   从 LLM 报告中提取 JSON 表格数据，并将其保存为 CSV 文件。
    *   处理 JSON 提取失败或数据无效的情况。
*   **报告格式校验 (`validate_report_format`):**
    *   对 LLM 生成的报告进行轻量级格式校验。
    *   检查报告中的 JSON 数据块是否可解析。
    *   检查报告文本是否包含预期的主要章节标题。

### 职责总结：
`src/report_generator.py` 模块是基金分析系统的输出层，职责明确，代码结构清晰。它通过封装文件系统操作、提供多格式输出、实现 JSON 提取和轻量级格式校验，确保了 LLM 分析成果能够以结构化和用户友好的方式呈现。