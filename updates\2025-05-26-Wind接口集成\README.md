# Wind API 集成说明

## 概述

本项目集成了Wind Python API（WindPy）来为港股补充估值数据，主要包括PE、PB、市值等关键指标。

## 前置条件

### 1. Wind终端要求
- 必须安装Wind金融终端
- 必须使用有效账户登录Wind终端
- Wind终端需要保持运行状态

### 2. Python环境要求
```bash
# 安装WindPy（通常由Wind终端安装程序提供）
pip install WindPy
```

## 配置说明

### 外部API配置文件：`external_api_config.json`

```json
{
  "external_data": {
    "enabled": true,
    "default_source": "wind",
    "description": "外部数据获取的全局配置，主要用于补充港股数据"
  },
  "external_data_wind": {
    "enabled": true,
    "description": "Wind Python API配置 - 依赖Wind终端登录状态",
    "connection": {
      "auto_start": true,
      "timeout_seconds": 30,
      "description": "Wind API连接配置，需要Wind终端已登录"
    },
    "fetch_valuation_hk": true,
    "fetch_valuation_a": false
  }
}
```

### 主要配置项说明

- `external_data.enabled`: 全局开关，控制是否启用外部数据获取
- `external_data_wind.enabled`: Wind API开关
- `fetch_valuation_hk`: 是否为港股获取估值数据（主要功能）
- `fetch_valuation_a`: 是否为A股获取估值数据（通常禁用，因为本地数据库已有）

## 使用方式

### 1. 自动集成
外部数据获取已集成到主数据流程中，当处理港股数据时会自动调用：

```python
# 在 data_fetcher.py 中自动调用
if EXTERNAL_DATA_AVAILABLE:
    external_data = get_external_stock_details(symbol)
    if external_data:
        # 合并外部数据到股票信息中
        item["pe_ttm"] = external_data.get("api_pe_ttm")
        item["pb"] = external_data.get("api_pb")
```

### 2. 手动调用
也可以直接调用外部数据提供者：

```python
from src.external_data_provider import get_external_stock_details

# 获取港股估值数据
external_data = get_external_stock_details("700.HK")
if external_data:
    pe_ttm = external_data.get("api_pe_ttm")
    pb = external_data.get("api_pb")
    market_cap = external_data.get("api_market_cap")
```

## Wind API字段映射

### 估值数据字段
- `S_VAL_PETTM`: 市盈率TTM → `api_pe_ttm`
- `S_VAL_PBLF`: 市净率LF → `api_pb`
- `S_VAL_MV`: 总市值 → `api_market_cap`

### 股票代码转换
- 内部格式：`700.HK`
- Wind格式：`00700.HK`（自动补零到5位）

## 错误处理

### 1. 容错机制
- Wind API不可用时自动使用模拟数据
- 单个股票获取失败不影响整体流程
- 详细的错误日志记录

### 2. 常见问题
1. **WindPy模块未安装**
   - 错误：`WindPy模块未安装`
   - 解决：安装Wind终端或单独安装WindPy包

2. **Wind终端未登录**
   - 错误：`Wind API初始化失败，错误代码: xxx`
   - 解决：启动Wind终端并登录有效账户

3. **网络连接问题**
   - 错误：`Wind API调用失败`
   - 解决：检查网络连接和Wind服务状态

## 日志监控

### 关键日志信息
```
INFO - 正在初始化Wind Python API...
INFO - Wind Python API初始化成功。
INFO - 准备从Wind获取股票 700.HK 的估值数据
INFO - 成功从Wind获取股票 700.HK 的估值数据。
WARNING - Wind API调用失败，使用模拟数据。
```

### 调试模式
设置日志级别为DEBUG可查看详细的API调用信息：
```python
import logging
logging.getLogger('src.external_data_provider').setLevel(logging.DEBUG)
```

## 性能考虑

### 1. 批量获取
- 目前实现为单个股票逐一获取
- 后续可优化为批量获取以提高性能

### 2. 缓存机制
- 配置文件中预留了缓存相关配置
- 可根据需要实现数据缓存

### 3. 超时控制
- 配置了30秒的API调用超时
- 避免长时间等待影响主流程

## 扩展计划

### 1. 数据源扩展
- 当前专注于Wind API
- 后续考虑集成iFind等其他数据源

### 2. 数据类型扩展
- 当前主要获取估值数据
- 后续可扩展财务数据、实时行情等

### 3. 市场扩展
- 当前主要服务港股
- 可根据需要扩展到其他市场
