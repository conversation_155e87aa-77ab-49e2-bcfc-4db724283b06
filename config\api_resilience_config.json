{"description": "API弹性和错误处理配置，专门针对503错误和并发问题", "retry_config": {"max_attempts": 3, "base_delay": 4, "max_delay": 10, "exponential_multiplier": 1, "description": "重试配置，使用指数退避策略"}, "timeout_config": {"connection_timeout": 60, "read_timeout": 300, "total_timeout": 360, "description": "超时配置，单位为秒"}, "rate_limiting": {"enabled": true, "default_request_interval": 3.0, "burst_protection": {"enabled": true, "max_burst_requests": 1, "burst_window_seconds": 10}, "api_specific": {"gemini": {"request_interval": 3.0, "max_concurrent": 1, "special_handling_503": true}, "openai": {"request_interval": 2.0, "max_concurrent": 2, "special_handling_503": false}}}, "error_handling": {"503_service_unavailable": {"enabled": true, "max_retries": 5, "initial_delay": 10, "max_delay": 60, "backoff_factor": 2, "description": "专门针对503错误的处理策略"}, "429_rate_limit": {"enabled": true, "max_retries": 3, "respect_retry_after": true, "default_delay": 60}, "connection_errors": {"enabled": true, "max_retries": 3, "delay": 5}}, "monitoring": {"log_all_retries": true, "log_timing_stats": true, "alert_on_consecutive_failures": 3, "track_error_patterns": true}, "fallback_strategies": {"auto_reduce_concurrency": {"enabled": true, "trigger_error_count": 3, "reduction_factor": 0.5, "min_workers": 1}, "circuit_breaker": {"enabled": false, "failure_threshold": 5, "recovery_timeout": 300}}}