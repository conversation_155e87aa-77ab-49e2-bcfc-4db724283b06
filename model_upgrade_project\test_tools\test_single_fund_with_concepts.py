#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试单个基金分析流程，包含新的概念提取功能
"""

import logging
import os
import sys
from dotenv import load_dotenv

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import run_analysis, setup_logging

def test_single_fund_analysis():
    """测试单个基金分析，包含概念提取"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    # 加载环境变量
    load_dotenv()
    
    # 检查必要的环境变量
    api_key = os.getenv("LLM_API_KEY")
    model_name = os.getenv("LLM_MODEL_NAME")
    
    if not api_key or not model_name:
        logger.error("请确保在 .env 文件中配置了 LLM_API_KEY 和 LLM_MODEL_NAME")
        return
    
    logger.info("=== 测试单个基金分析流程（包含概念提取） ===")
    
    # 测试基金代码
    test_fund_code = "010350.OF"
    
    logger.info(f"开始分析基金: {test_fund_code}")
    
    try:
        # 运行分析
        run_analysis(test_fund_code)
        logger.info("基金分析完成！")
        
        # 检查概念统计
        from src.concept_extractor import get_concept_statistics
        stats = get_concept_statistics()
        
        logger.info("=== 概念提取结果统计 ===")
        logger.info(f"总概念数量: {stats.get('total_concepts', 0)}")
        logger.info(f"总提取次数: {stats.get('total_extractions', 0)}")
        
        top_concepts = stats.get('top_concepts', [])
        if top_concepts:
            logger.info("高频概念 (前5个):")
            for concept, freq in top_concepts[:5]:
                logger.info(f"  {concept}: {freq}次")
        
    except Exception as e:
        logger.error(f"基金分析过程中发生错误: {e}", exc_info=True)

if __name__ == "__main__":
    test_single_fund_analysis()
