# 🧪 模型升级项目测试指南

## 📋 测试工具结构

```
model_upgrade_project/test_tools/
├── 🔧 核心测试工具
├── quick_model_test.py               # 快速模型测试
├── batch_model_comparison.py         # 批量模型对比
├── enhanced_model_comparison.py      # 增强型模型对比
├── multi_dimensional_comparison.py   # 多维度对比
├── test_enhanced_prompt_v2.py        # 增强提示词测试
│
├── 🔍 专项测试工具
├── test_503_solution.py              # 503错误解决方案测试
├── test_fund_scale_model.py          # 基金规模模型测试
├── test_concept_extraction_v3.py     # 概念提取v3测试
├── test_parallel.py                  # 并行处理测试
├── test_wind_optimization.py         # Wind优化测试
├── test_data_availability.py         # 数据可用性测试
│
├── 📊 案例研究工具
├── test_integrated_scale_analysis.py # 集成规模分析测试
├── test_laisi_case_study.py          # 来思案例研究测试
├── test_single_fund_with_concepts.py # 单基金概念测试
├── simple_multi_test.py              # 简单多基金测试
│
└── 🔄 其他工具
├── model_comparison_test.py          # 模型对比测试
├── random_funds_comparison.py        # 随机基金对比
└── old_model_stability_test.py       # 老模型稳定性测试
```

### 🔥 核心测试文件

#### `test_010350_data_format.py`
**用途**：完整数据流程验证
**重要性**：⭐⭐⭐⭐⭐
**测试范围**：
- 基金数据获取
- 持仓数据格式化
- 申万行业分类（A股+港股）
- 估值统计计算
- Wind API外部数据集成
- 概念标签提取
- LLM Prompt构建
- 数据格式兼容性

**运行方式**：
```bash
# 在tests目录下运行
python test_010350_data_format.py
```

**预期结果**：
- ✅ 获取基金基本信息
- ✅ 10只持仓股票（4只A股 + 6只港股）
- ✅ 申万行业分类完整
- ✅ 港股估值数据通过Wind API获取
- ✅ 估值覆盖率 > 60%
- ✅ 生成完整LLM Prompt

#### `test_wind_real_data.py`
**用途**：Wind API专项测试
**重要性**：⭐⭐⭐⭐
**测试范围**：
- Wind API连接状态
- 真实数据获取验证
- 数据源标识确认
- external_data_provider集成

**运行方式**：
```bash
# 在tests目录下运行
python test_wind_real_data.py
```

**预期结果**：
- ✅ Wind API连接成功
- ✅ 获取真实港股估值数据
- ✅ 数据源标识为"wind"
- ✅ PE(TTM)和PB数据合理

## 📚 存档测试文件

### `archive/test_data_flow.py`
**背景**：早期数据流程测试文件
**状态**：已被`test_010350_data_format.py`替代
**保留原因**：历史参考，展示系统演进过程

### `archive/test_external_data_provider.py`
**背景**：单独测试外部数据提供者
**状态**：功能已集成到主测试文件
**保留原因**：独立模块测试参考

## 🚀 快速测试流程

### 1. 系统完整性测试
```bash
# 在tests目录下运行
python test_010350_data_format.py
```

### 2. Wind API专项测试
```bash
# 在tests目录下运行
python test_wind_real_data.py
```

### 3. 生产环境测试
```bash
# 运行主程序
python main.py
```

## 🔧 故障排除

### 数据库连接问题
- 检查`.env`文件配置
- 确认数据库服务运行状态
- 验证连接字符串格式

### Wind API问题
- 确保Wind终端已启动并登录
- 检查WindPy模块安装：`pip install WindPy`
- 验证网络连接

### 港股数据缺失
- 检查`hk_stock_sw_quarterly`表是否存在
- 确认港股基础数据完整性
- 验证申万行业分类数据

## 📊 测试数据说明

### 测试基金：010350.OF
- **名称**：景顺长城品质长青A
- **特点**：包含A股和港股持仓
- **价值**：理想的混合持仓测试用例

### 测试港股
- **腾讯控股(0700.HK)**：超大市值股
- **泡泡玛特(9992.HK)**：中等市值股
- **金山云(3896.HK)**：小市值股

## 🎯 测试成功标准

### 数据完整性
- [ ] 基金基本信息获取成功
- [ ] 持仓数据格式正确
- [ ] 申万行业分类完整
- [ ] 估值数据覆盖率 > 60%

### 功能正确性
- [ ] Wind API返回真实数据
- [ ] 数据格式与LLM兼容
- [ ] 概念标签提取正常
- [ ] 报告生成成功

### 性能指标
- [ ] 数据获取时间 < 30秒
- [ ] Wind API响应时间 < 5秒/股票
- [ ] 内存使用合理

## 📝 测试日志

测试过程中的重要日志信息：
- `INFO`：正常流程信息
- `WARNING`：非关键问题提醒
- `ERROR`：需要处理的错误
- `DEBUG`：详细调试信息

建议在测试时设置日志级别为`INFO`以获得适当的信息量。

## 🆕 概念提取V3.0测试

### `test_concept_extraction_v3.py`
**用途**：测试新的精简概念提取策略
**重要性**：⭐⭐⭐⭐⭐
**测试范围**：
- 从LLM JSON输出提取核心概念
- 多基金概念积累验证
- 动态知识库更新测试
- 概念统计功能验证

**运行方式**：
```bash
# 在tests目录下运行
python test_concept_extraction_v3.py
```

**预期结果**：
- ✅ 从JSON字段成功提取1-5个核心概念
- ✅ 概念记录到动态知识库
- ✅ 频率统计正确更新
- ✅ 多基金概念积累正常

### `test_single_fund_with_concepts.py`
**用途**：完整基金分析流程（含概念提取）
**重要性**：⭐⭐⭐⭐
**测试范围**：
- 完整的基金分析流程
- 概念提取与主流程集成
- LLM分析后概念记录
- 端到端功能验证

**运行方式**：
```bash
# 在tests目录下运行
python test_single_fund_with_concepts.py
```

**注意事项**：
- 需要配置LLM API密钥
- 需要数据库连接
- 会生成实际的分析报告

**预期结果**：
- ✅ 基金分析成功完成
- ✅ 概念自动提取并记录
- ✅ 生成MD和CSV报告
- ✅ 概念统计信息更新
