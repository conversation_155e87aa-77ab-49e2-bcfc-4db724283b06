**Request for Comments (RFC): 自动化基金季报分析系统 (个人项目版)**

**RFC-001: FoF季报分析助手**

*   **作者**: <PERSON><PERSON> (AI Technical Leader)
*   **日期**: 2025年5月9日
*   **状态**: Proposal
*   **版本**: 1.4 (整合基金持仓数据分析)

**1. 摘要 (Abstract)**

本文档提出一个自动化基金季度报告分析系统的设计方案，旨在通过结合大型语言模型 (LLM) 和预定义的分析框架，实现对基金季报的高效、结构化信息提取与解读。该方案特别针对个人项目进行优化，强调最小可行产品 (MVP) 优先、持续迭代、成本控制、技术选型简化以及良好文档记录的原则。其核心目标是根据用户提供的分析规则文档（[`前置rules.md`](./前置rules.md)），对基金季报的文本内容及同期的前十大持仓数据进行深度综合分析，并生成包含标准文本分析和结构化表格数据的报告。本文档详细描述了从单个基金分析到批量处理的演进路径，明确了MVP阶段的实施标准，并包含了针对个人项目的具体技术选型建议及对技术债的考量。

**2. 引言与背景 (Introduction and Background)**

基金季度报告包含了基金经理对市场、投资组合、业绩及未来展望的关键信息，对于FoF（基金中的基金）研究至关重要。手动分析大量季报耗时耗力且难以保证一致性。本项目旨在开发一个自动化工具，以：
*   **输入**:
    *   基金季报的原始文本数据（例如，从数据库中提取的“市场展望”和“运作分析”等章节）。
    *   对应报告期的基金前十大持仓数据（例如，从 `tushare_fund_portfolio` 表获取）。
    *   一份详细的分析规则与框架文档 ([`前置rules.md`](./前置rules.md))，该文档定义了分析角色、技能、工作流程、策略/风格参考框架、标签体系以及输出格式。该文档将通过Git进行版本控制。
*   **输出**:
    *   结构化的基金分析报告，严格遵循[`前置rules.md`](./前置rules.md)定义的格式，包括对季报文本和持仓数据的综合分析内容（1-6部分的文本分析）和第7部分的增强型表格数据。
*   **项目定位**: 这是一个个人项目，旨在探索和实践自动化内容分析的技术方案。因此，在设计和实施过程中，将优先考虑方案的简洁性、可操作性和成本效益。

**3. 核心指导原则 (Guiding Principles)**

本项目的开发与实施将严格遵循以下用户已确认的原则：

*   **MVP (Minimum Viable Product) 思路**: 从构建一个能成功处理单个基金并生成合格报告的核心流程开始，然后逐步扩展至批量处理，并完善错误处理、监控等辅助功能。MVP阶段以核心功能实现为首要目标。
*   **[`前置rules.md`](./前置rules.md)的持续迭代与版本管理**: 该文档是分析质量的生命线，将通过Git进行版本控制以追踪变更。在实际应用中，需要根据LLM的反馈和分析效果，不断对其进行调整和优化，使其指令更明确、更易于模型准确遵循。当规则发生重大变更时，需考虑对基于旧版规则生成的历史报告的处理方式（例如，进行标记或重新分析）。
*   **成本意识**: 始终关注LLM API的调用成本，在开发和测试阶段使用小批量数据，并根据需要选择合适的模型。
*   **技术选型求简**: 优先选择开发者熟悉且易于管理的技术栈，避免在个人项目中过度工程化。
*   **文档记录**: 即便是个人项目，也应对核心脚本的逻辑、[`前置rules.md`](./前置rules.md)的设计理念、配置项等进行必要的文字记录，这将极大地方便未来的维护和改进工作。

**4. 拟议方案：系统架构与流程 (Proposed Solution: System Architecture and Workflow)**

拟议的自动化分析系统将主要由以下几个阶段构成，从处理单个基金的核心流程（MVP）逐步扩展到批量处理能力：

**4.1 核心需求：单个基金分析流程 (MVP第一步)**

此流程是系统的基础和核心，旨在验证分析框架和LLM在处理单个基金季报时的有效性。MVP阶段聚焦于核心功能的实现。

1.  **输入指定**: 用户提供单个基金的标识（如基金代码 `005682.OF`）。
2.  **数据获取**:
    *   使用指定的基金标识，从配置的数据源（例如 `tusharedb` 数据库的 `fund_quarterly_data` 表）查询该基金最新的季报文本数据。
    *   同时，从相应数据源（例如 `tusharedb` 数据库的 `tushare_fund_portfolio` 表）查询该基金对应报告期的前十大持仓数据。
    *   提取关键信息字段。
3.  **分析框架加载**: 加载并准备 [`前置rules.md`](./前置rules.md) （特定版本）的全部内容作为LLM的核心指令和上下文。
4.  **LLM分析执行**:
    *   将获取到的基金季报文本数据和前十大持仓数据合并，作为输入提供给大型语言模型 (LLM) API。
    *   LLM被指示扮演特定角色并遵循[`前置rules.md`](./前置rules.md)的规则，对提供的全部信息进行综合分析。
5.  **报告生成与输出**:
    *   LLM生成包含对季报文本和持仓数据综合分析的结果。
    *   将LLM生成的完整报告内容保存为单个文本文件。

**4.2 批量处理流程扩展**
(此处内容与版本1.2保持一致，描述阶段一至阶段四的批量处理扩展)
*   **阶段一：准备与配置 (批量)**
*   **阶段二：数据获取与预处理 (批量)**
*   **阶段三：核心分析执行 (可并行，MVP阶段可串行)**
*   **阶段四：报告整合与输出 (批量)**
    *   **格式校验 (轻量级 - MVP标准)**: 确保第7部分的JSON数据块可成功解析，并检查报告是否在结构上包含了[`前置rules.md`](./前置rules.md)所要求的1-6部分的主要章节标题。

*   **阶段五：监控、日志与错误处理 (MVP阶段可简化)**
    1.  **日志记录 (MVP标准)**: 记录关键操作的成功/失败状态和基本的错误信息，满足基本的问题溯源需求即可。详细的参数、耗时和性能日志待后续迭代增强。
    2.  **错误处理 (MVP标准)**: 主要目标是捕获执行过程中的异常，记录错误信息，并允许批处理任务尽可能继续处理下一个基金，而不是实现复杂的错误分类和自动恢复逻辑。
    3.  **执行摘要**: 批量处理结束后的总结。

**5. 关键技术选型考量 (Key Technology Considerations)**

*   **主要编程语言**: Python。MVP阶段鼓励遵循PEP 8及使用Flake8，同时尽可能编写清晰的`docstrings`和适当的`type hints`以增强代码可读性，但首要目标是功能实现。
*   **数据库交互**:
    *   **首选**: 通过已连接的MCP服务器提供的 `query` 工具。
    *   **备选方案**: `SQLAlchemy Core`。
    *   *MVP注*: 实施者在使用这些工具时，需自行关注基本的查询安全（如避免直接拼接SQL字符串）和效率。
*   **依赖管理**: `venv` + `requirements.txt` (通过 `pip freeze` 生成)。
*   **代码质量与测试**:
    *   **Linter**: Flake8。
    *   **单元测试**: MVP阶段以功能实现为主，后续迭代可针对核心、复杂模块补充单元测试。
*   **数据库表结构变更管理**: 初期手动编写和管理SQL变更脚本 (纳入Git版本控制)。
*   **LLM API**: 合适的LLM服务，优先考虑提供与OpenAI API兼容接口的服务。
*   **配置文件**: `.json`, `.yaml`, 或 `.env`。
*   **版本控制**: Git (用于代码和[`前置rules.md`](./前置rules.md))。

**6. 开发与实施策略 (Development and Implementation Strategy)**
(此处内容与版本1.2基本一致，强调MVP先行和持续迭代)
1.  **MVP先行 (核心需求实现)**
2.  **批量处理能力扩展**
3.  **持续迭代与优化**

**7. 技术债考量与迭代计划 (New Section based on feedback)**

本项目在MVP阶段采用了一些简化措施（例如基础版的错误处理和日志、手动数据库schema管理、有限的单元测试）以实现快速启动和核心功能验证。这些简化决策是基于个人项目资源和时间限制的务实选择，但可能会引入一定程度的“技术债”。

*   **识别**: 我们意识到这些简化点与更全面的技术标准（如 `[.roo/rules-architect/rules.md](.roo/rules-architect/rules.md)` 中定义的）存在差距。
*   **迭代计划**: 在MVP成功交付并验证核心价值后，后续的迭代应有计划地逐步偿还这些技术债。例如：
    *   增强错误处理机制，引入更细致的错误分类和重试逻辑。
    *   完善日志系统，考虑引入结构化日志或更详细的性能追踪。
    *   根据需要引入数据库迁移工具。
    *   逐步增加对关键模块的单元测试覆盖。
    *   持续重构代码，提升其对`type hints`和`docstrings`的遵循度。
*   **目标**: 通过持续迭代，使系统在保持个人项目可管理性的同时，逐步提升其健壮性、可维护性和对技术最佳实践的遵循度。

**8. 风险与缓解 (Risks and Mitigations - Brief)**
(此处内容与版本1.2保持一致)

**9. 未来展望 (Future Considerations - Optional)**
(此处内容与版本1.2保持一致)

**10. 征求意见 (Call for Comments)**
(此处内容与版本1.2保持一致)