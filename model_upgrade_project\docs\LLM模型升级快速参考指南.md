# LLM模型升级快速参考指南

## 🚀 快速开始

### 核心原则
1. **以参照文档为唯一标准** - 避免新老模型对比
2. **量化优化目标** - 明确字符数和质量指标
3. **三大核心优化** - 批判性思维、风险具体化、专业表达

### 质量标准 (4/4评分)
- ✅ PE(TTM)使用 ≥3次
- ✅ 风险标识：🔴≥2, 🟡≥1, 🟢≥1  
- ✅ 规模约束分析完整
- ✅ JSON格式33字段完整

---

## 📝 关键提示词模板

### 批判性思维强化
```markdown
🔍 【批判性思维深度】（学习参照文档的穿透式分析）
- **四层分析链条**：现象识别→深层挖掘→矛盾分析→风险预警
- **具体质疑方式**：
  * "基金经理声称X，但从持仓数据看存在Y的矛盾"
  * "声称重点投资方向在实际持仓中的占比仅为Z%"
- **风险具体化**：将抽象风险转化为可量化的具体风险点
```

### 字符数控制
```markdown
✅ 【字符数量要求】
- **强制要求**：目标输出长度必须达到14000-16000字符
- **各部分要求**：
  * 第0部分：450字以上（每个洞察150字）
  * 第1部分：800字以上
  * 第2部分：1800字以上  
  * 第3部分：2500字以上（每个个股300字）
  * 第4-6部分：各600-800字
```

### 专业表达强化
```markdown
**标签化表达方式**：
- **高频使用#标签#**：每个部分至少10个专业标签
- **专业术语密度**：GARP、核心-卫星配置、流动性约束、护城河等
- **风险标识逻辑**：🔴高风险 🟡观察要点 🟢积极信号
```

---

## 🧪 测试流程

### 核心测试工具
```bash
# 快速模型测试
python test_tools/quick_model_test.py

# 批量模型对比
python test_tools/batch_model_comparison.py

# 增强提示词测试
python test_tools/test_enhanced_prompt_v2.py
```
**检查点**：
- [ ] 质量评分 4/4
- [ ] 字符数 12,000-16,000
- [ ] 关键指标达标

### 模型对比工具
```bash
# 增强型模型对比
python test_tools/enhanced_model_comparison.py

# 多维度对比分析
python test_tools/multi_dimensional_comparison.py

# 随机基金对比
python test_tools/random_funds_comparison.py
```
**检查点**：
- [ ] 100%达标率
- [ ] 5个不同类型基金
- [ ] 稳定性验证

### 专项测试工具
```bash
# 503错误解决方案测试
python test_tools/test_503_solution.py

# 基金规模模型测试
python test_tools/test_fund_scale_model.py

# 概念提取v3测试
python test_tools/test_concept_extraction_v3.py

# 并行处理测试
python test_tools/test_parallel.py

# Wind优化测试
python test_tools/test_wind_optimization.py

# 数据可用性测试
python test_tools/test_data_availability.py
```

### 案例研究工具
```bash
# 集成规模分析测试
python test_tools/test_integrated_scale_analysis.py

# 来思案例研究测试
python test_tools/test_laisi_case_study.py

# 单基金概念测试
python test_tools/test_single_fund_with_concepts.py

# 简单多基金测试
python test_tools/simple_multi_test.py
```

---

## ⚡ 常见问题速查

### 字符数不足
**解决方案**：
- 强化各部分最低字符数要求
- 增加个股分析深度（每个300字）
- 丰富数据支撑和案例

### 批判性思维不足
**解决方案**：
- 强化四层分析链条要求
- 增加数据交叉验证指导
- 提供具体质疑模式

### 专业表达不够
**解决方案**：
- 量化标签使用要求（每部分≥10个）
- 提供专业术语库
- 学习参照文档表达方式

### API连接错误
**解决方案**：
- 检查API密钥和BASE地址
- 验证模型名称匹配
- 确认网络连接状态

---

## 📊 成功案例参考

### 优化前后对比
- **字符数**：11,000-13,000 → 12,000-24,000
- **质量评分**：不稳定 → 100%达到4/4分
- **稳定性**：单基金测试 → 5基金全部达标

### 关键成功因素
1. **参照文档学习**：深入分析23k+字符高质量报告
2. **四层分析链条**：显著提升批判性思维深度  
3. **量化要求**：明确字符数和使用频次目标
4. **专业表达**：大量使用#标签#和专业术语

---

## 🔧 实用工具

### 质量检查清单
```markdown
内容质量：
- [ ] PE(TTM) ≥3次
- [ ] 🔴≥2, 🟡≥1, 🟢≥1
- [ ] 规模约束分析
- [ ] JSON 33字段完整

分析深度：
- [ ] 四层分析链条
- [ ] 叙事与持仓矛盾识别
- [ ] 风险量化数据
- [ ] 专业术语充分

专业表达：
- [ ] #标签# ≥10个/部分
- [ ] 投资专业术语丰富
- [ ] 风险标识逻辑清晰
```

### 测试执行清单
```markdown
单基金测试：
1. [ ] 运行测试脚本
2. [ ] 检查4/4评分
3. [ ] 验证字符数
4. [ ] 记录结果

多基金测试：
1. [ ] 选择5个不同基金
2. [ ] 运行稳定性测试
3. [ ] 检查100%达标率
4. [ ] 分析汇总数据
```

---

## 📚 相关文件

### 核心文件
- `src/llm_analyzer.py` - 核心分析器
- `docs/深度分析专精.md` - 分析规则
- `main.py` - 主程序入口

### 测试工具（18个）
**核心测试**：
- `test_tools/quick_model_test.py` - 快速模型测试
- `test_tools/batch_model_comparison.py` - 批量模型对比
- `test_tools/enhanced_model_comparison.py` - 增强型模型对比
- `test_tools/multi_dimensional_comparison.py` - 多维度对比
- `test_tools/test_enhanced_prompt_v2.py` - 增强提示词测试

**专项测试**：
- `test_tools/test_503_solution.py` - 503错误解决方案测试
- `test_tools/test_fund_scale_model.py` - 基金规模模型测试
- `test_tools/test_concept_extraction_v3.py` - 概念提取v3测试
- `test_tools/test_parallel.py` - 并行处理测试
- `test_tools/test_wind_optimization.py` - Wind优化测试
- `test_tools/test_data_availability.py` - 数据可用性测试

**案例研究**：
- `test_tools/test_integrated_scale_analysis.py` - 集成规模分析测试
- `test_tools/test_laisi_case_study.py` - 来思案例研究测试
- `test_tools/test_single_fund_with_concepts.py` - 单基金概念测试
- `test_tools/simple_multi_test.py` - 简单多基金测试

**其他工具**：
- `test_tools/model_comparison_test.py` - 模型对比测试
- `test_tools/random_funds_comparison.py` - 随机基金对比
- `test_tools/old_model_stability_test.py` - 老模型稳定性测试

### 测试数据
- `test_data/test_fund_list_small.txt` - 小型基金列表
- `test_data/test_prompt_*.txt` - 测试提示词样本

### 测试结果（9个子目录）
- `test_results/new_model_tests/` - 新模型测试结果
- `test_results/model_comparison_analysis/` - 模型对比分析
- `test_results/enhanced_prompt_tests/` - 增强提示词测试结果
- `test_results/early_comparison_tests/` - 早期对比测试结果
- `test_results/quick_test_samples/` - 快速测试样本
- `test_results/comparison_results/` - 测试总结报告
- `test_results/multi_test_results/` - 新老模型对比测试结果
- `OVERALL_SUMMARY_*.md` - 汇总统计报告

---

## 💡 专业术语库

### 投资术语
- GARP、核心-卫星配置、流动性约束
- 护城河、竞争壁垒、定价权、客户粘性

### 财务术语  
- PE(TTM)、PB、ROE、ROIC
- 估值修复、戴维斯双杀

### 风险术语
- 策略漂移、集中度风险、流动性风险
- 规模约束、瓶颈股、传导机制

### 市场术语
- 技术迭代、国产替代、景气度
- 渗透率、叙事关联性、风格暴露

---

**版本**: v1.0 | **更新**: 2025-06-18 | **用途**: 日常模型升级参考
