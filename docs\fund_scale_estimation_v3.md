# 基金规模上限估算动态模型 V3.0

## 概述

基于"一张纸"速查表的基金规模约束计算模型，通过分析基金前十大重仓股的市值和权重，动态估算基金的理论规模上限。

## 核心公式

```
监管上限: N_reg = (C * reg_limit) / w
流动性上限: N_liq = (C * α * trade_limit * d) / w  
基金规模上限: N_max = min(N_reg, N_liq) 中的最小值
```

**参数说明:**
- `C`: 股票市值 (亿元)
- `w`: 股票在基金中的权重 (小数形式)
- `α`: 流动性因子 (根据市值动态查表，**可配置调整**)
- `reg_limit`: 监管持股比例上限 (**可配置，默认5%**)
- `trade_limit`: 每日可占成交额比例上限 (**可配置，默认10%**)
- `d`: 允许的调仓天数 (**可配置，默认5天**)

> **重要说明**: 所有计算参数均从配置文件 `config/market_cap_config.json` 动态读取，支持灵活调整以适应不同市场环境和监管要求。

## 流动性因子α查找表

| 市值档位 | 市值范围 | α值 | 说明 |
|---------|---------|-----|------|
| 微盘 | < 20亿元 | 0.038 | 小公司流动性相对较高但总成交额小 |
| 小盘 | 20-100亿元 | 0.023 | 中等流动性 |
| 中盘 | 100-500亿元 | 0.012 | 较好流动性 |
| 大盘 | 500-1000亿元 | 0.008 | 良好流动性 |
| 超大盘 | ≥ 1000亿元 | 0.004 | 最佳流动性但α值较低 |

## 计算流程

1. **数据获取**: 提取基金前十大重仓股的市值和权重
2. **参数查找**: 根据市值查找对应的流动性因子α
3. **双重约束计算**: 
   - 监管约束: 不能持有超过单一股票市值的5%
   - 流动性约束: 考虑交易活跃度和每日交易限制
4. **瓶颈识别**: 取两个约束中更严格的一个
5. **整体限制**: 所有重仓股中最严格的限制即为基金规模上限

## 文件结构

```
config/
├── fund_scale_config.json          # 动态模型配置文件

modules/
├── fund_scale_estimator.py         # 核心计算模块
└── fund_scale_integration.py       # 集成接口模块

docs/
└── fund_scale_estimation_v3.md     # 本文档

# 测试和示例
tests/test_fund_scale_model.py      # 模型测试脚本
tools/guides/fund_scale_integration_guide.py  # 集成指南
```

## 使用方法

### 快速使用

```python
from modules.fund_scale_integration import estimate_fund_scale, get_scale_summary

# 快速获取规模估算值
scale_limit = estimate_fund_scale(portfolio_data)
print(f"基金规模上限: {scale_limit:.1f}亿元")

# 获取LLM格式化摘要
summary = get_scale_summary(portfolio_data)
print(summary)
```

### 详细使用

```python
from modules.fund_scale_integration import FundScaleIntegration

integration = FundScaleIntegration()

# 详细估算
result = integration.estimate_fund_scale_simple(portfolio_data)
print(f"规模上限: {result['fund_scale_limit_yi']:.1f}亿元")
print(f"瓶颈股票: {result['bottleneck_stock']['name']}")

# 增强组合数据
enhanced_data = integration.add_scale_info_to_portfolio(portfolio_data)
```

## 输入数据格式

```python
portfolio_data = {
    "fund_name": "基金名称",
    "top_holdings": [
        {
            "stock_name": "股票名称",
            "market_cap": 1000,        # 市值
            "market_cap_unit": "亿元",  # 市值单位
            "weight": 0.05             # 权重 (5%)
        },
        # ... 更多持仓
    ]
}
```

## 输出结果格式

```python
{
    "fund_scale_limit_yi": 50.0,           # 基金规模上限(亿元)
    "bottleneck_stock": {                  # 瓶颈股票信息
        "name": "某股票",
        "market_cap_yi": 100.0,
        "weight": 0.08,
        "constraint_type": "流动性约束"
    },
    "constraint_summary": {                # 约束分布统计
        "total_stocks": 10,
        "regulatory_constraints": 2,
        "liquidity_constraints": 8
    },
    "model_version": "3.0"
}
```

## 参数配置详解

### 可调配置参数

所有关键参数均可通过 `config/market_cap_config.json` 文件进行调整：

```json
{
  "fund_scale_parameters": {
    "reg_limit": 0.05,      // 监管持股比例上限 (5%)
    "trade_limit": 0.10,    // 每日可占成交额比例上限 (10%)
    "d": 5,                 // 允许的调仓天数 (主动型基金默认5天)
  },
  "market_cap_styles": {
    "A-Share": {
      "alpha_values": {
        "Mega": 0.004,      // 超大盘α值 (0.4%)
        "Large": 0.008,     // 大盘α值 (0.8%)
        "Mid": 0.012,       // 中盘α值 (1.2%)
        "Small": 0.023,     // 小盘α值 (2.3%)
        "Micro": 0.038      // 微盘α值 (3.8%)
      }
    }
  }
}
```

### 参数调整指南

1. **reg_limit (监管限制)**：
   - 当前设置：5% (0.05)
   - 调整建议：根据监管政策变化调整
   - 影响：值越小，监管约束越严格

2. **trade_limit (交易限制)**：
   - 当前设置：10% (0.10)
   - 调整建议：根据市场流动性环境调整
   - 影响：值越小，流动性约束越严格

3. **d (调仓天数)**：
   - 当前设置：5天
   - 调整建议：主动型基金5-10天，被动型基金1-3天
   - 影响：天数越多，流动性约束越宽松

4. **α值 (流动性因子)**：
   - 当前设置：按市值档位区分
   - 调整建议：建议按季度根据实际换手率数据更新
   - 影响：α值越高，流动性约束越宽松

## 实际案例：莱斯信息计算详解

### 案例背景
- **基金**：017488.OF (嘉实信息产业A)
- **瓶颈股票**：莱斯信息 (688631.SH)
- **计算结果**：基金规模上限 7.4亿元

### 基础数据
```
股票名称：莱斯信息
权重：4.46% (0.0446)
流通市值：55.4亿元
总市值：147.8亿元
市值风格：中盘 (100-500亿元)
α值：0.012 (中盘股配置)
```

### 详细计算过程

#### 第一步：参数获取
```python
# 从配置文件读取参数
reg_limit = 0.05      # 5% 监管持股比例上限
trade_limit = 0.10    # 10% 每日可占成交额比例上限
d = 5                 # 5天调仓天数
alpha = 0.012         # 中盘股α值 (1.2%)
```

#### 第二步：监管上限计算
```python
N_reg = (总市值 * reg_limit) / 权重
N_reg = (147.8亿 * 0.05) / 0.0446
N_reg = 7.39亿 / 0.0446
N_reg = 165.7亿元
```

#### 第三步：流动性上限计算
```python
N_liq = (流通市值 * alpha * trade_limit * d) / 权重
N_liq = (55.4亿 * 0.012 * 0.10 * 5) / 0.0446
N_liq = (55.4 * 0.006) / 0.0446
N_liq = 0.3324亿 / 0.0446
N_liq = 7.4亿元
```

#### 第四步：最终结果
```python
N_max = min(N_reg, N_liq)
N_max = min(165.7亿, 7.4亿)
N_max = 7.4亿元
瓶颈类型：流动性约束
```

### 逻辑验证
如果基金规模为7.4亿元：
- 持有莱斯信息市值 = 7.4亿 × 4.46% = 0.33亿元
- 占流通市值比例 = 0.33亿 / 55.4亿 = 0.6%
- 流动性约束允许比例 = 0.10 × 0.012 × 5 = 0.6%
- **验证通过**：0.6% = 0.6% ✅

### 参数敏感性分析
1. **如果α值调整为0.015**：N_liq = 9.25亿元
2. **如果调仓天数调整为3天**：N_liq = 4.44亿元
3. **如果交易限制调整为15%**：N_liq = 11.1亿元

## 动态特性

1. **市值依赖**: 每只股票的市值和权重都是动态的
2. **α值查表**: 流动性因子根据市值动态确定
3. **参数可配置**: 监管限制、交易限制、调仓天数等可调整
4. **未来扩展**: 可根据基金实际换手率动态计算调仓天数

## 测试验证

运行测试脚本验证模型功能:

```bash
python test_fund_scale_model.py      # 基础功能测试
python example_fund_scale_usage.py   # 使用示例演示
```

## 集成到主系统

在现有的基金分析流程中添加规模估算:

```python
def analyze_fund_with_scale(fund_data):
    # 原有分析逻辑...
    analysis_result = perform_basic_analysis(fund_data)
    
    # 添加规模估算
    integration = FundScaleIntegration()
    enhanced_data = integration.add_scale_info_to_portfolio(fund_data)
    analysis_result["scale_estimation"] = enhanced_data["fund_scale_estimation"]
    
    return analysis_result
```

## 注意事项

1. **数据质量**: 估算结果依赖于准确的市值和权重数据
2. **模型假设**: 基于静态持仓分析，实际操作中可能有其他约束
3. **参数调整**: 可根据市场环境和监管变化调整配置参数
4. **适用范围**: 主要适用于主动管理的股票型基金

## 版本历史

- **V3.0**: 实现完整的动态模型，支持α值查表和双重约束计算
- **V2.5**: 简化版本，使用固定参数
- **V2.0**: 初始版本概念设计
