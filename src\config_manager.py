# 配置管理器
# 统一管理并行处理和其他配置

import json
import os
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class ConfigManager:
    """
    配置管理器
    负责加载和管理各种配置文件
    """
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = config_dir
        self.configs = {}
        self._ensure_config_dir()
    
    def _ensure_config_dir(self):
        """确保配置目录存在"""
        if not os.path.exists(self.config_dir):
            os.makedirs(self.config_dir)
            logger.info(f"创建配置目录: {self.config_dir}")
    
    def load_config(self, config_name: str, default_config: Optional[Dict] = None) -> Dict[str, Any]:
        """
        加载配置文件
        
        Args:
            config_name: 配置文件名（不含扩展名）
            default_config: 默认配置（如果文件不存在时使用）
        
        Returns:
            配置字典
        """
        config_path = os.path.join(self.config_dir, f"{config_name}.json")
        
        try:
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                logger.info(f"成功加载配置文件: {config_path}")
                self.configs[config_name] = config
                return config
            else:
                if default_config:
                    logger.warning(f"配置文件不存在: {config_path}，使用默认配置")
                    self.save_config(config_name, default_config)
                    self.configs[config_name] = default_config
                    return default_config
                else:
                    logger.error(f"配置文件不存在且无默认配置: {config_path}")
                    return {}
        
        except json.JSONDecodeError as e:
            logger.error(f"配置文件JSON格式错误: {config_path}, 错误: {e}")
            return default_config or {}
        except Exception as e:
            logger.error(f"加载配置文件失败: {config_path}, 错误: {e}")
            return default_config or {}
    
    def save_config(self, config_name: str, config: Dict[str, Any]) -> bool:
        """
        保存配置文件
        
        Args:
            config_name: 配置文件名（不含扩展名）
            config: 配置字典
        
        Returns:
            是否保存成功
        """
        config_path = os.path.join(self.config_dir, f"{config_name}.json")
        
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            logger.info(f"配置文件保存成功: {config_path}")
            self.configs[config_name] = config
            return True
        except Exception as e:
            logger.error(f"保存配置文件失败: {config_path}, 错误: {e}")
            return False
    
    def get_config(self, config_name: str) -> Dict[str, Any]:
        """获取已加载的配置"""
        return self.configs.get(config_name, {})
    
    def update_config(self, config_name: str, updates: Dict[str, Any], save: bool = True) -> bool:
        """
        更新配置
        
        Args:
            config_name: 配置文件名
            updates: 要更新的配置项
            save: 是否立即保存到文件
        
        Returns:
            是否更新成功
        """
        if config_name not in self.configs:
            logger.error(f"配置 {config_name} 未加载，无法更新")
            return False
        
        try:
            # 深度更新配置
            self._deep_update(self.configs[config_name], updates)
            
            if save:
                return self.save_config(config_name, self.configs[config_name])
            return True
        except Exception as e:
            logger.error(f"更新配置失败: {config_name}, 错误: {e}")
            return False
    
    def _deep_update(self, base_dict: Dict, update_dict: Dict):
        """深度更新字典"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value



class ParallelConfig:
    """
    并行处理配置管理器
    提供便捷的并行配置访问接口
    """
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.config_name = "parallel_config"
        self._load_config()
    
    def _load_config(self):
        """加载并行配置"""
        default_config = {
            "parallel_processing": {
                "enabled": False,
                "max_workers": 2,
                "description": "LLM并行处理配置"
            },
            "fallback": {
                "auto_disable_on_error": True,
                "error_threshold": 3
            },
            "monitoring": {
                "log_performance": True,
                "save_timing_stats": True
            }
        }
        
        self.config = self.config_manager.load_config(self.config_name, default_config)
    
    def is_enabled(self) -> bool:
        """是否启用并行处理"""
        return self.config.get("parallel_processing", {}).get("enabled", False)
    
    def get_max_workers(self) -> int:
        """获取最大并发数"""
        return self.config.get("parallel_processing", {}).get("max_workers", 2)
    
    def enable_parallel(self, max_workers: Optional[int] = None, save: bool = True) -> bool:
        """
        启用并行处理
        
        Args:
            max_workers: 最大并发数（可选）
            save: 是否保存配置
        
        Returns:
            是否设置成功
        """
        updates = {
            "parallel_processing": {
                "enabled": True
            }
        }
        
        if max_workers is not None:
            updates["parallel_processing"]["max_workers"] = max_workers
        
        return self.config_manager.update_config(self.config_name, updates, save)
    
    def disable_parallel(self, save: bool = True) -> bool:
        """
        禁用并行处理
        
        Args:
            save: 是否保存配置
        
        Returns:
            是否设置成功
        """
        updates = {
            "parallel_processing": {
                "enabled": False
            }
        }
        
        return self.config_manager.update_config(self.config_name, updates, save)
    
    def set_max_workers(self, max_workers: int, save: bool = True) -> bool:
        """
        设置最大并发数
        
        Args:
            max_workers: 最大并发数
            save: 是否保存配置
        
        Returns:
            是否设置成功
        """
        updates = {
            "parallel_processing": {
                "max_workers": max_workers
            }
        }
        
        return self.config_manager.update_config(self.config_name, updates, save)
    
    def apply_preset(self, preset_name: str, save: bool = True) -> bool:
        """
        应用预设配置
        
        Args:
            preset_name: 预设名称 (conservative, balanced, aggressive)
            save: 是否保存配置
        
        Returns:
            是否应用成功
        """
        presets = {
            "conservative": {"enabled": True, "max_workers": 2},
            "balanced": {"enabled": True, "max_workers": 2},
            "aggressive": {"enabled": True, "max_workers": 5},
            "disabled": {"enabled": False, "max_workers": 2}
        }
        
        if preset_name not in presets:
            logger.error(f"未知的预设配置: {preset_name}")
            return False
        
        preset = presets[preset_name]
        updates = {
            "parallel_processing": preset
        }
        
        success = self.config_manager.update_config(self.config_name, updates, save)
        if success:
            logger.info(f"已应用预设配置: {preset_name} - {preset}")
        
        return success
    
    def get_api_recommendation(self, api_provider: str) -> Dict[str, Any]:
        """
        获取API提供商的推荐配置
        
        Args:
            api_provider: API提供商名称
        
        Returns:
            推荐配置
        """
        api_limits = self.config.get("parallel_processing", {}).get("api_limits", {})
        return api_limits.get(api_provider.lower(), {
            "recommended_max_workers": 2,
            "rate_limit_per_minute": 60
        })
    
    def get_current_settings(self) -> Dict[str, Any]:
        """获取当前并行设置"""
        return {
            "enabled": self.is_enabled(),
            "max_workers": self.get_max_workers(),
            "config": self.config.get("parallel_processing", {})
        }

# 全局配置管理器实例
_config_manager = None
_parallel_config = None

def get_config_manager() -> ConfigManager:
    """获取全局配置管理器"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager

def get_parallel_config() -> ParallelConfig:
    """获取并行配置管理器"""
    global _parallel_config
    if _parallel_config is None:
        _parallel_config = ParallelConfig(get_config_manager())
    return _parallel_config



