"""
基金规模估算动态模型测试脚本
验证"一张纸"速查表模型的计算逻辑

测试场景：
1. 单只股票的监管约束和流动性约束计算
2. 不同市值档位的α值查找
3. 基金整体规模上限估算
4. 结果格式化输出
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.fund_scale_estimator import FundScaleEstimator

def test_alpha_lookup():
    """测试α值查找功能"""
    print("=== 测试α值查找功能 ===")
    
    estimator = FundScaleEstimator()
    
    test_cases = [
        (15, "微盘"),
        (50, "小盘"), 
        (200, "中盘"),
        (800, "大盘"),
        (1500, "超大盘")
    ]
    
    for market_cap, expected_tier in test_cases:
        alpha = estimator.get_alpha_by_market_cap(market_cap)
        print(f"市值 {market_cap}亿元 -> α = {alpha:.3f} ({expected_tier})")
    
    print()

def test_single_stock_calculation():
    """测试单只股票计算"""
    print("=== 测试单只股票规模限制计算 ===")
    
    estimator = FundScaleEstimator()
    
    # 测试案例：不同类型的约束
    test_cases = [
        {"market_cap_yi": 50, "weight": 0.08, "name": "小盘股(高权重)"},
        {"market_cap_yi": 800, "weight": 0.03, "name": "大盘股(低权重)"},
        {"market_cap_yi": 200, "weight": 0.05, "name": "中盘股(中等权重)"},
        {"market_cap_yi": 15, "weight": 0.02, "name": "微盘股(低权重)"}
    ]
    
    for case in test_cases:
        result = estimator.calculate_single_stock_limit(
            case["market_cap_yi"], 
            case["weight"]
        )
        
        print(f"\n{case['name']}:")
        print(f"  市值: {result['market_cap_yi']}亿元, 权重: {result['weight']:.1%}")
        print(f"  α值: {result['alpha']:.3f}")
        print(f"  监管上限: {result['n_reg']:.1f}亿元")
        print(f"  流动性上限: {result['n_liq']:.1f}亿元")
        print(f"  最终限制: {result['n_max']:.1f}亿元 ({result['bottleneck_type']})")
    
    print()

def test_fund_scale_estimation():
    """测试基金整体规模估算"""
    print("=== 测试基金整体规模估算 ===")
    
    estimator = FundScaleEstimator()
    
    # 模拟一只基金的前十大重仓股
    mock_holdings = [
        {"stock_name": "贵州茅台", "market_cap_yi": 2500, "weight": 0.08},
        {"stock_name": "宁德时代", "market_cap_yi": 1200, "weight": 0.06},
        {"stock_name": "比亚迪", "market_cap_yi": 800, "weight": 0.05},
        {"stock_name": "五粮液", "market_cap_yi": 600, "weight": 0.04},
        {"stock_name": "中国平安", "market_cap_yi": 900, "weight": 0.04},
        {"stock_name": "招商银行", "market_cap_yi": 1100, "weight": 0.03},
        {"stock_name": "美的集团", "market_cap_yi": 400, "weight": 0.03},
        {"stock_name": "隆基绿能", "market_cap_yi": 300, "weight": 0.025},
        {"stock_name": "药明康德", "market_cap_yi": 200, "weight": 0.025},
        {"stock_name": "某小盘股", "market_cap_yi": 50, "weight": 0.02}
    ]
    
    result = estimator.estimate_fund_scale_limit(mock_holdings)
    
    print("基金规模估算结果:")
    print(f"整体规模上限: {result['fund_scale_limit_yi']:.1f}亿元")
    print(f"瓶颈股票: {result['bottleneck_stock']['name']}")
    print(f"约束类型: {result['bottleneck_stock']['constraint_type']}")
    print(f"约束分布: 监管约束{result['constraint_summary']['regulatory_constraints']}只, "
          f"流动性约束{result['constraint_summary']['liquidity_constraints']}只")
    
    print("\n详细的LLM格式化输出:")
    print(estimator.format_result_for_llm(result))
    
    print("\n各股票详细限制:")
    for stock in result['all_stock_limits']:
        print(f"  {stock['stock_name']}: {stock['n_max']:.1f}亿元 ({stock['bottleneck_type']})")

def test_edge_cases():
    """测试边界情况"""
    print("\n=== 测试边界情况 ===")
    
    estimator = FundScaleEstimator()
    
    # 测试极端市值
    print("极端市值测试:")
    extreme_cases = [
        (5, "极小市值"),
        (5000, "极大市值")
    ]
    
    for market_cap, desc in extreme_cases:
        alpha = estimator.get_alpha_by_market_cap(market_cap)
        print(f"  {desc} {market_cap}亿元 -> α = {alpha:.3f}")

if __name__ == "__main__":
    print("基金规模估算动态模型测试")
    print("=" * 50)
    
    try:
        test_alpha_lookup()
        test_single_stock_calculation()
        test_fund_scale_estimation()
        test_edge_cases()
        
        print("\n✅ 所有测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
