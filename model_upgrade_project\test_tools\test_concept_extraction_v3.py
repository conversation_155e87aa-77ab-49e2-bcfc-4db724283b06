#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的精简概念提取策略
测试从LLM JSON输出中提取核心概念并记录到动态知识库的功能
"""

import logging
import json
import os
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.concept_extractor import record_and_save_core_concepts, get_concept_statistics

def setup_logging():
    """配置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - [%(name)s.%(funcName)s:%(lineno)d] - %(message)s',
        handlers=[logging.StreamHandler()]
    )

def test_concept_extraction_from_json():
    """测试从模拟的LLM JSON输出中提取核心概念"""
    logger = logging.getLogger(__name__)

    # 模拟LLM报告的JSON数据（基于实际报告格式）
    mock_json_data = {
        "FundCode": "010350.OF",
        "FundName": "景顺长城品质长青A",
        "ManagerName": "农冰立",
        "ReportDate": "2025-03-31",
        "V2_Summ_FocusExp_Derived": "#AI驱动的科技创新与应用(含消费电子及军工电子)#",
        "Manager_Explicit_Concept_Tags_FromReport": "国产大模型崛起, AI产业趋势, 端侧变现空间 (智能手机，汽车，机器人), 消费电子创新 (光学), 军工电子 (行业恢复), 业绩优秀和估值合理",
        "V1_CoreLogic_Snippet": "本基金的核心投资逻辑围绕国产AI大模型崛起及其在端侧应用的长期变现潜力展开...",
        "AnalysisTimestamp": "2025-05-29T01:11:29Z"
    }

    logger.info("=== 测试新的精简概念提取策略 ===")

    # 模拟从JSON中提取核心概念的逻辑（与main.py中的逻辑一致）
    core_concepts_to_record = []
    fund_code_for_concepts = mock_json_data.get("FundCode", "UNKNOWN_FUND")

    # 1. 从 V2_Summ_FocusExp_Derived 提取 (通常是1个核心概括性标签)
    focus_exp_tag = mock_json_data.get("V2_Summ_FocusExp_Derived")
    if focus_exp_tag and isinstance(focus_exp_tag, str):
        cleaned_focus_exp_tag = focus_exp_tag.strip('# ')  # 去除 # 号和多余空格
        if cleaned_focus_exp_tag:
            core_concepts_to_record.append(cleaned_focus_exp_tag)

    # 2. 从 Manager_Explicit_Concept_Tags_FromReport 提取 (基金经理明确提及的，取前 N 个，例如 N=2)
    manager_tags_str = mock_json_data.get("Manager_Explicit_Concept_Tags_FromReport")
    if manager_tags_str and isinstance(manager_tags_str, str):
        manager_tags_list = [tag.strip() for tag in manager_tags_str.split(',') if tag.strip()]

        # 定义从经理标签中提取的最大数量
        MAX_MANAGER_TAGS = 2
        tags_added_from_manager = 0
        for tag in manager_tags_list:
            if tags_added_from_manager < MAX_MANAGER_TAGS:
                if tag not in core_concepts_to_record:  # 避免与 focus_exp_tag 重复
                    core_concepts_to_record.append(tag)
                    tags_added_from_manager += 1
            else:
                break

    # 去重（以防万一）
    core_concepts_to_record = list(dict.fromkeys(core_concepts_to_record))

    logger.info(f"从JSON顶层字段提取到的核心概念: {core_concepts_to_record}")

    # 测试记录核心概念到动态知识库
    if core_concepts_to_record:
        logger.info("正在测试记录核心概念到动态知识库...")
        record_and_save_core_concepts(core_concepts_to_record, fund_code_for_concepts)
        logger.info("核心概念记录完成。")
    else:
        logger.warning("未提取到核心概念，跳过记录测试。")

def test_multiple_funds():
    """测试多个基金的概念提取和积累"""
    logger = logging.getLogger(__name__)

    logger.info("=== 测试多个基金的概念积累 ===")

    # 模拟多个基金的数据
    mock_funds_data = [
        {
            "FundCode": "001148.OF",
            "V2_Summ_FocusExp_Derived": "#新能源汽车产业链投资#",
            "Manager_Explicit_Concept_Tags_FromReport": "新能源汽车, 锂电池, 智能驾驶, 产业链整合"
        },
        {
            "FundCode": "005094.OF",
            "V2_Summ_FocusExp_Derived": "#医药创新与消费升级#",
            "Manager_Explicit_Concept_Tags_FromReport": "创新药, CXO, 医疗器械, 消费医疗"
        },
        {
            "FundCode": "008186.OF",
            "V2_Summ_FocusExp_Derived": "#AI驱动的科技创新与应用#",
            "Manager_Explicit_Concept_Tags_FromReport": "人工智能, 云计算, 数据中心, 算力基础设施"
        }
    ]

    for fund_data in mock_funds_data:
        core_concepts = []
        fund_code = fund_data.get("FundCode", "UNKNOWN")

        # 提取概念
        focus_tag = fund_data.get("V2_Summ_FocusExp_Derived", "").strip('# ')
        if focus_tag:
            core_concepts.append(focus_tag)

        manager_tags = fund_data.get("Manager_Explicit_Concept_Tags_FromReport", "")
        if manager_tags:
            tags_list = [tag.strip() for tag in manager_tags.split(',') if tag.strip()]
            for tag in tags_list[:2]:  # 取前2个
                if tag not in core_concepts:
                    core_concepts.append(tag)

        logger.info(f"基金 {fund_code} 提取的概念: {core_concepts}")

        # 记录到知识库
        if core_concepts:
            record_and_save_core_concepts(core_concepts, fund_code)

def test_concept_statistics():
    """测试概念统计功能"""
    logger = logging.getLogger(__name__)

    logger.info("=== 测试概念统计功能 ===")

    try:
        stats = get_concept_statistics()
        logger.info("概念统计信息:")
        logger.info(f"  总概念数量: {stats.get('total_concepts', 0)}")
        logger.info(f"  总提取次数: {stats.get('total_extractions', 0)}")
        logger.info(f"  最后更新时间: {stats.get('last_updated', 'N/A')}")

        top_concepts = stats.get('top_concepts', [])
        if top_concepts:
            logger.info("  高频概念 (前10个):")
            for concept, freq in top_concepts[:10]:
                logger.info(f"    {concept}: {freq}次")

        recent_concepts = stats.get('recent_concepts', [])
        if recent_concepts:
            logger.info(f"  今日新增概念: {recent_concepts}")
        else:
            logger.info("  今日无新增概念")

    except Exception as e:
        logger.error(f"获取概念统计信息失败: {e}", exc_info=True)

def main():
    """主测试函数"""
    setup_logging()
    logger = logging.getLogger(__name__)

    logger.info("开始测试新的精简概念提取策略...")

    try:
        # 测试单个基金的概念提取
        test_concept_extraction_from_json()

        print("\n" + "="*50 + "\n")

        # 测试多个基金的概念积累
        test_multiple_funds()

        print("\n" + "="*50 + "\n")

        # 测试概念统计
        test_concept_statistics()

        logger.info("所有测试完成！")

    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}", exc_info=True)

if __name__ == "__main__":
    main()
