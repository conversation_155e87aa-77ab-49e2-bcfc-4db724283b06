#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
老模型稳定性测试工具
先测试老模型的稳定性和质量基线
"""

import os
import json
import logging
import time
import statistics
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv

# 导入项目模块
from src.llm_analyzer import analyze_with_llm
from src.data_fetcher import fetch_fund_data, fetch_fund_portfolio_data
from src.external_data_provider import get_external_stock_details
from src.report_generator import _extract_json_from_report as extract_json_from_report

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_analysis_rules():
    """加载分析规则"""
    try:
        with open("docs/深度分析专精.md", "r", encoding="utf-8") as f:
            return f.read()
    except FileNotFoundError:
        logger.error("分析规则文件未找到")
        return ""

class OldModelStabilityTester:
    """老模型稳定性测试器"""
    
    def __init__(self):
        load_dotenv()
        
        # 老模型配置
        self.old_model_config = {
            "name": "vertexai/gemini-2.5-pro-preview-05-06",
            "api_key": "sk-mqWRZm6zFLW04WRL72FeEcF983634a5c8bF7Ef1b955eC3Ab",
            "api_base": "http://206.189.40.22:3009/v1"
        }
        
        self.analysis_rules = load_analysis_rules()
        self.results_dir = Path("quick_test_results")
        self.results_dir.mkdir(exist_ok=True)
        
        # 测试基金列表
        self.test_funds = [
            "017826.OF",  # 兴证全球欣越A
            "000573.OF",  # 宝盈核心优势A
            "002350.OF",  # 易方达裕祥回报
        ]
        
    def prepare_fund_data(self, fund_code):
        """准备基金数据"""
        logger.info(f"准备基金 {fund_code} 的数据...")
        
        # 获取基金基本数据
        fund_data = fetch_fund_data(fund_code)
        if not fund_data:
            logger.error(f"无法获取基金 {fund_code} 的基本数据")
            return None
            
        # 获取持仓数据
        portfolio_data = fetch_fund_portfolio_data(fund_code, fund_data.get('report_end_date', '2025-03-31'))
        if not portfolio_data:
            logger.warning(f"无法获取基金 {fund_code} 的持仓数据")
            portfolio_data = []
            
        # 简化外部数据获取
        try:
            external_data = {}
            for item in portfolio_data[:3]:  # 只处理前3个持仓以节省时间
                symbol = item.get('symbol')
                if symbol and symbol.endswith('.HK'):  # 只处理港股
                    stock_external_data = get_external_stock_details(symbol)
                    if stock_external_data:
                        external_data[symbol] = stock_external_data
            logger.info(f"成功获取 {len(external_data)} 只股票的外部估值数据")
        except Exception as e:
            logger.warning(f"获取外部估值数据失败: {e}")
            external_data = {}
            
        # 合并估值数据
        for item in portfolio_data:
            symbol = item.get('symbol')
            if symbol in external_data:
                item.update(external_data[symbol])
                
        return {
            'fund_data': fund_data,
            'portfolio_data': portfolio_data
        }
        
    def test_old_model(self, fund_code, prepared_data, test_round):
        """测试老模型"""
        logger.info(f"第{test_round}轮测试 - 使用老模型分析基金 {fund_code}")
        
        fund_data = prepared_data['fund_data']
        portfolio_data = prepared_data['portfolio_data']
        
        # 格式化持仓数据
        formatted_portfolio_items = []
        for item in portfolio_data:
            total_mv_wanyuan = item.get('total_mv', 0)
            if isinstance(total_mv_wanyuan, (int, float)) and total_mv_wanyuan > 0:
                total_mv_str = f"{total_mv_wanyuan / 10000:.2f}亿元"
            else:
                total_mv_str = "未知市值"
                
            market_cap_style_str = item.get('market_cap_style', '未知风格')
            pe_str = f"{item.get('pe', 'N/A')}" if item.get('pe') is not None else "N/A"
            pe_ttm_str = f"{item.get('pe_ttm', 'N/A')}" if item.get('pe_ttm') is not None else "N/A"
            pb_str = f"{item.get('pb', 'N/A')}" if item.get('pb') is not None else "N/A"
            
            formatted_portfolio_items.append(
                f"- {item['symbol']} ({item.get('stock_name', '未知名称')} - {item.get('sw_l1_industry', '未知行业')}): "
                f"总市值: {total_mv_str}, 市值风格: {market_cap_style_str}, "
                f"PE: {pe_str}, PE(TTM): {pe_ttm_str}, PB: {pb_str}, "
                f"占净值比例 {item.get('stk_mkv_ratio', 'N/A')}%"
            )
            
        portfolio_data_str = "\n".join(formatted_portfolio_items) if formatted_portfolio_items else "未提供持仓数据。"
        
        # 记录开始时间
        start_time = time.time()
        
        # 调用LLM分析
        try:
            result = analyze_with_llm(
                fund_code=fund_code,
                fund_name=fund_data.get('fund_name', ''),
                fund_manager=fund_data.get('fund_manager', ''),
                report_end_date=fund_data.get('report_end_date', ''),
                market_outlook=fund_data.get('market_outlook', ''),
                operation_analysis=fund_data.get('operation_analysis', ''),
                portfolio_data=portfolio_data_str,
                sw_industry_info="老模型稳定性测试用申万行业信息",
                valuation_summary="老模型稳定性测试用估值信息",
                scale_analysis="老模型稳定性测试用规模分析",
                analysis_rules=self.analysis_rules,
                model_name=self.old_model_config['name'],
                api_key=self.old_model_config['api_key'],
                api_base=self.old_model_config['api_base']
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            if result:
                logger.info(f"老模型分析成功，报告长度: {len(result)} 字符，耗时: {duration:.1f}秒")
                return {
                    'success': True,
                    'result': result,
                    'duration': duration,
                    'length': len(result),
                    'fund_code': fund_code,
                    'test_round': test_round
                }
            else:
                logger.error(f"老模型分析失败")
                return {
                    'success': False,
                    'error': 'Analysis failed',
                    'duration': duration,
                    'fund_code': fund_code,
                    'test_round': test_round
                }
                
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            logger.error(f"老模型分析出错: {e}")
            return {
                'success': False,
                'error': str(e),
                'duration': duration,
                'fund_code': fund_code,
                'test_round': test_round
            }
            
    def analyze_quality_dimensions(self, result_text):
        """多维度质量分析"""
        analysis = {}
        
        # 1. 基础指标
        analysis['length'] = len(result_text)
        analysis['lines'] = len(result_text.split('\n'))
        analysis['words'] = len(result_text.split())
        
        # 2. JSON格式检查
        json_data = extract_json_from_report(result_text)
        analysis['json_valid'] = json_data is not None
        if json_data:
            analysis['json_fields_count'] = len(json_data)
            analysis['json_completeness'] = len(json_data) / 33  # 期望33个字段
        else:
            analysis['json_fields_count'] = 0
            analysis['json_completeness'] = 0
            
        # 3. 结构完整性
        required_sections = [
            "核心洞察与关键评估摘要",
            "核心投资逻辑摘要", 
            "投资组合与变动分析",
            "业绩归因与关键驱动",
            "关键洞察与风险提示",
            "其他重要信息",
            "表格数据"
        ]
        found_sections = sum(1 for section in required_sections if section in result_text)
        analysis['structure_completeness'] = found_sections / len(required_sections)
        
        # 4. 重点标识使用
        emoji_indicators = ['🔴', '🟡', '🟢']
        analysis['emoji_count'] = sum(result_text.count(emoji) for emoji in emoji_indicators)
        
        # 5. 综合质量评分
        quality_score = (
            min(analysis['length'] / 28000, 1.0) * 0.25 +  # 长度评分
            analysis['json_completeness'] * 0.35 +  # JSON完整性
            analysis['structure_completeness'] * 0.25 +  # 结构完整性
            min(analysis['emoji_count'] / 10, 1.0) * 0.15  # 标识使用
        )
        analysis['overall_quality'] = quality_score
        
        return analysis
        
    def run_stability_test(self, rounds=3):
        """运行稳定性测试"""
        print(f"🚀 开始老模型稳定性测试 ({rounds}轮)")
        print(f"模型: {self.old_model_config['name']}")
        print(f"测试基金: {', '.join(self.test_funds)}")
        print("=" * 60)
        
        all_results = []
        
        for round_num in range(1, rounds + 1):
            print(f"\n🔄 第 {round_num}/{rounds} 轮测试")
            print("-" * 40)
            
            for fund_code in self.test_funds:
                print(f"\n📊 测试基金: {fund_code}")
                
                # 准备数据
                prepared_data = self.prepare_fund_data(fund_code)
                if not prepared_data:
                    print(f"❌ 基金 {fund_code} 数据准备失败")
                    continue
                
                # 测试老模型
                result = self.test_old_model(fund_code, prepared_data, round_num)
                
                # 保存结果
                if result['success']:
                    # 保存原始报告
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    
                    result_file = self.results_dir / f"old_model_round{round_num}_{fund_code}_{timestamp}.md"
                    
                    with open(result_file, 'w', encoding='utf-8') as f:
                        f.write(result['result'])
                    
                    # 质量分析
                    analysis = self.analyze_quality_dimensions(result['result'])
                    
                    # 记录结果
                    test_result = {
                        'round': round_num,
                        'fund_code': fund_code,
                        'timestamp': timestamp,
                        'duration': result['duration'],
                        'analysis': analysis,
                        'file': str(result_file)
                    }
                    
                    all_results.append(test_result)
                    
                    # 显示简要结果
                    print(f"    ✅ 质量评分: {analysis['overall_quality']:.3f}, 耗时: {result['duration']:.1f}秒")
                    print(f"    📏 长度: {analysis['length']:,}字符, JSON: {'✅' if analysis['json_valid'] else '❌'}")
                    
                else:
                    print(f"    ❌ 测试失败: {result.get('error', '未知错误')}")
                    
                # 等待一下避免API限制
                time.sleep(5)
        
        # 生成分析报告
        self.generate_stability_report(all_results)
        
        return all_results
        
    def generate_stability_report(self, all_results):
        """生成稳定性分析报告"""
        if not all_results:
            print("❌ 没有有效的测试结果")
            return
            
        print(f"\n📊 生成稳定性分析报告...")
        
        # 计算统计数据
        def calculate_stats(values):
            if not values:
                return {'mean': 0, 'median': 0, 'stdev': 0, 'min': 0, 'max': 0}
            return {
                'mean': statistics.mean(values),
                'median': statistics.median(values),
                'stdev': statistics.stdev(values) if len(values) > 1 else 0,
                'min': min(values),
                'max': max(values)
            }
        
        # 提取各项指标
        durations = [r['duration'] for r in all_results]
        quality_scores = [r['analysis']['overall_quality'] for r in all_results]
        lengths = [r['analysis']['length'] for r in all_results]
        json_success_rate = sum(1 for r in all_results if r['analysis']['json_valid']) / len(all_results)
        
        # 生成报告
        report = {
            'test_summary': {
                'total_tests': len(all_results),
                'test_rounds': max(r['round'] for r in all_results),
                'test_funds': list(set(r['fund_code'] for r in all_results)),
                'timestamp': datetime.now().isoformat(),
                'model': self.old_model_config['name']
            },
            'stability_metrics': {
                'duration': calculate_stats(durations),
                'quality_score': calculate_stats(quality_scores),
                'length': calculate_stats(lengths),
                'json_success_rate': json_success_rate
            },
            'detailed_results': all_results
        }
        
        # 保存报告
        report_file = self.results_dir / f"old_model_stability_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
            
        # 生成可读性报告
        self.generate_readable_stability_report(report)
        
        print(f"✅ 稳定性报告已保存: {report_file}")
        
    def generate_readable_stability_report(self, report_data):
        """生成可读性稳定性报告"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        readable_file = self.results_dir / f"old_model_stability_readable_{timestamp}.md"
        
        with open(readable_file, 'w', encoding='utf-8') as f:
            f.write("# 老模型稳定性测试报告\n\n")
            
            # 测试概览
            summary = report_data['test_summary']
            f.write(f"## 测试概览\n\n")
            f.write(f"- **测试模型**: {summary['model']}\n")
            f.write(f"- **测试轮数**: {summary['test_rounds']}轮\n")
            f.write(f"- **测试基金**: {len(summary['test_funds'])}只\n")
            f.write(f"- **总测试次数**: {summary['total_tests']}次\n")
            f.write(f"- **测试时间**: {summary['timestamp']}\n\n")
            
            # 稳定性指标
            metrics = report_data['stability_metrics']
            f.write(f"## 稳定性指标\n\n")
            
            f.write(f"### 性能稳定性\n")
            f.write(f"- **平均耗时**: {metrics['duration']['mean']:.1f}秒\n")
            f.write(f"- **耗时范围**: {metrics['duration']['min']:.1f}秒 - {metrics['duration']['max']:.1f}秒\n")
            f.write(f"- **耗时标准差**: {metrics['duration']['stdev']:.1f}秒\n\n")
            
            f.write(f"### 质量稳定性\n")
            f.write(f"- **平均质量评分**: {metrics['quality_score']['mean']:.3f}\n")
            f.write(f"- **质量评分范围**: {metrics['quality_score']['min']:.3f} - {metrics['quality_score']['max']:.3f}\n")
            f.write(f"- **质量标准差**: {metrics['quality_score']['stdev']:.3f}\n\n")
            
            f.write(f"### 输出稳定性\n")
            f.write(f"- **平均输出长度**: {metrics['length']['mean']:,.0f}字符\n")
            f.write(f"- **长度范围**: {metrics['length']['min']:,.0f} - {metrics['length']['max']:,.0f}字符\n")
            f.write(f"- **JSON成功率**: {metrics['json_success_rate']:.1%}\n\n")
            
            # 稳定性评估
            f.write(f"## 稳定性评估\n\n")
            
            # 性能稳定性评估
            duration_cv = metrics['duration']['stdev'] / metrics['duration']['mean'] if metrics['duration']['mean'] > 0 else 0
            if duration_cv < 0.2:
                f.write(f"✅ **性能稳定性良好** (变异系数: {duration_cv:.2f})\n")
            elif duration_cv < 0.4:
                f.write(f"🟡 **性能稳定性一般** (变异系数: {duration_cv:.2f})\n")
            else:
                f.write(f"🔴 **性能稳定性较差** (变异系数: {duration_cv:.2f})\n")
                
            # 质量稳定性评估
            quality_cv = metrics['quality_score']['stdev'] / metrics['quality_score']['mean'] if metrics['quality_score']['mean'] > 0 else 0
            if quality_cv < 0.1:
                f.write(f"✅ **质量稳定性优秀** (变异系数: {quality_cv:.2f})\n")
            elif quality_cv < 0.2:
                f.write(f"🟡 **质量稳定性良好** (变异系数: {quality_cv:.2f})\n")
            else:
                f.write(f"🔴 **质量稳定性需改进** (变异系数: {quality_cv:.2f})\n")
                
            # JSON成功率评估
            if metrics['json_success_rate'] >= 0.9:
                f.write(f"✅ **JSON格式稳定性优秀** ({metrics['json_success_rate']:.1%}成功率)\n")
            elif metrics['json_success_rate'] >= 0.7:
                f.write(f"🟡 **JSON格式稳定性良好** ({metrics['json_success_rate']:.1%}成功率)\n")
            else:
                f.write(f"🔴 **JSON格式稳定性需改进** ({metrics['json_success_rate']:.1%}成功率)\n")
                
        print(f"✅ 可读性稳定性报告已保存: {readable_file}")

def main():
    """主函数"""
    tester = OldModelStabilityTester()
    
    # 运行3轮稳定性测试
    results = tester.run_stability_test(rounds=3)
    
    print(f"\n🎉 老模型稳定性测试完成！")
    print(f"📁 结果保存在: {tester.results_dir}")
    print(f"📊 共完成 {len(results)} 次有效测试")

if __name__ == "__main__":
    main()
