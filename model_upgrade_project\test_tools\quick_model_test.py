#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速模型测试脚本
用于快速测试新模型的基本功能

使用方法:
python quick_model_test.py
"""

import os
import sys
import json
import datetime
from pathlib import Path
from dotenv import load_dotenv

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 加载环境变量
load_dotenv()

from src.llm_analyzer import analyze_with_llm
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_analysis_rules():
    """加载分析规则"""
    rules_path = project_root / "docs" / "深度分析专精.md"
    try:
        with open(rules_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        logger.error(f"加载分析规则失败: {e}")
        return ""

def test_models():
    """测试新旧模型"""
    
    # 模型配置 - 新旧模型使用不同的API地址和密钥
    old_model = {
        "name": "vertexai/gemini-2.5-pro-preview-05-06",
        "api_key": "sk-mqWRZm6zFLW04WRL72FeEcF983634a5c8bF7Ef1b955eC3Ab",
        "api_base": "http://206.189.40.22:3009/v1"
    }

    new_model = {
        "name": "gemini-2.5-pro-exp-03-25",
        "api_key": "sk-gRq2nHouhXUgRqVzdnKbQYVDwPYIhLj94NmG2rgTjAuZA8SC",
        "api_base": "https://axhixwxzsmtd.us-west-1.clawcloudrun.com/v1"
    }
    
    # 测试数据（使用 019820.OF 的数据）
    test_data = {
        "fund_code": "019820.OF",
        "fund_name": "鹏华远见精选A",
        "fund_manager": "王子建",
        "report_end_date": "2025-03-31",
        "market_outlook": "展望后市，我们认为科技成长表现突出，中小市值公司相对活跃，表现好于大市值公司。",
        "operation_analysis": """报告期内，我们从去年四季度开始重点布局科技成长赛道里的优秀公司。
        我们重点布局了三个方向：
        1. 人工智能国产算力链条：我们认为AI应用的爆发将催生大量的国产算力需求。
        2. 人形机器人：我们认为2025年是人形机器人的产业化元年。
        3. 以恒生科技为代表的中国核心科技资产：我们认为中国科技产业正处于巨大的创新浪潮中。
        同时，我们也关注内需板块的企稳和政策预期。""",
        "portfolio_data": """股票代码: 301160.SZ, 股票名称: 翔楼新材, 占净值比例: 13.04%, 持股数量: 1234567, 市值: 10000.00万元
股票代码: 1810.HK, 股票名称: 小米集团-W, 占净值比例: 5.27%, 持股数量: 2345678, 市值: 4000.00万元
股票代码: 0981.HK, 股票名称: 中芯国际, 占净值比例: 4.25%, 持股数量: 3456789, 市值: 3200.00万元""",
        "sw_industry_info": """{"翔楼新材": {"一级行业": "钢铁", "二级行业": "特钢Ⅱ", "三级行业": "特钢Ⅲ"}}""",
        "valuation_summary": """{"平均PE": 75.2, "中位数PE": 32.99, "平均PB": 4.15}""",
        "scale_analysis": """{"整体规模上限": "6.8亿元", "瓶颈股票": "翔楼新材"}"""
    }
    
    analysis_rules = load_analysis_rules()
    
    print("🔄 开始模型对比测试...")
    print(f"📊 测试基金: {test_data['fund_name']} ({test_data['fund_code']})")
    print()
    
    # 测试旧模型
    print("🔵 测试旧模型...")
    print(f"模型: {old_model['name']}")
    
    try:
        old_report = analyze_with_llm(
            fund_code=test_data["fund_code"],
            fund_name=test_data["fund_name"],
            fund_manager=test_data["fund_manager"],
            report_end_date=test_data["report_end_date"],
            market_outlook=test_data["market_outlook"],
            operation_analysis=test_data["operation_analysis"],
            portfolio_data=test_data["portfolio_data"],
            sw_industry_info=test_data["sw_industry_info"],
            valuation_summary=test_data["valuation_summary"],
            scale_analysis=test_data["scale_analysis"],
            analysis_rules=analysis_rules,
            model_name=old_model["name"],
            api_key=old_model["api_key"],
            api_base=old_model["api_base"]
        )
        
        if old_report:
            print("✅ 旧模型测试成功")
            print(f"📝 报告长度: {len(old_report)} 字符")
        else:
            print("❌ 旧模型测试失败")
            return
            
    except Exception as e:
        print(f"❌ 旧模型测试出错: {e}")
        return
    
    print()
    
    # 测试新模型
    print("🟢 测试新模型...")
    print(f"模型: {new_model['name']}")
    
    try:
        new_report = analyze_with_llm(
            fund_code=test_data["fund_code"],
            fund_name=test_data["fund_name"],
            fund_manager=test_data["fund_manager"],
            report_end_date=test_data["report_end_date"],
            market_outlook=test_data["market_outlook"],
            operation_analysis=test_data["operation_analysis"],
            portfolio_data=test_data["portfolio_data"],
            sw_industry_info=test_data["sw_industry_info"],
            valuation_summary=test_data["valuation_summary"],
            scale_analysis=test_data["scale_analysis"],
            analysis_rules=analysis_rules,
            model_name=new_model["name"],
            api_key=new_model["api_key"],
            api_base=new_model["api_base"]
        )
        
        if new_report:
            print("✅ 新模型测试成功")
            print(f"📝 报告长度: {len(new_report)} 字符")
        else:
            print("❌ 新模型测试失败")
            return
            
    except Exception as e:
        print(f"❌ 新模型测试出错: {e}")
        return
    
    print()
    
    # 保存对比结果
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 创建输出目录
    output_dir = Path("quick_test_results")
    output_dir.mkdir(exist_ok=True)
    
    # 保存旧模型报告
    old_report_path = output_dir / f"OLD_MODEL_{timestamp}.md"
    with open(old_report_path, 'w', encoding='utf-8') as f:
        f.write(f"# 旧模型报告 ({old_model['name']})\n\n")
        f.write(old_report)
    
    # 保存新模型报告
    new_report_path = output_dir / f"NEW_MODEL_{timestamp}.md"
    with open(new_report_path, 'w', encoding='utf-8') as f:
        f.write(f"# 新模型报告 ({new_model['name']})\n\n")
        f.write(new_report)
    
    # 简单质量对比
    print("📊 快速质量对比:")
    print(f"   旧模型报告长度: {len(old_report):,} 字符")
    print(f"   新模型报告长度: {len(new_report):,} 字符")
    
    # 检查 JSON 结构
    old_has_json = "```json" in old_report and "```" in old_report
    new_has_json = "```json" in new_report and "```" in new_report
    print(f"   旧模型 JSON 结构: {'✅' if old_has_json else '❌'}")
    print(f"   新模型 JSON 结构: {'✅' if new_has_json else '❌'}")
    
    # 检查重点标识
    old_has_indicators = any(indicator in old_report for indicator in ["🔴", "🟡", "🟢"])
    new_has_indicators = any(indicator in new_report for indicator in ["🔴", "🟡", "🟢"])
    print(f"   旧模型重点标识: {'✅' if old_has_indicators else '❌'}")
    print(f"   新模型重点标识: {'✅' if new_has_indicators else '❌'}")
    
    print()
    print("📁 报告已保存:")
    print(f"   旧模型: {old_report_path}")
    print(f"   新模型: {new_report_path}")
    print()
    print("🔍 请手动检查两个报告的质量差异")
    print("📋 建议检查项目:")
    print("   1. 分析逻辑的深度和准确性")
    print("   2. 核心洞察的质量")
    print("   3. 中文表达的流畅度")
    print("   4. 结构化数据的完整性")
    print("   5. 重点标识的使用准确性")

if __name__ == "__main__":
    test_models()
