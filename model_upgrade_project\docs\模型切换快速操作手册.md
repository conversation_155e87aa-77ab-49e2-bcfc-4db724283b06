# 模型切换快速操作手册

## 🚀 快速开始

### 核心概念
- **模型切换** = 更换AI大脑，提升分析质量
- **测试验证** = 确保新大脑工作正常
- **生产部署** = 正式使用新大脑

### 关键文件
- `.env` - 模型配置文件
- `test_new_model_quality.py` - 单基金测试
- `test_multiple_funds_quality.py` - 多基金测试
- `main.py` - 生产环境程序

---

## 📋 切换检查清单

### 1. 环境配置检查
```bash
# 检查.env文件配置
LLM_MODEL_NAME=gemini-2.5-pro-preview-06-05
OPENAI_API_BASE=https://axhixwxzsmtd.us-west-1.clawcloudrun.com/v1
LLM_API_KEY=sk-gRq2nHouhXUgRqVzdnKbQYVDwPYIhLj94NmG2rgTjAuZA8SC
```

**检查要点**:
- [ ] 模型名称正确
- [ ] API地址正确
- [ ] 密钥有效

### 2. 单基金测试
```bash
python test_new_model_quality.py
```

**期望结果**:
- [ ] 质量评分 4/4
- [ ] 字符数 12,000+
- [ ] PE(TTM) ≥3次
- [ ] 风险标识充分
- [ ] JSON格式正确

### 3. 多基金测试
```bash
python test_multiple_funds_quality.py
```

**期望结果**:
- [ ] 100%达标率
- [ ] 5个基金全部成功
- [ ] 平均字符数 14,000+
- [ ] 质量稳定

### 4. 生产环境验证
```bash
python main.py 008186.OF
```

**检查要点**:
- [ ] 程序正常运行
- [ ] 模型名称显示正确
- [ ] 报告生成成功
- [ ] 质量达到预期

---

## ⚡ 常见问题速查

### API连接问题
**症状**: HTTP 503错误
**解决**:
1. 检查API密钥是否正确
2. 验证API地址是否正确
3. 确认网络连接正常

### 质量不达标
**症状**: 评分低于4/4
**解决**:
1. 检查提示词配置
2. 验证深度分析专精.md文件
3. 重新运行测试

### 字符数不足
**症状**: 输出少于12,000字符
**解决**:
1. 检查字符数要求设置
2. 验证各部分最低要求
3. 调整提示词参数

---

## 📊 质量标准

### 基础指标
- **字符数**: 12,000-16,000字符
- **PE(TTM)**: ≥3次使用
- **风险标识**: 🔴≥2, 🟡≥1, 🟢≥1
- **JSON字段**: 33个完整字段

### 高级指标
- **批判性思维**: 四层分析链条
- **风险具体化**: 量化数据支撑
- **专业表达**: 高频#标签#使用
- **结构完整**: 7部分完整

---

## 🔧 故障排除

### 1. 模型无响应
```bash
# 检查配置
cat .env | grep LLM

# 测试连接
curl -X POST $OPENAI_API_BASE/chat/completions \
  -H "Authorization: Bearer $LLM_API_KEY" \
  -H "Content-Type: application/json"
```

### 2. 质量异常
```bash
# 重新测试
python test_new_model_quality.py

# 检查日志
tail -f logs/llm_analyzer.log
```

### 3. 生产环境问题
```bash
# 检查进程
ps aux | grep python

# 查看错误日志
tail -f logs/main.log
```

---

## 📈 监控指标

### 技术指标
- API成功率: >99%
- 响应时间: <3分钟
- Token使用: 14,000-26,000
- 错误率: <1%

### 质量指标
- 评分达标率: 100%
- 平均字符数: >14,000
- 专业术语密度: 高
- 用户满意度: >90%

---

## 🚨 应急处理

### 紧急回滚
```bash
# 1. 备份当前配置
cp .env .env.backup

# 2. 恢复老模型配置
LLM_MODEL_NAME=vertexai/gemini-2.5-pro-preview-05-06
OPENAI_API_BASE=http://*************:3009/v1

# 3. 重启服务
python main.py
```

### 快速修复
```bash
# 1. 检查配置
python -c "import os; print(os.getenv('LLM_MODEL_NAME'))"

# 2. 测试连接
python test_new_model_quality.py

# 3. 验证质量
python test_multiple_funds_quality.py
```

---

## 📝 操作记录

### 切换记录模板
```
日期: 2025-06-18
操作人: [姓名]
切换类型: [测试/生产]
模型版本: gemini-2.5-pro-preview-06-05
测试结果: [成功/失败]
质量评分: [X/4]
备注: [特殊情况说明]
```

### 质量监控记录
```
检查时间: [时间]
基金代码: [代码]
字符数: [数量]
质量评分: [评分]
问题发现: [问题描述]
处理措施: [解决方案]
```

---

## 🎯 最佳实践

### 切换前
1. 充分测试验证
2. 准备回滚方案
3. 通知相关人员

### 切换中
1. 监控系统状态
2. 记录操作过程
3. 及时处理问题

### 切换后
1. 验证运行状态
2. 监控质量指标
3. 收集用户反馈

---

## 📞 联系方式

### 技术支持
- **AI优化团队**: [联系方式]
- **运维团队**: [联系方式]
- **紧急联系**: [24小时热线]

### 文档维护
- **负责人**: [姓名]
- **更新频率**: 每月
- **版本控制**: Git

---

**版本**: v1.0  
**更新**: 2025-06-18  
**用途**: 模型切换日常操作参考
