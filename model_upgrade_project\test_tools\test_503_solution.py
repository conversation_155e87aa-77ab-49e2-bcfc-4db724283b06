#!/usr/bin/env python3
"""
测试503错误解决方案
验证新的重试机制、速率限制和错误处理
"""

import logging
import time
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.parallel_processor import SimpleLLMParallelProcessor, create_analysis_task

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - [%(name)s.%(funcName)s:%(lineno)d] - %(message)s',
    handlers=[logging.StreamHandler()]
)

logger = logging.getLogger(__name__)

def mock_llm_analysis_with_errors(fund_code, **kwargs):
    """
    模拟LLM分析，包含随机错误以测试重试机制
    """
    import random
    import openai
    
    # 模拟不同类型的错误
    error_chance = random.random()
    
    if error_chance < 0.2:  # 20%概率503错误
        logger.warning(f"模拟503错误 - {fund_code}")
        raise openai.InternalServerError("Service Unavailable", response=None, body=None)
    elif error_chance < 0.3:  # 10%概率连接错误
        logger.warning(f"模拟连接错误 - {fund_code}")
        raise openai.APIConnectionError("Connection failed")
    elif error_chance < 0.35:  # 5%概率超时错误
        logger.warning(f"模拟超时错误 - {fund_code}")
        raise openai.APITimeoutError("Request timeout")
    
    # 模拟正常处理时间
    processing_time = random.uniform(2, 8)
    time.sleep(processing_time)
    
    return f"基金 {fund_code} 分析报告 - 处理时间: {processing_time:.2f}秒"

def test_rate_limiting():
    """测试速率限制功能"""
    logger.info("=== 测试速率限制功能 ===")
    
    # 创建处理器，设置较短的间隔用于测试
    processor = SimpleLLMParallelProcessor(
        max_workers=1,
        enable_parallel=True,
        request_interval=2.0  # 2秒间隔
    )
    
    # 创建测试任务
    test_tasks = []
    for i in range(3):
        task = create_analysis_task(
            mock_llm_analysis_with_errors,
            fund_code=f"TEST{i:03d}.OF"
        )
        test_tasks.append(task)
    
    start_time = time.time()
    
    try:
        results = processor.process_batch_funds(test_tasks)
        
        total_time = time.time() - start_time
        logger.info(f"总处理时间: {total_time:.2f}秒")
        
        # 验证速率限制是否生效（应该至少需要4秒：2秒间隔 × 2）
        expected_min_time = 4.0  # 2秒间隔 × 2个间隔
        if total_time >= expected_min_time:
            logger.info("✅ 速率限制功能正常工作")
        else:
            logger.warning(f"⚠️ 速率限制可能未生效，预期最少{expected_min_time}秒，实际{total_time:.2f}秒")
        
        # 统计结果
        successful = sum(1 for r in results if r['success'])
        failed = len(results) - successful
        
        logger.info(f"成功: {successful}, 失败: {failed}")
        
    finally:
        processor.close()

def test_retry_mechanism():
    """测试重试机制"""
    logger.info("=== 测试重试机制 ===")
    
    # 测试单个函数的重试
    from src.llm_analyzer import analyze_with_llm
    
    # 这里需要有效的API配置才能真正测试
    # 暂时只测试配置是否正确加载
    try:
        # 检查重试装饰器是否正确应用
        if hasattr(analyze_with_llm, 'retry'):
            logger.info("✅ 重试装饰器已正确应用到analyze_with_llm函数")
        else:
            logger.warning("⚠️ 重试装饰器可能未正确应用")
            
    except Exception as e:
        logger.error(f"测试重试机制时出错: {e}")

def test_configuration_loading():
    """测试配置文件加载"""
    logger.info("=== 测试配置文件加载 ===")
    
    try:
        import json
        
        # 测试并行配置
        with open('config/parallel_config.json', 'r', encoding='utf-8') as f:
            parallel_config = json.load(f)
            
        logger.info(f"并行配置加载成功: max_workers={parallel_config['parallel_processing']['max_workers']}")
        
        # 测试API弹性配置
        with open('config/api_resilience_config.json', 'r', encoding='utf-8') as f:
            resilience_config = json.load(f)
            
        logger.info(f"API弹性配置加载成功: 重试次数={resilience_config['retry_config']['max_attempts']}")
        
        logger.info("✅ 所有配置文件加载成功")
        
    except Exception as e:
        logger.error(f"配置文件加载失败: {e}")

def test_dependency_imports():
    """测试依赖导入"""
    logger.info("=== 测试依赖导入 ===")
    
    try:
        # 测试tenacity导入
        from tenacity import retry, stop_after_attempt, wait_exponential
        logger.info("✅ Tenacity库导入成功")
        
        # 测试openai导入
        import openai
        logger.info("✅ OpenAI库导入成功")
        
        # 测试并行处理器导入
        from src.parallel_processor import SimpleLLMParallelProcessor
        logger.info("✅ 并行处理器导入成功")
        
    except ImportError as e:
        logger.error(f"依赖导入失败: {e}")
        logger.error("请运行: pip install tenacity")

def main():
    """主测试函数"""
    logger.info("开始503错误解决方案测试")
    
    # 运行所有测试
    test_dependency_imports()
    test_configuration_loading()
    test_retry_mechanism()
    test_rate_limiting()
    
    logger.info("测试完成")

if __name__ == "__main__":
    main()
