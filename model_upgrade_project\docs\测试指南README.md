# 🧪 模型升级项目测试指南

## 📁 目录结构

```
model_upgrade_project/
├── test_tools/                  # 测试工具目录（18个工具）
│   ├── 🔧 核心测试工具
│   ├── quick_model_test.py      # 快速模型测试
│   ├── batch_model_comparison.py # 批量模型对比
│   ├── enhanced_model_comparison.py # 增强型模型对比
│   ├── multi_dimensional_comparison.py # 多维度对比
│   ├── test_enhanced_prompt_v2.py # 增强提示词测试
│   │
│   ├── 🔍 专项测试工具
│   ├── test_503_solution.py     # 503错误解决方案测试
│   ├── test_fund_scale_model.py # 基金规模模型测试
│   ├── test_concept_extraction_v3.py # 概念提取v3测试
│   ├── test_parallel.py         # 并行处理测试
│   ├── test_wind_optimization.py # Wind优化测试
│   ├── test_data_availability.py # 数据可用性测试
│   │
│   ├── 📊 案例研究工具
│   ├── test_integrated_scale_analysis.py # 集成规模分析测试
│   ├── test_laisi_case_study.py # 来思案例研究测试
│   ├── test_single_fund_with_concepts.py # 单基金概念测试
│   ├── simple_multi_test.py     # 简单多基金测试
│   │
│   └── 🔄 其他工具
│   ├── model_comparison_test.py # 模型对比测试
│   ├── random_funds_comparison.py # 随机基金对比
│   └── old_model_stability_test.py # 老模型稳定性测试
│
├── test_data/                   # 测试数据目录（3个文件）
├── test_fund_list_small.txt     # 小型基金列表
├── test_prompt_001128_OF_with_external.txt # 测试提示词样本1
└── test_prompt_010350_OF_with_external.txt # 测试提示词样本2
```

> 💡 **新手请先阅读**: [`TESTING_GUIDE.md`](./TESTING_GUIDE.md) 获取完整的测试指南

## 🔥 核心测试文件

### `test_010350_data_format.py`
**用途**：完整数据流程验证
**重要性**：⭐⭐⭐⭐⭐

**运行方式**：
```bash
cd tests
python test_010350_data_format.py
```

**测试覆盖**：
- ✅ 基金数据获取
- ✅ 持仓数据格式化
- ✅ 申万行业分类（A股+港股）
- ✅ 估值统计计算
- ✅ Wind API外部数据集成
- ✅ 概念标签提取
- ✅ LLM Prompt构建

### `test_wind_real_data.py`
**用途**：Wind API专项测试
**重要性**：⭐⭐⭐⭐

**运行方式**：
```bash
cd tests
python test_wind_real_data.py
```

**测试覆盖**：
- ✅ Wind API连接状态
- ✅ 真实数据获取验证
- ✅ 数据源标识确认
- ✅ external_data_provider集成

### `test_wind_optimization.py`
**用途**：Wind API重复调用优化验证
**重要性**：⭐⭐⭐⭐

**运行方式**：
```bash
cd tests
python test_wind_optimization.py
```

**测试覆盖**：
- ✅ 缓存机制验证
- ✅ 性能提升测试
- ✅ 数据一致性检查
- ✅ 批量分析优化效果

## 🚀 快速测试

### 完整系统测试
```bash
cd tests
python test_010350_data_format.py
```

### Wind API测试
```bash
cd tests
python test_wind_real_data.py
```

## 📚 历史测试文件

历史和存档的测试文件位于 `../archive/` 目录：
- `test_data_flow.py` - 早期数据流程测试
- `test_external_data_provider.py` - 单独模块测试

## 🔧 前置条件

### 数据库连接
- 确保 `.env` 文件配置正确
- 数据库服务正常运行

### Wind API（仅港股估值需要）
- Wind终端已启动并登录
- WindPy模块已安装：`pip install WindPy`

## 📊 测试数据

### 测试基金：010350.OF
- **名称**：景顺长城品质长青A
- **特点**：包含A股和港股持仓
- **持仓**：4只A股 + 6只港股

### 预期结果
- 估值覆盖率 > 60%
- 申万行业分类完整
- Wind API返回真实数据

## 🎯 成功标准

- [ ] 所有模块正常工作
- [ ] 数据格式与LLM兼容
- [ ] Wind API返回真实数据（data_source: "wind"）
- [ ] 无关键错误日志

## 📝 注意事项

1. **路径问题**：测试文件中的导入路径已调整为相对于tests目录
2. **日志级别**：建议设置为INFO获得适当信息量
3. **网络依赖**：Wind API测试需要网络连接
4. **数据依赖**：需要tusharedb数据库中的基础数据
