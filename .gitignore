# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.venv/
venv/
env/
ENV/
env.bak/
venv.bak/

# Environment variables
.env
.env.test
.env.production
.env.local

# Logs
*.log
logs/

# Node.js (not needed for this Python project)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package.json
package-lock.json
yarn.lock
.pnpm-debug.log*

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
reports/
output/
data/fund_list.txt
archive/
scripts/*.py

# Temporary test and analysis files (moved to tests/)

# Database files
*.db
*.sqlite
*.sqlite3

# Backup files
*.bak
*.backup
*.old

# Temporary files
*.tmp
*.temp

# Test coverage
.coverage
.pytest_cache/
htmlcov/

# SpecStory explanation file
.specstory/.what-is-this.md
