# .env.example - 复制此文件为 .env 并填入您的实际值

# --- LLM Configuration ---
# 您的 LLM API 密钥
LLM_API_KEY="sk-M9nmyRCnTkFPHxD_oCCZSCucLwEqEF-OEHCK_CM-MKOqTllX9FPgrrNR0fI"

# 您要使用的 LLM 模型名称
# 例如:
# - 对于 OpenAI 官方: "gpt-3.5-turbo", "gpt-4", etc.
# - 对于 DeepSeek: "deepseek-chat", "deepseek-coder", etc. (请查阅DeepSeek文档)
# - 对于 Kimi (Moonshot): "moonshot-v1-8k", "moonshot-v1-32k", etc. (请查阅Moonshot文档)
# - 对于其他兼容API或第三方中转站: 请使用其指定的模型名称
LLM_MODEL_NAME="gemini-2.5-pro-preview-06-05" # 新模型

# LLM API 的基础 URL (Endpoint)
# - 如果使用 OpenAI 官方 API，可以将此项留空或注释掉，openai库会自动使用官方地址。
# - 对于 DeepSeek: 通常是 "https://api.deepseek.com/v1" (请核实)
# - 对于 Kimi (Moonshot): 通常是 "https://api.moonshot.cn/v1" (请核实)
# - 对于其他第三方中转站或本地部署的兼容API: 请填入其提供的 base_url
OPENAI_API_BASE="https://aa.aazs.me/v1" # 新模型API地址

# --- 数据库配置 (如果需要直接连接数据库，当前MVP中数据获取是模拟的) ---
DB_HOST="localhost"
DB_PORT="5432"
DB_NAME="tusharedb"
DB_USER="postgres"
DB_PASSWORD="xh123"