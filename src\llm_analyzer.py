import logging
import os
import time
import openai # 用于与OpenAI兼容API交互
# import tiktoken # 用于更精确的token估算，MVP阶段可选或使用简单估算
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

logger = logging.getLogger(__name__)

# 假设 OpenAI API 密钥和模型名称等已通过 os.getenv 加载
# 或者作为参数传递给 analyze_with_llm

def _build_llm_prompt(fund_code: str, fund_name: str, fund_manager: str, report_end_date: str, market_outlook: str, operation_analysis: str, portfolio_data: str, sw_industry_info: str, valuation_summary: str, scale_analysis: str, analysis_rules: str) -> str:
    """
    构建发送给LLM的完整Prompt。
    遵循 IMP 3.4.1 Prompt 构建中确认的要素，并包含持仓数据。
    """
    # 确保即使输入为空，也作为空字符串处理，而不是None
    market_outlook = market_outlook or ""
    operation_analysis = operation_analysis or ""
    # portfolio_data 现在应该是由 main.py 格式化好的字符串，其中包含了股票名称和行业
    # 如果 main.py 未能提供格式化的持仓数据，这里提供一个默认提示
    portfolio_data_str = portfolio_data if portfolio_data and portfolio_data.strip() else "未提供或无法解析持仓数据。"
    sw_industry_info_str = sw_industry_info if sw_industry_info and sw_industry_info.strip() else "未提供申万行业分类信息。"
    valuation_summary_str = valuation_summary if valuation_summary and valuation_summary.strip() else "未提供估值统计信息。"

    # 智能处理规模约束分析数据
    if scale_analysis and scale_analysis.strip() and not scale_analysis.strip().startswith("规模约束分析：数据不足"):
        scale_analysis_str = f"以下是基于\"一张纸公式\"的基金规模约束分析结果：\n{scale_analysis}"
    else:
        scale_analysis_str = "未提供规模约束分析信息。"

    # 基于高质量参照文档优化的提示词模板
    prompt = f"""# 高质量基金分析报告生成指令

你是一位顶尖的FoF研究员，专注于生成与reports目录中高质量文档相匹配的深度分析报告。

## 核心质量标准（基于高质量参照文档）

🎯 【结构化表达要求】
- 严格按照7部分结构，每部分都要有清晰的子标题和层次
- 合理使用🔴🟡🟢标识突出核心观点
- 保持良好的段落分隔和呼吸感

📊 【数据支撑完整性】
- 必须包含具体的估值数据：每个个股的PE(TTM)、PB等具体数值
- 详细的规模约束分析：具体的规模上限、瓶颈股票分析
- 完整的申万行业分类信息整合

🔍 【批判性思维深度】（融合深度分析专精框架的穿透式分析）
- **核心聚焦领域识别与质疑**：
  * 准确识别基金的"核心聚焦领域/叙事"
  * 质疑投资叙事与实际持仓的一致性
  * 识别管理人言行中最值得关注的不一致之处
- **四层分析链条**：现象识别→深层挖掘→矛盾分析→风险预警
- **具体质疑方式**：
  * 参照模式："基金经理声称聚焦X领域，但从持仓数据看Y领域占比更高"
  * 数据验证："声称重点投资方向在实际持仓中的占比仅为Z%"
  * 逻辑穿透："此现象背后可能反映的真实意图是..."
- **风险具体化**：将抽象风险转化为可量化的具体风险点
- **前瞻性预警**：识别市场预期与基金实际策略的差异，预见潜在后果

## 高质量分析方法（基于参照文档学习）

📊 【个股分析标准化模式】
参照标准：每个个股必须包含以下要素
- 申万一级/二级/三级分类信息
- 基金中定位和权重意义
- 与核心叙事的关联性分析
- 具体估值数据：PE(TTM) XX.XX倍，PB X.XX倍
- 🟢🔴风险机遇评估

🎯 【规模约束深度分析模式】
参照标准："基金规模上限估算为11.3亿元"
- 提供具体的规模上限数值
- 识别瓶颈股票和流动性约束
- 分析规模约束对投资策略的影响
- 评估未来规模增长的挑战

⚡ 【概念标签提取优化】
参照标准：高质量概念标签提取
- 从季报文本中精确提取基金经理的原始表述
- 确保每个概念标签都有明确的文本支撑
- 提取数量要达到10个以上高质量标签
- 涵盖投资主题、策略关键词、核心理念等维度

🏗️ 【结构特征分析要求】
第2部分必须包含"具体策略/结构特征 ("核心聚焦领域/叙事"内)"子部分：
- 分析基金的内部配置结构特征（如核心-卫星配置）
- 识别在聚焦领域内部的投资分布特点
- 参照模式："基金呈现出一种`#核心-卫星配置特征#`的雏形"
- 分析在细分赛道内的风格分布和押注策略

## 强制质量控制标准

✅ 【字符数量要求】
- **强制要求**：目标输出长度必须达到14000-16000字符
- **参照标准**：reports目录中的高质量文档平均13000-14000字符
- **实现策略**：
  * 第0部分：每个洞察至少150字详细阐述（总计450字以上）
  * 第1部分：至少800字的详细投资逻辑展开
  * 第2部分：至少1800字的聚焦领域深度分析
  * 第3部分：至少2500字的组合分析（每个代表性个股至少300字）
  * 第4部分：至少600字的业绩归因分析
  * 第5部分：至少800字的风险提示
  * 第6部分：至少200字的其他信息
  * 第7部分：完整JSON数据

📊 【数据完整性检查】
必须包含以下数据要素：
- 每个代表性个股的具体PE(TTM)、PB数值
- 详细的估值统计摘要（平均PE、中位数PE等）
- 具体的规模约束分析结果
- 完整的申万行业分类信息

🔍 【概念标签质量控制】
Manager_Explicit_Concept_Tags_FromReport字段要求：
- 必须包含10个以上从季报中提取的概念标签
- 每个标签都要有明确的季报文本支撑
- 涵盖投资主题、策略关键词、核心理念等维度

⚠️ 【分析深度要求】
- 第0部分：每个洞察至少120字详细阐述
- 第3部分：每个代表性个股分析至少250字
- 第5部分：包含6个以上具体风险点
- 使用🔴🟡🟢标识突出重点观点

# 角色定义与质量标准
你是一位顶尖的FoF（基金中的基金）研究员，专项任务是对聚焦型策略基金进行季度报告的深度、穿透式分析。

# 质量标准
**目标输出长度**: 14000-16000字符
**结构完整性**: 严格按照7部分结构，每部分都要有充分的分析深度、具体展开和数据支撑
**专业表达**: 使用精准的基金分析术语，逻辑清晰，前后呼应，大量引用具体数据和估值指标
**重点突出**: 合理使用🔴🟡🟢标识，突出核心洞察和风险点
**批判性思维**: 独立判断，不盲从基金经理表述，质疑不合理的投资逻辑

# 详细分析要求（基于高质量参照文档标准）

## 各部分具体要求

**第0部分 - 核心洞察与关键评估摘要**：
- **强制要求**：2-3个最值得关注的核心发现、评估或警示，每个150-200字深度阐述
- **必须使用🔴🟡🟢重点标识**：突出本期分析中最关键的发现
- **核心聚焦领域识别**：准确识别基金的"核心聚焦领域/叙事"
- **分析层次**：每个洞察必须包含四层分析链条
  * 现象识别：基于具体数据观察表面现象
  * 深层挖掘：透过现象挖掘背后的真实逻辑和动机
  * 矛盾分析：识别策略表述与实际执行的不一致性
  * 风险预警：预见潜在的策略漂移和执行风险
- **重点突出与批判性评估**：
  * 必须识别管理人言行中最值得关注的不一致之处
  * 突出核心风险与机遇
  * 识别投资叙事与持仓现实的错位
- **参照标准**：学习参照文档的穿透式质疑
  * "投资叙事与持仓现实的显著错位"
  * "泛AI战略布局与当前持仓透明度存在温差"
  * "高集中度与核心标的叙事依赖性构成主要风险"

**第1部分 - 投资逻辑摘要**：
- 必须包含4个核心概念的详细定义，每个至少60字
- 结合基金经理的具体表述和市场环境
- 参照模式："多主题、多市场机会捕捉型的投资策略"

**第2部分 - 聚焦领域分析**：
- 至少1400字，包含详细的驱动因素、选股策略、运作特征
- **必须包含"具体策略/结构特征 ("核心聚焦领域/叙事"内)"子部分**
- 大量使用#标签#格式的专业标签
- 参照模式：`#技术迭代升级#` `#估值修复预期#`
- 参照结构特征分析："基金呈现出一种`#核心-卫星配置特征#`的雏形"

**第3部分 - 组合分析**：
- 至少1800字，每个代表性个股深度剖析至少250字
- **强制要求**：每个个股必须包含具体估值数据
- 格式要求："PE(TTM) 21.41倍，PB 4.09倍"
- 包含申万分类、基金中定位、叙事关联性、风险机遇

**第4部分 - 业绩归因**：
- 至少500字，包含Beta和Alpha来源的具体分析
- 要有推断性分析和具体推理过程

**第5部分 - 风险提示**：
- **风险具体化要求**：学习参照文档将抽象风险转化为具体、可量化的风险点
- **参照标准**：
  * "规模上限估算为11.3亿元，且前十大重仓股均存在流动性约束"
  * "华纬科技为显著瓶颈股，其5.87%的权重已对基金规模构成压力"
  * "若基金规模接近或超过此上限，基金经理维持当前聚焦中小市值个股的策略灵活性将受到严重挑战"
- **风险传导机制**：分析风险的传导路径和影响程度
- **量化风险评估**：提供具体的数值和比例来支撑风险判断

**第7部分 - JSON数据**：
- 必须包含33个完整字段，格式严格正确
- Manager_Explicit_Concept_Tags_FromReport必须包含10个以上概念标签

# 分析任务
请根据以下提供的分析规则、基金季报信息、前十大持仓数据以及申万行业分类信息，生成一份详细的基金分析报告。

【基金基本信息】
基金代码：{fund_code}
基金名称：{fund_name}
基金经理：{fund_manager}
报告期：{report_end_date}

【分析规则】
{analysis_rules}

【基金季报信息】
市场展望：
{market_outlook}

运作分析：
{operation_analysis}

【前十大持仓数据】
以下是基金的前十大持仓股票。请注意：市值单位为“亿元”。市值风格信息主要基于A股数据计算；对于标记为“未知风格”的股票，可能是由于非A股或A股数据缺失。请在分析时充分利用股票名称、所属行业和市值风格信息：
{portfolio_data_str}

【申万行业分类信息】
以下是基金持仓股票的详细申万行业分类信息（一级、二级、三级行业）：
{sw_industry_info_str}

【估值统计信息】
以下是基金持仓股票的整体估值统计摘要：
{valuation_summary_str}

【基金规模约束分析】
{scale_analysis_str}

## 概念标签提取要求（基于高质量参照文档）

**质量标准参照**：
参照文档示例："泛AI, 算力基础设施, 机器人, 港股互联网企业关注增加, 北交所投资机会, 估值考量"

**提取原则**：
- 所有概念标签必须有季报文本的明确支持
- 直接提取基金经理的原始表述，不进行过度解读
- 确保每个标签都能在季报文本中找到明确依据

**提取范围**：
- **投资主题概念**：如"泛AI"、"算力基础设施"、"机器人"等
- **市场配置策略**：如"港股互联网企业关注增加"、"北交所投资机会"等
- **投资理念关键词**：如"估值考量"、"确定性"、"高赔率"等
- **核心聚焦表述**：基金经理强调的核心投资方向和理念

**数量要求**：
- 必须提取10个以上高质量概念标签
- 每个标签都要有明确的季报文本支撑
- 涵盖投资主题、策略关键词、核心理念等维度

**格式要求**：
- 保持基金经理的原始表述，避免过度加工
- 确保标签的准确性和可追溯性

# 分析执行步骤（基于高质量参照文档的思维方式）

**步骤1：批判性信息提取与质疑**
- **数据交叉验证**：将基金经理表述与实际持仓数据进行交叉验证
- **矛盾识别**：主动寻找投资叙事与实际持仓的不一致性
- **深层挖掘**：透过表面现象挖掘背后的真实逻辑和动机

**步骤2：穿透式分析执行**
- **四层分析链条**：现象识别→深层挖掘→矛盾分析→风险预警
- **风险具体化**：将抽象风险转化为可量化的具体风险点
- **前瞻性预警**：预见潜在的策略漂移和执行风险

**步骤3：专业表达与质量控制**
- **高密度专业术语**：大量使用#标签#格式和投资专业术语
- **风险标识逻辑**：🔴高风险🟡观察要点🟢积极信号
- **数据支撑充分**：每个重要判断都要有具体数据支撑

**步骤4：参照文档质量对标**
- **分析深度对标**：确保达到参照文档的穿透式分析水准
- **表达方式对标**：学习参照文档的专业表达和逻辑结构
- **内容完整性对标**：确保所有关键要素都得到充分体现

# 高质量输出标准（基于参照文档优化）

## 专业表达标准

**标签化表达方式**（融合深度分析专精框架的专业标签体系）：
- **核心聚焦领域标签**：准确识别并使用聚焦领域标签
  * 如：`#AI芯片设计与制造领域#` `#新能源汽车电池材料赛道#` `#国潮美妆与个护主题#`
- **驱动因素标签**：
  * 产业趋势类：`#技术迭代升级#` `#产业周期拐点#` `#行业渗透率提升#`
  * 政策事件类：`#国产替代加速#` `#自主可控强化#` `#国家战略支持#`
  * 企业竞争力类：`#核心技术壁垒#` `#强大品牌护城河#` `#独特商业模式#`
- **选股策略标签**：
  * 标的类型：`#聚焦行业龙头#` `#细分赛道领导者#` `#高弹性中小市值#`
  * 竞争力偏好：`#硬科技/核心技术驱动#` `#研发创新能力优先#` `#品牌价值型#`
  * 财务考量：`#高盈利增长预期#` `#估值相对合理#` `#低估值安全边际#`
- **运作特征标签**：
  * 集中度：`#高度集中(聚焦领域内个股)#` `#适度集中(聚焦领域内个股)#`
  * 市值偏好：`#偏好中型成长企业#` `#偏好小型高弹企业#`
  * 结构特征：`#核心-卫星配置特征#` `#聚焦领域内相对分散投资#`
- **风险标识逻辑**：
  * 🔴：高风险、核心矛盾、严重问题、言行不一致
  * 🟡：中等风险、观察要点、平衡挑战、待验证
  * 🟢：积极信号、策略优势、机遇识别、一致性良好

## 估值分析整合标准

**整体估值特征**：
- 基于提供的估值统计信息进行分析
- 参照模式："平均PE(TTM)为20.54，中位数PE(TTM)为21.48"
- 估值水平与投资逻辑的一致性验证

**个股估值要求**：
- **强制格式**：每个个股必须包含"PE(TTM) XX.XX倍，PB X.XX倍"
- 参照示例："PE(TTM) 21.41倍，PB 4.09倍"
- 结合行业特征进行估值合理性分析

## 个股分析深度标准

每个代表性个股必须包含：
- **申万分类**：一级/二级/三级行业归属
- **基金中定位**：权重意义和作用
- **叙事关联性**：与核心投资逻辑的关联
- **估值特征**：具体PE(TTM)、PB数值及分析
- **风险机遇**：🔴🟡🟢标识的风险评估

## 规模约束分析标准

- 提供具体的规模上限数值（参照："11.3亿元"）
- 识别瓶颈股票和流动性约束
- 分析规模约束对投资策略的影响
- 评估未来规模增长的挑战

## JSON数据质量标准

- 33个完整字段，格式严格正确
- Manager_Explicit_Concept_Tags_FromReport包含10个以上高质量概念标签
- 所有字段内容详实且逻辑一致

【输出格式要求】
请严格按照【分析规则】中定义的报告结构输出。确保报告的第7部分（表格数据）是有效的JSON格式。

**质量标准**：

1. **专业术语丰富度**：大量使用投资专业术语，如GARP、核心-卫星配置、流动性约束、护城河、竞争壁垒、ROE、定价权、客户粘性等
2. **标签化表达**：采用`#标签#`格式，如`#企业竞争力类#` `#高盈利增长预期#` `#估值修复预期#` `#聚焦行业龙头#`
3. **风险等级体系**：系统性使用🔴🟡🟢标识，对每个重要观点都包含风险和机遇的平衡评估
4. **估值数据整合**：
   - 第3部分必须包含整体估值特征分析：基于提供的估值统计信息，分析平均PE(TTM)、中位数PE(TTM)、平均PB、中位数PB等
   - 每个个股必须包含"PE(TTM) XX.XX倍，PB X.XX倍"格式，并结合行业特征进行估值合理性分析
5. **分析深度要求**：
   - 每个代表性个股分析至少250字，包含：核心业务、基金中定位、与核心叙事关联性、估值特征、潜在风险/机遇五个维度
   - 概念标签数量：Manager_Explicit_Concept_Tags_FromReport字段必须包含17个以上从季报文本中提炼的概念标签
6. **逻辑一致性**：投资逻辑与持仓选择的高度一致性验证，分析策略执行的有效性

**文本排版要求**：
- 使用清晰的层次结构和段落分隔
- 重要数据和结论使用**粗体**突出
- 合理使用项目符号和编号
- 保持专业的学术报告风格



【基金分析报告】
"""
    return prompt

def _estimate_input_tokens(text: str) -> int:
    """
    对输入文本进行初步的token估算。
    MVP阶段可以采用简单方法，例如：len(text) / 2.5 (一个粗略的中文平均值)
    更精确的方法是使用 tiktoken 库针对特定模型进行计算。
    """
    # 简单估算：假设平均每个token约2.5个字符 (这非常粗略，仅为示例)
    estimated_tokens = int(len(text) / 2.5)
    logger.debug(f"初步估算的输入tokens: {estimated_tokens} (基于字符数: {len(text)})")
    return estimated_tokens

@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=4, max=10),
    retry=retry_if_exception_type((openai.InternalServerError, openai.APIConnectionError, openai.APITimeoutError))
)
def analyze_with_llm(
    fund_code: str,
    fund_name: str,
    fund_manager: str,
    report_end_date: str,
    market_outlook: str,
    operation_analysis: str,
    portfolio_data: str, # 新增持仓数据参数
    sw_industry_info: str, # 新增申万行业分类信息参数
    valuation_summary: str, # 新增估值统计信息参数
    scale_analysis: str, # 新增规模约束分析参数
    analysis_rules: str,
    model_name: str,
    api_key: str,
    api_base: str | None = None, # 可选，用于兼容非OpenAI官方的API服务
    use_streaming: bool = True # 新增：是否使用流式响应，默认启用
) -> str | None:
    """
    使用LLM分析基金数据并生成报告。

    Args:
        fund_code: 基金代码。
        fund_name: 基金名称。
        fund_manager: 基金经理。
        report_end_date: 报告期截止日期。
        market_outlook: 市场展望文本。
        operation_analysis: 运作分析文本。
        portfolio_data: 前十大持仓数据字符串。
        sw_industry_info: 申万行业分类信息字符串。
        valuation_summary: 估值统计信息字符串。
        scale_analysis: 基金规模约束分析字符串。
        analysis_rules: 分析规则 (深度分析专精.md内容)。
        model_name: 要使用的LLM模型名称。
        api_key: LLM API的密钥。
        api_base: (可选) LLM API的基础URL。

    Returns:
        LLM生成的报告内容字符串，如果成功。
        如果发生错误或无法生成报告，则返回None。
    """
    logger.info(f"开始使用LLM (模型: {model_name}) 进行分析 (包含持仓数据)...")

    prompt = _build_llm_prompt(fund_code, fund_name, fund_manager, report_end_date, market_outlook, operation_analysis, portfolio_data, sw_industry_info, valuation_summary, scale_analysis, analysis_rules)

    # 初步token监控 - 输入估算 (IMP 3.4.3)
    estimated_prompt_tokens = _estimate_input_tokens(prompt)
    # (实际的tiktoken使用会更准确，但增加依赖，MVP用简单估算)
    # try:
    #     encoding = tiktoken.encoding_for_model(model_name)
    #     prompt_tokens = len(encoding.encode(prompt))
    #     logger.info(f"使用tiktoken估算的输入tokens: {prompt_tokens}")
    # except Exception as e:
    #     logger.warning(f"无法使用tiktoken为模型 {model_name} 估算token: {e}。将使用字符数估算。")
    #     prompt_tokens = estimated_prompt_tokens # 回退

    try:
        client = openai.OpenAI(
            api_key=api_key,
            base_url=api_base, # 如果为None，则使用OpenAI官方API
            timeout=300.0,  # 5分钟超时
            max_retries=2,  # OpenAI客户端内置重试
            # default_headers={"X-My-Custom-Header": "value"} # (可选) 某些兼容API可能需要自定义头部
        )

        logger.debug(f"发送给LLM的Prompt (部分预览):\n{prompt[:500]}...\n") # 记录部分Prompt以供调试

        # 根据配置选择流式或非流式响应
        logger.info(f"使用{'流式' if use_streaming else '非流式'}响应模式")
        completion = client.chat.completions.create(
            model=model_name,
            messages=[
                {"role": "user", "content": prompt}
            ],
            stream=use_streaming,  # 根据参数决定是否启用流式响应
            temperature=0.3,  # 降低随机性，提高一致性
            max_tokens=16000,  # 大幅增加token限制以确保完整输出
        )

        # 根据响应模式处理结果
        if use_streaming:
            # 处理流式响应
            llm_response_content = ""
            total_chunks = 0

            try:
                logger.info("开始接收流式响应...")
                for chunk in completion:
                    if chunk.choices[0].delta.content is not None:
                        content_chunk = chunk.choices[0].delta.content
                        llm_response_content += content_chunk
                        total_chunks += 1

                        # 每100个chunk记录一次进度
                        if total_chunks % 100 == 0:
                            logger.debug(f"已接收 {total_chunks} 个数据块，当前内容长度: {len(llm_response_content)} 字符")

                    # 检查是否完成
                    if chunk.choices[0].finish_reason is not None:
                        logger.info(f"流式响应完成，原因: {chunk.choices[0].finish_reason}")
                        break

                logger.info(f"流式响应接收完成，总计 {total_chunks} 个数据块，最终内容长度: {len(llm_response_content)} 字符")

                # 检查内容是否完整（是否包含第七部分）
                if "第七部分" not in llm_response_content:
                    logger.warning("流式响应可能不完整：未找到第七部分标记")
                if not llm_response_content.strip().endswith("}") and not llm_response_content.strip().endswith("```"):
                    logger.warning("流式响应可能被截断：内容结尾异常")

            except Exception as stream_error:
                logger.error(f"处理流式响应时发生错误: {stream_error}", exc_info=True)
                # 如果流式处理失败，尝试使用已接收的部分内容
                if llm_response_content.strip():
                    logger.warning(f"使用部分接收的内容继续处理，当前长度: {len(llm_response_content)} 字符")
                else:
                    logger.error("未能接收到任何有效内容")
                    return None
        else:
            # 处理非流式响应（传统模式）
            try:
                # 标准OpenAI格式响应
                if hasattr(completion, 'usage') and completion.usage:
                    logger.info(
                        f"LLM API token使用情况: "
                        f"Prompt tokens: {completion.usage.prompt_tokens}, "
                        f"Completion tokens: {completion.usage.completion_tokens}, "
                        f"Total tokens: {completion.usage.total_tokens}"
                    )

                llm_response_content = completion.choices[0].message.content
                logger.info(f"非流式响应接收完成，内容长度: {len(llm_response_content)} 字符")

                # 检查内容是否完整（是否包含第七部分）
                if "第七部分" not in llm_response_content:
                    logger.warning("非流式响应可能不完整：未找到第七部分标记")
                if not llm_response_content.strip().endswith("}") and not llm_response_content.strip().endswith("```"):
                    logger.warning("非流式响应可能被截断：内容结尾异常")

            except Exception as non_stream_error:
                logger.error(f"处理非流式响应时发生错误: {non_stream_error}", exc_info=True)
                return None

        if not llm_response_content or not llm_response_content.strip():
            logger.error("LLM返回了空内容。")
            return None # MVP策略：LLM返回非预期内容

        logger.info("LLM分析成功完成。")
        return llm_response_content

    except openai.APIConnectionError as e: # 网络问题 (IMP 3.4.4)
        logger.error(f"LLM API连接错误: {e}", exc_info=True)
        return None
    except openai.RateLimitError as e: # 速率限制 (IMP 3.4.4)
        logger.error(f"LLM API速率限制错误: {e}", exc_info=True)
        return None
    except openai.AuthenticationError as e: # API密钥错误 (IMP 3.4.4)
        logger.critical(f"LLM API认证错误 (无效的API Key?): {e}", exc_info=True)
        # 提示用户检查配置，但函数本身返回None让调用者处理终止
        return None
    except openai.APITimeoutError as e: # 请求超时 (IMP 3.4.4)
        logger.error(f"LLM API请求超时: {e}", exc_info=True)
        return None
    except openai.APIError as e: # 其他 OpenAI API 错误 (IMP 3.4.4 - LLM返回错误)
        logger.error(f"LLM API返回错误: {e}", exc_info=True)
        return None
    except Exception as e: # 捕获其他意外错误
        logger.error(f"执行LLM分析时发生意外错误: {e}", exc_info=True)
        return None

if __name__ == '__main__':
    # 用于模块独立测试
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(levelname)s - [%(name)s.%(funcName)s:%(lineno)d] - %(message)s',
        handlers=[logging.StreamHandler()]
    )

    # 需要设置环境变量 LLM_API_KEY, LLM_MODEL_NAME, (可选) OPENAI_API_BASE
    # 例如: export LLM_API_KEY="your_key"
    #       export LLM_MODEL_NAME="gpt-3.5-turbo"
    #       export OPENAI_API_BASE="your_compatible_api_endpoint"

    # 从环境变量加载配置 (模拟main.py中的dotenv加载)
    # load_dotenv() # 如果在测试脚本中也想用.env
    test_api_key = os.getenv("LLM_API_KEY", "sk-testkey_replace_with_real_one_for_testing")
    test_model_name = os.getenv("LLM_MODEL_NAME", "gemini-2.5-pro-exp-03-25") # 使用我们决定的模型
    test_api_base = os.getenv("OPENAI_API_BASE") # 例如 Kimi 的 "https://api.moonshot.cn/v1"

    if test_api_key == "sk-testkey_replace_with_real_one_for_testing":
        logger.warning("使用的是测试API Key，除非API Base指向模拟服务，否则实际调用会失败。")

    sample_market_outlook = "市场持续震荡，结构性机会与风险并存。投资者需保持谨慎乐观，关注政策导向和基本面改善的行业。"
    sample_operation_analysis = "本基金在本报告期内，维持了中性仓位，重点配置了新能源、半导体等高景气行业，并对部分估值过高的个股进行了减持。组合表现基本符合预期。"
    sample_portfolio_data = """- 600519 (贵州茅台 - 食品饮料): 总市值: 2156.78亿元, 市值风格: 大盘价值, PE: 18.5, PE(TTM): 17.8, PB: 10.2, 占净值比例 9.80%
- 601318 (中国平安 - 非银金融): 总市值: 7845.32亿元, 市值风格: 大盘价值, PE: 6.8, PE(TTM): 6.5, PB: 0.9, 占净值比例 7.50%
- 000858 (五 粮 液 - 食品饮料): 总市值: 6789.45亿元, 市值风格: 大盘价值, PE: 15.2, PE(TTM): 14.9, PB: 8.7, 占净值比例 6.20%"""
    sample_rules = """
【分析规则】
1.  回顾与分析：总结基金业绩，对比基准。
2.  展望：管理人对后市的看法。
3.  ... (此处省略其他规则) ...
7.  表格数据：请以JSON格式提供以下表格：
    {
        "top_holdings": [
            {"name": "股票A", "percentage": "5.00%"},
            {"name": "股票B", "percentage": "4.50%"}
        ],
        "sector_allocation": [
            {"sector": "科技", "percentage": "30.00%"},
            {"sector": "消费", "percentage": "25.00%"}
        ]
    }
"""

    sample_sw_industry_info = """- 600519 (贵州茅台): 一级行业: 食品饮料, 二级行业: 白酒, 三级行业: 白酒Ⅲ, 占净值比例: 9.80%
- 601318 (中国平安): 一级行业: 非银金融, 二级行业: 保险, 三级行业: 保险Ⅲ, 占净值比例: 7.50%
- 000858 (五 粮 液): 一级行业: 食品饮料, 二级行业: 白酒, 三级行业: 白酒Ⅲ, 占净值比例: 6.20%"""

    sample_valuation_summary = """估值统计摘要：
- 持仓股票数量: 10只
- 平均PE: 16.8, 中位数PE: 15.2
- 平均PE(TTM): 16.4, 中位数PE(TTM): 14.9
- 平均PB: 6.6, 中位数PB: 8.7
- PE数据覆盖率: 85.5%, PB数据覆盖率: 92.3%"""

    sample_scale_analysis = """整体规模上限约21.5亿元，瓶颈股为人福医药 (流动性约束)。"""

    logger.info(f"--- 测试LLM分析模块 (模型: {test_model_name}, API Base: {test_api_base or 'OpenAI Default'}) ---")
    report = analyze_with_llm(
        "000001.OF",  # 测试基金代码
        "测试基金",    # 测试基金名称
        "测试经理",    # 测试基金经理
        "2024-03-31", # 测试报告期
        sample_market_outlook,
        sample_operation_analysis,
        sample_portfolio_data, # 添加持仓数据
        sample_sw_industry_info, # 添加申万行业分类信息
        sample_valuation_summary, # 添加估值统计信息
        sample_scale_analysis, # 添加规模约束分析信息
        sample_rules,
        test_model_name,
        test_api_key,
        test_api_base
    )

    if report:
        logger.info("LLM分析成功，报告内容 (部分预览):")
        logger.info(report[:1000] + "...")
    else:
        logger.error("LLM分析失败。")