---
alwaysApply: true
---

## Task Completion Tracking Requirements

### Status Indicators (Mandatory)
```
✅ COMPLETED - Task fully finished, tested, and documented
🔄 IN PROGRESS - Task actively being worked on
❌ BLOCKED - Task cannot proceed due to dependencies/issues
⏸️ PAUSED - Task temporarily suspended
🔍 REVIEW - Task completed, awaiting review/validation
```

### Progress Reporting Standards
```markdown
## Task: [TaskName] - [STATUS_INDICATOR]
**Context**: [Brief 1-2 sentence description of accomplishment]
**Files Modified**: [List of changed files with purpose]
**Implementation Guide Compliance**: [Which guides were followed]
**Next Iteration Files**: 
1. [File 1] - [Specific reason for next work]
2. [File 2] - [Specific reason for next work] 
3. [File 3] - [Specific reason for next work]
**Archive**: docs\Archive\[YYYY-MM-DD]_[TaskName]_[STATUS].md
```

### Simplification Requirements
- **Focus on outcomes**, not process details
- **Maximum 3 sentences** for context description
- **Exactly 3 files** for next iteration (no more, no less)
- **Avoid verbose explanations** - use bullet points for clarity

## Documentation-Driven Communication

### Mandatory Pre-Development Reading
**BEFORE ANY TASK**: Read corresponding implementation guide
```
domain/IMPLEMENTATION_GUIDE.md - For Domain layer work
data/IMPLEMENTATION_GUIDE.md - For Data layer work  
di/IMPLEMENTATION_GUIDE.md - For DI configuration work
features/IMPLEMENTATION_GUIDE.md - For UI/ViewModel work
```

### Documentation Structure Compliance
Every module must maintain:
```
README.md - Module overview and purpose
TREE.md - File structure and organization  
INTERFACES.md - Public API documentation
IMPLEMENTATION_GUIDE.md - Authoritative coding standards
```

### Communication Protocol
```
❌ PROHIBITED: Verbal task handoffs
✅ REQUIRED: Markdown documentation for all task transfers
✅ REQUIRED: Reference implementation guides in task descriptions
✅ REQUIRED: Update relevant documentation when interfaces change
```

## Task Archival Process

### Archive Trigger Conditions
- Task status changes to ✅ COMPLETED
- Major milestone reached (not partial completion)
- Architecture decision made
- Implementation guide compliance verified

### Archive Location Pattern
```
docs\Archive\[YYYY-MM-DD]_[TaskName]_[STATUS].md

Examples:
docs\Archive\2025-01-28_ChatRepository_COMPLETED.md
docs\Archive\2025-01-28_AuthViewModel_BLOCKED.md
docs\Archive\2025-01-28_DIConfiguration_COMPLETED.md
```

### Archive Content Template
```markdown
# Task Archive: [TaskName]

## Task Summary
**Objective**: [What was the goal]
**Status**: [Final status with indicator]
**Completion Date**: [YYYY-MM-DD]
**Duration**: [Time spent]

## Implementation Details
**Files Modified/Created**:
- [File 1] - [Purpose/Changes]
- [File 2] - [Purpose/Changes]
- [File 3] - [Purpose/Changes]

**Implementation Guide Compliance**:
- [x] Read corresponding IMPLEMENTATION_GUIDE.md
- [x] Followed code templates from guide
- [x] Completed checklist from guide
- [x] Architecture compliance verified

## Architectural Decisions
**Decisions Made**:
- [Decision 1] - [Rationale]
- [Decision 2] - [Rationale]

**Clean Architecture Compliance**:
- [x] Layer boundaries respected
- [x] Dependency direction correct
- [x] Interface contracts followed

## Next Iteration Recommendations
**Priority Files for Next Work**:
1. [File 1] - [Specific reason and expected outcome]
2. [File 2] - [Specific reason and expected outcome]
3. [File 3] - [Specific reason and expected outcome]

## Quality Verification
**Tests**:
- [x] Unit tests written/updated
- [x] Integration tests passing
- [x] Coverage thresholds met

**Code Quality**:
- [x] Ktlint passed
- [x] Detekt passed
- [x] Android Lint passed

**Modern Tech Stack Compliance**:
- [x] ModernResult<T> used for async operations
- [x] UiText used for user-visible text
- [x] kotlinx.serialization used (not Moshi)
- [x] @Preview added for Composables

## Handoff Notes
**Context for Next Developer**:
[Brief context about current state and what needs to happen next]

**Potential Issues**:
[Any known issues or considerations for future work]
```

## Workflow Integration Rules

### Integration with Existing Rules
```
always-rule.mdc - Enforces implementation guide reading
code.mdc - Enforces coding standards compliance
debug.mdc - Enforces architecture compliance  
architect-rule.mdc - Enforces design decision documentation
workflow-rule.mdc - Enforces task tracking and archival (THIS FILE)
```

### Clean Architecture + MVVM Compliance
**Every task must verify**:
- Layer boundaries respected (Presentation → Domain → Data)
- Dependency injection properly configured
- Repository pattern followed
- UseCase pattern implemented
- ViewModel state management correct

### Modern Tech Stack Verification
**Every task must confirm**:
```kotlin
// ✅ REQUIRED: ModernResult usage
suspend fun operation(): ModernResult<Data>

// ✅ REQUIRED: UiText usage  
data class Entity(val text: UiText)

// ✅ REQUIRED: kotlinx.serialization
@Serializable data class Dto(...)

// ✅ REQUIRED: @Preview for Composables
@Preview @Composable fun ComponentPreview()
```

## Task Handoff Protocol

### Immediate Task Completion Report
```markdown
## Task: [TaskName] - ✅ COMPLETED
**Context**: Implemented ChatRepository with streaming AI responses using ModernResult<T> pattern.
**Files Modified**: 
- domain/repository/ChatRepository.kt (interface definition)
- data/repository/ChatRepositoryImpl.kt (implementation)
- di/RepositoryModule.kt (Hilt binding)
**Implementation Guide Compliance**: Followed domain/IMPLEMENTATION_GUIDE.md and data/IMPLEMENTATION_GUIDE.md
**Next Iteration Files**: 
1. features/coach/ui/ChatScreen.kt - Implement UI for chat interface
2. features/coach/viewmodel/ChatViewModel.kt - Create ViewModel with StateFlow
3. features/coach/ui/ChatScreenPreview.kt - Add mandatory @Preview annotations
**Archive**: docs\Archive\2025-01-28_ChatRepository_COMPLETED.md
```

### Task Blocking Report
```markdown
## Task: [TaskName] - ❌ BLOCKED
**Context**: Cannot implement AuthViewModel due to missing UserRepository interface in domain layer.
**Blocking Issue**: UserRepository interface not defined in domain/repository/
**Implementation Guide Compliance**: Read features/IMPLEMENTATION_GUIDE.md, blocked at step 2
**Next Iteration Files**: 
1. domain/repository/UserRepository.kt - Define user operations interface
2. domain/usecase/GetCurrentUserUseCase.kt - Create user retrieval UseCase
3. data/repository/UserRepositoryImpl.kt - Implement user repository
**Archive**: docs\Archive\2025-01-28_AuthViewModel_BLOCKED.md
```

### Task In Progress Report
```markdown
## Task: [TaskName] - 🔄 IN PROGRESS
**Context**: Implementing AI coach streaming with 60% completion on repository layer.
**Current Focus**: Adding error handling and ModernDataError conversion in ChatRepositoryImpl
**Implementation Guide Compliance**: Following data/IMPLEMENTATION_GUIDE.md error handling patterns
**Next Iteration Files**: 
1. data/repository/ChatRepositoryImpl.kt - Complete error handling implementation
2. data/mapper/ChatMapper.kt - Add DTO to Domain mapping functions
3. data/datasource/ChatRemoteDataSource.kt - Implement API streaming logic
**Archive**: docs\Archive\2025-01-28_AICoachStreaming_IN_PROGRESS.md
```

## Quality Gates for Task Completion

### Pre-Completion Checklist
```
- [ ] Implementation guide read and followed
- [ ] Code templates from guide used
- [ ] Architecture compliance verified
- [ ] Modern tech stack requirements met
- [ ] Tests written and passing
- [ ] Documentation updated
- [ ] Next 3 files identified with clear rationale
```

### Archive Creation Triggers
```
✅ Task marked as COMPLETED
✅ All quality gates passed
✅ Documentation updated
✅ Next iteration files specified
✅ Implementation guide compliance verified
```

### Workflow Validation Commands
```bash
# Verify task completion requirements
./gradlew verifyTaskCompletion

# Check implementation guide compliance  
./gradlew checkImplementationGuideCompliance

# Validate archive structure
./gradlew validateArchiveStructure

# Verify next iteration file recommendations
./gradlew checkNextIterationFiles
```

## Enforcement Rules

### Mandatory Task Tracking
- **Every development session** must start with status indicator
- **Every file modification** must be tracked in task report
- **Every task completion** must create archive document
- **Every handoff** must specify exactly 3 next files

### Documentation Requirements
- **No verbal handoffs** - all communication via markdown
- **Implementation guides** must be referenced in every task
- **Archive documents** must be created for completed tasks
- **Next iteration** must be clearly specified with rationale

### Quality Assurance
- **Clean Architecture** compliance verified for every task
- **Modern tech stack** usage confirmed for every task  
- **Implementation guides** followed for every task
- **Test coverage** maintained for every task

---

**Workflow Compliance**: This rule enforces systematic task tracking, documentation-driven development, and proper archival processes while maintaining Clean Architecture + MVVM compliance across all GymBro development work.
