# 基金概念知识图谱数据模型设计

## 1. 整体架构

### 1.1 设计原则
- **实体-关系模型**：采用图数据库的实体-关系模型
- **多层次结构**：支持概念的层次化组织
- **时间维度**：记录概念和关系的时间演化
- **动态扩展**：支持新实体类型和关系类型的动态添加
- **语义丰富**：每个实体和关系都包含丰富的语义信息

### 1.2 存储格式选择
建议采用JSON格式存储，便于集成到现有系统，同时保留向图数据库迁移的可能性。

## 2. 实体类型定义

### 2.1 概念实体 (Concept)
```json
{
  "id": "concept_001",
  "type": "Concept",
  "name": "创新药",
  "full_name": "医药健康领域(主打创新药与AI医疗)",
  "category": "investment_theme",
  "level": "specific",  // general, specific, detailed
  "description": "专注于创新药物研发和投资的概念",
  "keywords": ["创新药", "新药研发", "医药创新"],
  "industry_tags": ["医药生物", "生物制药"],
  "technology_tags": ["生物技术", "药物研发"],
  "first_seen": "2025-06-02T16:33:17.028685",
  "last_updated": "2025-06-02T21:18:53.556865",
  "frequency": 5,
  "confidence_score": 0.95,
  "extraction_source": "auto_extracted",
  "validation_status": "confirmed"  // pending, confirmed, rejected
}
```

### 2.2 基金实体 (Fund)
```json
{
  "id": "fund_001",
  "type": "Fund",
  "code": "014841.OF",
  "name": "基金名称",
  "fund_type": "股票型",
  "investment_style": "成长型",
  "market_focus": ["A股", "港股"],
  "scale_range": "中型",
  "manager_info": {
    "name": "基金经理姓名",
    "experience_years": 5
  },
  "first_analyzed": "2025-06-02T16:33:17.028685",
  "last_analyzed": "2025-06-02T21:18:53.556865",
  "analysis_count": 3
}
```

### 2.3 行业实体 (Industry)
```json
{
  "id": "industry_001",
  "type": "Industry",
  "name": "医药生物",
  "level": "L1",  // L1, L2, L3 (申万行业分类)
  "parent_industry": null,
  "sub_industries": ["生物制药", "化学制药", "医疗器械"],
  "sw_code": "801150",
  "description": "医药生物行业分类"
}
```

### 2.4 技术实体 (Technology)
```json
{
  "id": "tech_001",
  "type": "Technology",
  "name": "人工智能",
  "alias": ["AI", "机器学习", "深度学习"],
  "maturity_level": "成长期",  // 萌芽期, 成长期, 成熟期
  "application_areas": ["医疗", "金融", "制造"],
  "related_concepts": ["AI医疗", "智能制造"]
}
```

### 2.5 时间节点实体 (TimeNode)
```json
{
  "id": "time_001",
  "type": "TimeNode",
  "date": "2025-06-02",
  "quarter": "2025Q2",
  "year": "2025",
  "market_events": ["政策发布", "行业会议"],
  "significance": "重要概念提取节点"
}
```

## 3. 关系类型定义

### 3.1 基金-概念关系 (FOCUSES_ON)
```json
{
  "id": "rel_001",
  "type": "FOCUSES_ON",
  "source": "fund_001",
  "target": "concept_001",
  "weight": 0.8,  // 关注强度 0-1
  "first_mentioned": "2025-06-02T16:33:17.028685",
  "last_mentioned": "2025-06-02T21:18:53.556865",
  "mention_count": 3,
  "context": "基金在报告中重点提及该概念",
  "sentiment": "positive",  // positive, neutral, negative
  "confidence": 0.9
}
```

### 3.2 概念-概念关系 (RELATED_TO)
```json
{
  "id": "rel_002",
  "type": "RELATED_TO",
  "source": "concept_001",
  "target": "concept_002",
  "relation_subtype": "semantic_similar",  // semantic_similar, co_occurrence, hierarchical
  "similarity_score": 0.75,
  "co_occurrence_count": 5,
  "relationship_strength": "strong",  // weak, medium, strong
  "discovered_method": "nlp_analysis",
  "validation_status": "auto_detected"
}
```

### 3.3 概念层次关系 (CONTAINS/PART_OF)
```json
{
  "id": "rel_003",
  "type": "CONTAINS",
  "source": "concept_parent",
  "target": "concept_child",
  "hierarchy_level": 2,
  "containment_type": "thematic",  // thematic, industrial, technological
  "confidence": 0.85
}
```

### 3.4 概念-行业关系 (BELONGS_TO)
```json
{
  "id": "rel_004",
  "type": "BELONGS_TO",
  "source": "concept_001",
  "target": "industry_001",
  "relevance_score": 0.9,
  "mapping_method": "keyword_matching",
  "validation_status": "confirmed"
}
```

### 3.5 概念-技术关系 (INVOLVES)
```json
{
  "id": "rel_005",
  "type": "INVOLVES",
  "source": "concept_001",
  "target": "tech_001",
  "involvement_level": "core",  // peripheral, important, core
  "technology_role": "enabling",  // enabling, supporting, transforming
  "maturity_requirement": "成长期"
}
```

### 3.6 时间演化关系 (EVOLVES_TO)
```json
{
  "id": "rel_006",
  "type": "EVOLVES_TO",
  "source": "concept_old",
  "target": "concept_new",
  "evolution_type": "refinement",  // refinement, expansion, transformation
  "time_span": "3_months",
  "evolution_trigger": "market_change",
  "confidence": 0.7
}
```

## 4. 图谱结构组织

### 4.1 整体JSON结构
```json
{
  "metadata": {
    "version": "1.0",
    "created_at": "2025-06-02T21:30:00.000000",
    "last_updated": "2025-06-02T21:30:00.000000",
    "total_entities": 150,
    "total_relationships": 300,
    "data_sources": ["concept_tags_dynamic", "fund_reports", "industry_mapping"]
  },
  "entities": {
    "concepts": {},
    "funds": {},
    "industries": {},
    "technologies": {},
    "time_nodes": {}
  },
  "relationships": {
    "focuses_on": {},
    "related_to": {},
    "contains": {},
    "belongs_to": {},
    "involves": {},
    "evolves_to": {}
  },
  "indexes": {
    "by_name": {},
    "by_type": {},
    "by_time": {},
    "by_frequency": {}
  }
}
```

## 5. 扩展性设计

### 5.1 新实体类型扩展
- 支持通过配置文件定义新的实体类型
- 每个实体类型包含必需字段和可选字段定义
- 支持实体类型的继承关系

### 5.2 新关系类型扩展
- 支持动态定义新的关系类型
- 关系类型包含约束规则（哪些实体类型可以建立此关系）
- 支持关系的方向性和权重配置

### 5.3 动态更新机制
- 增量更新：只更新变化的部分
- 版本控制：保留历史版本用于回滚
- 冲突解决：定义数据冲突的解决策略

这个数据模型设计如何？有哪些地方需要调整或补充？
