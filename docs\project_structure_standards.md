# 项目结构规范和分类标准

> **重要提醒**：本文档定义了项目文件的分类标准和命名规范，避免重复整理和分类混乱。
> **最后更新**：2025-06-04
> **维护者**：项目团队

## 📁 目录结构标准

### 核心原则
1. **功能导向**：按功能和用途分类，不按文件类型
2. **生命周期管理**：区分活跃文件、历史文件和归档文件
3. **专业命名**：避免歧义，体现文件的正式地位
4. **文档完整**：每个目录都有README说明

### 标准目录结构

```
项目根目录/
├── src/                    # 源代码（核心业务逻辑）
├── config/                 # 配置文件（JSON/YAML格式）
├── tools/                  # 工具和指南
│   ├── config/            # 配置管理工具
│   ├── guides/            # 使用指南和集成教程
│   └── [工具名]_project.py # 通用工具
├── tests/                  # 测试文件（有效的测试代码）
├── docs/                   # 文档（Markdown格式）
├── data/                   # 数据文件（示例和生产数据）
├── modules/                # 扩展模块（可选功能）
├── output/                 # 程序输出（自动生成）
├── reports/                # 报告输出（自动生成）
├── archive/                # 归档文件（历史和废弃文件）
├── memory-bank/           # AI助手记忆库
└── updates/               # 项目更新日志
```

## 🗂️ 文件分类标准

### 1. 源代码 (src/)
**标准**：
- ✅ 核心业务逻辑模块
- ✅ 主要功能实现
- ✅ 生产环境使用的代码

**命名规范**：
- `[功能名]_[类型].py`
- 例：`data_fetcher.py`, `llm_analyzer.py`

### 2. 配置文件 (config/)
**标准**：
- ✅ JSON/YAML格式的配置
- ✅ 程序运行时需要的配置
- ✅ 可以被代码直接读取

**命名规范**：
- `[功能名]_config.json`
- 例：`parallel_config.json`, `market_cap_config.json`

### 3. 工具和指南 (tools/)

#### 3.1 配置管理工具 (tools/config/)
**标准**：
- ✅ 管理配置文件的工具
- ✅ 运行时配置调整工具

**命名规范**：
- `[功能名]_tool.py`
- 例：`config_tool.py`

#### 3.2 使用指南 (tools/guides/)
**标准**：
- ✅ 正式的使用指南和集成教程
- ✅ API演示和最佳实践
- ✅ 具有文档和教育价值
- ❌ 不是临时示例或演示代码

**命名规范**：
- `[功能名]_integration_guide.py`
- 例：`fund_scale_integration_guide.py`

**重要区别**：
- ❌ 避免使用 `example_` 前缀（暗示临时性）
- ❌ 避免使用 `test_` 前缀（容易与测试混淆）
- ✅ 使用 `_guide` 或 `_tutorial` 后缀

#### 3.3 通用工具 (tools/)
**标准**：
- ✅ 项目维护和管理工具
- ✅ 不属于特定功能的通用工具

**命名规范**：
- `[功能名]_project.py`
- 例：`cleanup_project.py`

### 4. 测试文件 (tests/)
**标准**：
- ✅ 有效的单元测试
- ✅ 集成测试
- ✅ 性能测试
- ❌ 临时调试脚本

**命名规范**：
- `test_[功能名].py`
- 例：`test_parallel.py`, `test_wind_api.py`

### 5. 文档 (docs/)
**标准**：
- ✅ Markdown格式的文档
- ✅ 技术规范和设计文档
- ✅ 用户指南和API文档

**命名规范**：
- `[文档名].md`
- 子目录：`[分类]/[文档名].md`

### 6. 数据文件 (data/)
**标准**：
- ✅ 示例数据文件
- ✅ 生产环境数据配置
- ✅ 模板文件

**命名规范**：
- `[文件名].txt.example` - 示例文件
- `[文件名]_production.txt` - 生产文件

### 7. 归档文件 (archive/)
**标准**：
- ✅ 历史版本的配置文件
- ✅ 废弃的测试脚本
- ✅ 过时的模块代码
- ✅ 临时备份文件

**命名规范**：
- 配置备份：`.[原文件名]_backup_[YYYYMMDD]`
- 版本文件：`[文件名]_v[版本号].py`
- 历史文档：`[文档名]v[版本号].md`

## ⚠️ 分类决策指南

### 何时放入 tools/guides/
**判断标准**：
1. 是否是正式的使用指南？
2. 是否具有教育和文档价值？
3. 是否展示最佳实践？
4. 是否会被长期维护？

**如果答案都是"是"** → `tools/guides/`

### 何时放入 tests/
**判断标准**：
1. 是否是正式的测试代码？
2. 是否会在CI/CD中运行？
3. 是否验证功能正确性？

**如果答案都是"是"** → `tests/`

### 何时放入 archive/
**判断标准**：
1. 是否已被新功能替代？
2. 是否不再被主程序使用？
3. 是否是临时调试代码？
4. 是否是历史版本备份？

**如果任一答案是"是"** → `archive/`

## 📝 重要决策记录

### 2025-06-04 重命名决策
**问题**：`tools/scripts/example_fund_scale_usage.py` 命名有歧义
**分析**：
- 文件实际是正式的集成指南（218行高质量代码）
- "example_" 前缀暗示临时性
- "scripts" 目录名暗示工具脚本

**解决方案**：
- 文件夹：`scripts/` → `guides/`
- 文件：`example_fund_scale_usage.py` → `fund_scale_integration_guide.py`

**原则确立**：
- 使用指南类文件统一放在 `tools/guides/`
- 命名格式：`[功能名]_integration_guide.py`
- 避免 `example_` 前缀，避免歧义

### 2025-06-04 .env文件整理
**问题**：data文件夹中有多个历史.env文件
**解决方案**：移动到archive文件夹
**命名规范**：`.env_[来源]_backup_[YYYYMMDD]`

### 2025-06-04 基金列表文件规范化
**问题**：fund_list.txt文件在根目录，不符合data文件夹的设计用途
**分析**：
- data文件夹设计用于存放数据文件
- 根目录的fund_list.txt违反了目录结构规范
- 工作文件应该在data文件夹中，不应提交到版本控制

**解决方案**：
- 移动：根目录`fund_list.txt` → `data/fund_list.txt`
- 更新：main.py和data_fetcher.py中的默认路径
- 更新：.gitignore中的忽略路径
- 更新：示例文件和文档中的说明

**影响**：
- ✅ data文件夹恢复标准用途（数据文件存储）
- ✅ 工作文件与示例文件在同一目录，便于管理
- ✅ 符合项目结构规范

## 🔄 定期维护

### 季度检查清单
- [ ] 检查archive文件夹，清理过期文件
- [ ] 验证tools/guides/中的文件是否仍然有效
- [ ] 更新README文件中的目录结构
- [ ] 检查命名规范的执行情况

### 年度审查
- [ ] 重新评估目录结构的合理性
- [ ] 更新分类标准
- [ ] 整理历史决策记录

---

> **提醒**：任何结构调整都应该更新本文档，确保标准的一致性和可追溯性。
