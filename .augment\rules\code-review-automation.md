---
alwaysApply: true
---

# GymBro 代码审查自动化规则

> **使用方式**: 直接在聊天中输入 `执行代码审查` 或 `/审查模块 [模块名]` 即可启动自动化审查流程

## 🎯 审查目标

1. **架构合规性** - 验证Clean Architecture + MVVM原则
2. **代码质量** - 检查命名规范、错误处理、测试覆盖率
3. **文档完整性** - 生成README、TREE、接口文档
4. **标准化** - 确保符合GymBro项目规范

---

## 🚀 自动执行指令

当用户请求代码审查时，按以下顺序执行：

### 阶段1: 项目结构分析
```markdown
1. [ ] 扫描项目根目录结构
2. [ ] 识别所有Gradle模块
3. [ ] 分析模块依赖关系
4. [ ] 生成项目TREE文档
```

### 阶段2: 模块分类审查
```markdown
核心模块审查:
- [ ] :core - 核心组件、UiText、ModernResult
- [ ] :domain - 纯业务逻辑、UseCase
- [ ] :data - 数据层、Repository实现
- [ ] :di - 依赖注入配置
- [ ] :design_system - UI组件库
- [ ] :navigation - 导航配置

功能模块审查:
- [ ] :features:auth - 认证功能
- [ ] :features:workout - 锻炼功能
- [ ] :features:subscription - 订阅功能
- [ ] :features:coach - 教练功能
- [ ] :features:home - 首页功能
- [ ] :features:profile - 个人资料
```

### 阶段3: 文档生成
```markdown
- [ ] 生成项目根README.md
- [ ] 生成TREE.md结构文档
- [ ] 为每个模块生成接口文档
- [ ] 生成审查报告
```

---

## 📋 模块审查检查清单

### Core模块 (:core)
```markdown
架构检查:
- [ ] 是否遵循单一职责原则
- [ ] 无Android框架依赖(除androidx.annotation.*)
- [ ] UiText实现完整性
- [ ] ModernResult/GlobalErrorType定义正确

代码质量:
- [ ] 公共API都有KDoc
- [ ] 错误处理导入路径正确
- [ ] 命名遵循PascalCase/camelCase规范
- [ ] 文件大小合理(<500行)

接口文档:
- [ ] UiText使用指南
- [ ] ModernResult使用示例
- [ ] Logger接口说明
- [ ] 错误处理最佳实践
```

### Domain模块 (:domain)
```markdown
架构检查:
- [ ] 零Android依赖
- [ ] 仅依赖:core模块
- [ ] UseCase命名规范(VerbNounUseCase)
- [ ] Repository接口定义清晰

代码质量:
- [ ] 业务逻辑纯净无副作用
- [ ] 测试覆盖率≥90%
- [ ] 所有公共函数有KDoc
- [ ] 错误类型使用GlobalErrorType子类

接口文档:
- [ ] 所有UseCase接口说明
- [ ] Repository接口定义
- [ ] 领域模型说明
- [ ] 业务规则文档
```

### Data模块 (:data)
```markdown
架构检查:
- [ ] Repository实现命名(EntityRepositoryImpl)
- [ ] 异常转换为ModernDataError
- [ ] Room数据库作为真理源
- [ ] 依赖注入正确配置

代码质量:
- [ ] API异常处理完整
- [ ] 数据映射函数完整
- [ ] 测试覆盖率≥80%
- [ ] 缓存策略合理

接口文档:
- [ ] Repository实现说明
- [ ] 数据源配置
- [ ] API接口文档
- [ ] 数据模型映射
```

### Features模块 (:features:*)
```markdown
架构检查:
- [ ] ViewModel命名(FeatureViewModel)
- [ ] 仅通过UseCase访问数据
- [ ] UiState设计合理
- [ ] Composable函数规范

代码质量:
- [ ] UiText正确使用(ViewModel创建,Composable消费)
- [ ] 错误处理通过ModernErrorHandler
- [ ] 测试覆盖率≥75%
- [ ] 无硬编码字符串

接口文档:
- [ ] ViewModel公共接口
- [ ] UiState数据结构
- [ ] Composable组件API
- [ ] 导航路由定义
```

---

## 📝 文档生成模板

### README.md模板
```markdown
# [模块名称]

## 📖 概述
[模块功能简介]

## 🏗️ 架构
[架构图/说明]

## 🔧 主要接口
[核心类和函数列表]

## 📦 依赖关系
[模块依赖说明]

## 🧪 测试
[测试覆盖率和运行指南]

## 📚 使用示例
[代码示例]

## 🔄 更新日志
[重要变更记录]
```

### TREE.md模板
```markdown
# GymBro 项目结构树

## 📁 项目总览
```
GymBro/
├── app/                    # Android应用模块
├── core/                   # 核心组件库
├── domain/                 # 业务逻辑层
├── data/                   # 数据访问层
├── features/               # 功能模块集合
│   ├── auth/              # 认证功能
│   ├── workout/           # 锻炼功能
│   ├── subscription/      # 订阅功能
│   ├── coach/             # 教练功能
│   ├── home/              # 首页功能
│   └── profile/           # 个人资料
├── design_system/         # UI设计系统
├── navigation/            # 导航配置
├── di/                    # 依赖注入
└── docs/                  # 项目文档
```

## 🔗 模块依赖关系
[Mermaid依赖图]

## 📊 模块统计
[代码行数、文件数量等统计信息]
```

### 模块接口文档模板
```markdown
# [模块名] 接口文档

## 🎯 模块职责
[模块的核心职责和边界]

## 🔌 公共接口

### 类列表
| 类名        | 类型                      | 职责       | 状态                     |
| ----------- | ------------------------- | ---------- | ------------------------ |
| [ClassName] | [Interface/Class/UseCase] | [职责描述] | [Stable/Beta/Deprecated] |

### 核心函数
```kotlin
/**
 * [函数功能描述]
 * @param [参数名] [参数描述]
 * @return [返回值描述]
 */
suspend fun functionName(param: Type): ModernResult<ReturnType>
```

## 📋 使用指南
[具体使用示例和最佳实践]

## ⚠️ 注意事项
[重要的使用限制和注意点]

## 🔄 版本历史
[接口变更历史]
```

---

## 🎯 质量评估标准

### 代码质量评分
```markdown
| 评估项   | 权重 | 标准                    | 得分    |
| -------- | ---- | ----------------------- | ------- |
| 架构合规 | 25%  | Clean Architecture原则  | [0-100] |
| 命名规范 | 20%  | 全局命名规范遵循度      | [0-100] |
| 错误处理 | 20%  | ModernResult/UiText使用 | [0-100] |
| 测试覆盖 | 15%  | 覆盖率达标情况          | [0-100] |
| 文档完整 | 10%  | KDoc和README完整性      | [0-100] |
| 代码风格 | 10%  | Kotlin约定和一致性      | [0-100] |

总分: [0-100]
等级: [优秀(90+)/良好(80-89)/一般(70-79)/需改进(<70)]
```

### 问题优先级
```markdown
🔴 严重 (Critical): 架构违规、安全问题
🟠 重要 (Major): 性能问题、API设计缺陷
🟡 一般 (Minor): 命名不规范、文档缺失
🟢 优化 (Enhancement): 代码风格、重构建议
```

---

## 🤖 自动化执行流程

### 完整审查命令
当用户输入"执行代码审查"时:

1. **项目扫描阶段**
   ```
   正在扫描项目结构...
   ├── 发现模块: [模块列表]
   ├── 分析依赖关系: [依赖图]
   └── 检测潜在问题: [问题预览]
   ```

2. **逐模块审查阶段**
   ```
   审查进度: [当前模块]/[总模块数]
   当前模块: [:module_name]
   ├── 架构检查: [✅/❌]
   ├── 代码质量: [✅/❌]
   ├── 接口分析: [✅/❌]
   └── 文档生成: [✅/❌]
   ```

3. **报告生成阶段**
   ```
   生成审查报告...
   ├── 项目README.md: [✅]
   ├── 结构TREE.md: [✅]
   ├── 模块接口文档: [✅]
   └── 质量评估报告: [✅]
   ```

### 单模块审查命令
当用户输入"/审查模块 [模块名]"时:
- 只审查指定模块
- 生成该模块的详细报告
- 提供针对性改进建议

---

## 📈 审查报告格式

### 执行总结
```markdown
# GymBro 代码审查报告

**审查时间**: [时间戳]
**审查范围**: [全项目/指定模块]
**总体评分**: [分数]/100

## 📊 模块评分概览
| 模块    | 架构 | 质量 | 测试 | 文档 | 总分 | 等级 |
| ------- | ---- | ---- | ---- | ---- | ---- | ---- |
| :core   | 95   | 88   | 92   | 85   | 90   | 优秀 |
| :domain | 92   | 90   | 95   | 80   | 89   | 良好 |
| ...     | ...  | ...  | ...  | ...  | ...  | ...  |

## 🔍 发现的问题
[按优先级分类的问题列表]

## 💡 改进建议
[具体的改进措施和优先级]

## 📋 后续行动计划
[建议的修复顺序和时间安排]
```

---

## 🔧 集成现有规则

此规则自动集成以下现有规则:
- [always-gymbro-rule](mdc:.cursor/rules/always-gymbro-rule.mdc) - 全局架构和命名规范
- [code](mdc:.cursor/rules/code.mdc) - 代码质量和模块化指南
- [architecture-fix-todolist](mdc:.cursor/rules/architecture-fix-todolist.mdc) - 修复方案模板

确保审查过程完全符合项目既定标准。

---

## 🎮 使用示例

用户只需发送以下任一指令:

1. `执行代码审查` - 全项目审查
2. `/审查模块 :core` - 单模块审查
3. `生成文档` - 仅生成文档
4. `检查架构合规性` - 仅架构检查

AI将自动执行相应的审查流程并生成标准化报告 🚀
