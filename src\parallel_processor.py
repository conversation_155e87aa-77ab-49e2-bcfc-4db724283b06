# 简化的并行LLM处理器
# 最小修改，保持向后兼容

import logging
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any, Optional
import queue

logger = logging.getLogger(__name__)

class SimpleLLMParallelProcessor:
    """
    简化的LLM并行处理器
    - 最小修改现有代码
    - 保持完全向后兼容
    - 简单的线程池实现
    """
    
    def __init__(self, max_workers: int = 2, enable_parallel: bool = True, request_interval: float = 2.0):
        """
        初始化并行处理器

        Args:
            max_workers: 最大并发数，建议2-3个
            enable_parallel: 是否启用并行，False时退回到串行模式
            request_interval: 请求间隔时间（秒），用于避免API过载
        """
        self.max_workers = max_workers
        self.enable_parallel = enable_parallel
        self.request_interval = request_interval
        self.executor = None
        self.last_request_time = 0
        self.request_lock = threading.Lock()

        if self.enable_parallel:
            self.executor = ThreadPoolExecutor(max_workers=max_workers)
            logger.info(f"LLM并行处理器已启用，最大并发数: {max_workers}，请求间隔: {request_interval}秒")
        else:
            logger.info("LLM并行处理器已禁用，使用串行模式")
    
    def process_batch_funds(self, fund_analysis_tasks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        批量处理基金分析任务
        
        Args:
            fund_analysis_tasks: 基金分析任务列表，每个任务包含:
                - fund_code: 基金代码
                - analysis_function: 分析函数
                - analysis_args: 分析函数参数
                - analysis_kwargs: 分析函数关键字参数
        
        Returns:
            分析结果列表，每个结果包含:
                - fund_code: 基金代码
                - success: 是否成功
                - result: 分析结果（成功时）
                - error: 错误信息（失败时）
                - duration: 处理时间
        """
        if not self.enable_parallel or len(fund_analysis_tasks) == 1:
            # 串行处理（兼容模式）
            return self._process_serial(fund_analysis_tasks)
        else:
            # 并行处理
            return self._process_parallel(fund_analysis_tasks)
    
    def _process_serial(self, tasks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """串行处理（原有逻辑）"""
        results = []
        
        for task in tasks:
            fund_code = task['fund_code']
            logger.info(f"串行处理基金: {fund_code}")
            
            start_time = time.time()
            try:
                # 执行分析函数
                analysis_function = task['analysis_function']
                args = task.get('analysis_args', [])
                kwargs = task.get('analysis_kwargs', {})
                
                result = analysis_function(*args, **kwargs)
                
                results.append({
                    'fund_code': fund_code,
                    'success': True,
                    'result': result,
                    'error': None,
                    'duration': time.time() - start_time
                })
                
                logger.info(f"基金 {fund_code} 分析完成，耗时: {time.time() - start_time:.1f}秒")
                
            except Exception as e:
                logger.error(f"基金 {fund_code} 分析失败: {e}", exc_info=True)
                results.append({
                    'fund_code': fund_code,
                    'success': False,
                    'result': None,
                    'error': str(e),
                    'duration': time.time() - start_time
                })
        
        return results
    
    def _process_parallel(self, tasks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """并行处理"""
        logger.info(f"开始并行处理 {len(tasks)} 个基金，并发数: {self.max_workers}")
        
        # 提交所有任务
        future_to_task = {}
        for task in tasks:
            future = self.executor.submit(self._execute_single_task, task)
            future_to_task[future] = task
        
        # 收集结果
        results = []
        completed_count = 0
        
        for future in as_completed(future_to_task):
            task = future_to_task[future]
            fund_code = task['fund_code']
            
            try:
                result = future.result()
                results.append(result)
                completed_count += 1
                
                if result['success']:
                    logger.info(f"基金 {fund_code} 分析完成 ({completed_count}/{len(tasks)})，耗时: {result['duration']:.1f}秒")
                else:
                    logger.error(f"基金 {fund_code} 分析失败 ({completed_count}/{len(tasks)}): {result['error']}")
                    
            except Exception as e:
                logger.error(f"基金 {fund_code} 任务执行异常: {e}", exc_info=True)
                results.append({
                    'fund_code': fund_code,
                    'success': False,
                    'result': None,
                    'error': f"任务执行异常: {str(e)}",
                    'duration': 0
                })
                completed_count += 1
        
        # 按原始顺序排序结果
        task_order = {task['fund_code']: i for i, task in enumerate(tasks)}
        results.sort(key=lambda x: task_order.get(x['fund_code'], 999))
        
        return results
    
    def _wait_for_rate_limit(self):
        """等待请求间隔，避免API过载"""
        with self.request_lock:
            current_time = time.time()
            time_since_last_request = current_time - self.last_request_time

            if time_since_last_request < self.request_interval:
                sleep_time = self.request_interval - time_since_last_request
                logger.debug(f"等待 {sleep_time:.2f} 秒以满足请求间隔要求")
                time.sleep(sleep_time)

            self.last_request_time = time.time()

    def _execute_single_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """执行单个分析任务"""
        fund_code = task['fund_code']
        start_time = time.time()

        try:
            # 等待请求间隔
            self._wait_for_rate_limit()

            # 执行分析函数
            analysis_function = task['analysis_function']
            args = task.get('analysis_args', [])
            kwargs = task.get('analysis_kwargs', {})

            result = analysis_function(*args, **kwargs)

            return {
                'fund_code': fund_code,
                'success': True,
                'result': result,
                'error': None,
                'duration': time.time() - start_time
            }

        except Exception as e:
            return {
                'fund_code': fund_code,
                'success': False,
                'result': None,
                'error': str(e),
                'duration': time.time() - start_time
            }
    
    def close(self):
        """关闭线程池"""
        if self.executor:
            self.executor.shutdown(wait=True)
            logger.info("LLM并行处理器已关闭")

# 便捷函数：创建分析任务
def create_analysis_task(analysis_function, **kwargs) -> Dict[str, Any]:
    """
    创建分析任务

    Args:
        analysis_function: 分析函数
        **kwargs: 传递给分析函数的关键字参数（必须包含fund_code参数）

    Returns:
        分析任务字典
    """
    # 从kwargs中提取fund_code作为任务标识
    fund_code = kwargs.get('fund_code')
    if not fund_code:
        raise ValueError("kwargs中必须包含fund_code参数")

    return {
        'fund_code': fund_code,
        'analysis_function': analysis_function,
        'analysis_args': [],  # 不使用位置参数，全部使用关键字参数
        'analysis_kwargs': kwargs
    }

# 便捷函数：简单的批量分析
def simple_parallel_analysis(fund_tasks: List[Dict[str, Any]],
                            max_workers: int = 2,
                            enable_parallel: bool = True) -> List[Dict[str, Any]]:
    """
    简单的批量并行分析
    
    Args:
        fund_tasks: 基金分析任务列表
        max_workers: 最大并发数
        enable_parallel: 是否启用并行
    
    Returns:
        分析结果列表
    """
    processor = SimpleLLMParallelProcessor(max_workers=max_workers, enable_parallel=enable_parallel)
    
    try:
        results = processor.process_batch_funds(fund_tasks)
        return results
    finally:
        processor.close()
