"""
基金规模估算集成模块
提供简化的接口供主系统调用

主要功能：
1. 从基金持仓数据中提取市值和权重信息
2. 调用动态模型进行规模估算
3. 格式化结果供LLM分析使用

作者: AI Assistant
版本: V3.0
"""

import logging
from typing import Dict, List, Optional
try:
    from .fund_scale_estimator import FundScaleEstimator
except ImportError:
    from fund_scale_estimator import FundScaleEstimator

class FundScaleIntegration:
    """基金规模估算集成接口"""

    def __init__(self):
        """初始化集成模块"""
        self.estimator = FundScaleEstimator()
        self.logger = logging.getLogger(__name__)

    def extract_holdings_data(self, portfolio_data: Dict) -> List[Dict]:
        """
        从基金组合数据中提取重仓股信息

        Args:
            portfolio_data: 基金组合数据，包含持仓信息

        Returns:
            List[Dict]: 重仓股列表，格式为 [{'stock_name': str, 'market_cap_yi': float, 'weight': float}]
        """
        holdings = []

        try:
            # 假设portfolio_data包含top_holdings字段
            top_holdings = portfolio_data.get('top_holdings', [])

            for holding in top_holdings:
                # 提取必要信息
                stock_name = holding.get('stock_name', holding.get('name', '未知股票'))

                # 市值处理 - 假设原始数据可能是万元或亿元
                market_cap = holding.get('market_cap', 0)
                market_cap_unit = holding.get('market_cap_unit', '亿元')

                if market_cap_unit == '万元':
                    market_cap_yi = market_cap / 10000  # 万元转亿元
                elif market_cap_unit == '元':
                    market_cap_yi = market_cap / 100000000  # 元转亿元
                else:
                    market_cap_yi = market_cap  # 假设已经是亿元

                # 权重处理
                weight = holding.get('weight', 0)
                if isinstance(weight, str) and '%' in weight:
                    weight = float(weight.replace('%', '')) / 100
                elif weight > 1:  # 如果权重大于1，假设是百分比形式
                    weight = weight / 100

                if market_cap_yi > 0 and weight > 0:
                    holdings.append({
                        'stock_name': stock_name,
                        'market_cap_yi': market_cap_yi,
                        'weight': weight
                    })

        except Exception as e:
            self.logger.error(f"提取持仓数据时出错: {e}")

        return holdings

    def estimate_fund_scale_simple(self, portfolio_data: Dict) -> Optional[Dict]:
        """
        简化的基金规模估算接口

        Args:
            portfolio_data: 基金组合数据

        Returns:
            Dict: 规模估算结果，如果失败返回None
        """
        try:
            # 提取持仓数据
            holdings = self.extract_holdings_data(portfolio_data)

            if not holdings:
                self.logger.warning("未能提取到有效的持仓数据")
                return None

            # 进行规模估算
            result = self.estimator.estimate_fund_scale_limit(holdings)

            return result

        except Exception as e:
            self.logger.error(f"基金规模估算失败: {e}")
            return None

    def get_scale_summary_for_llm(self, portfolio_data: Dict) -> str:
        """
        获取基金规模估算摘要，供LLM分析使用

        Args:
            portfolio_data: 基金组合数据

        Returns:
            str: 格式化的规模估算摘要
        """
        result = self.estimate_fund_scale_simple(portfolio_data)

        if result is None:
            return "基金规模估算: 数据不足，无法进行估算"

        # 使用estimator的格式化方法
        formatted_result = self.estimator.format_result_for_llm(result)

        return formatted_result

    def add_scale_info_to_portfolio(self, portfolio_data: Dict) -> Dict:
        """
        将规模估算信息添加到组合数据中

        Args:
            portfolio_data: 原始组合数据

        Returns:
            Dict: 增强后的组合数据，包含规模估算信息
        """
        enhanced_data = portfolio_data.copy()

        # 进行规模估算
        scale_result = self.estimate_fund_scale_simple(portfolio_data)

        if scale_result:
            enhanced_data['fund_scale_estimation'] = {
                'estimated_limit_yi': scale_result['fund_scale_limit_yi'],
                'bottleneck_stock': scale_result['bottleneck_stock']['name'],
                'constraint_type': scale_result['bottleneck_stock']['constraint_type'],
                'model_version': scale_result['model_version'],
                'summary_for_llm': self.estimator.format_result_for_llm(scale_result)
            }
        else:
            enhanced_data['fund_scale_estimation'] = {
                'estimated_limit_yi': None,
                'error': '规模估算失败',
                'summary_for_llm': '基金规模估算: 数据不足，无法进行估算'
            }

        return enhanced_data

# 便捷函数
def estimate_fund_scale(portfolio_data: Dict) -> Optional[float]:
    """
    便捷函数：快速获取基金规模估算值

    Args:
        portfolio_data: 基金组合数据

    Returns:
        float: 估算的基金规模上限（亿元），失败返回None
    """
    integration = FundScaleIntegration()
    result = integration.estimate_fund_scale_simple(portfolio_data)

    if result:
        return result['fund_scale_limit_yi']
    return None

def get_scale_summary(portfolio_data: Dict) -> str:
    """
    便捷函数：快速获取规模估算摘要

    Args:
        portfolio_data: 基金组合数据

    Returns:
        str: 格式化的规模估算摘要
    """
    integration = FundScaleIntegration()
    return integration.get_scale_summary_for_llm(portfolio_data)
