"""
基金规模上限估算模块 - 动态模型实现
基于"一张纸"速查表的基金规模约束计算

计算公式：
- 监管上限: N_reg = (C * reg_limit) / w
- 流动性上限: N_liq = (C * α * trade_limit * d) / w
- 基金规模上限: N_max = min(N_reg, N_liq) 中的最小值

作者: AI Assistant
版本: V3.0
"""

import json
import logging
import os
from typing import Dict, List, Tuple, Optional
from pathlib import Path

class FundScaleEstimator:
    """
    基金规模上限估算器

    基于"一张纸公式"的动态模型，支持完全可配置的参数调整：

    可调配置参数：
    - reg_limit: 监管持股比例上限 (默认5%)
    - trade_limit: 每日可占成交额比例上限 (默认10%)
    - d: 允许的调仓天数 (默认5天)
    - α值: 按市值档位配置的流动性因子

    所有参数均从 config/market_cap_config.json 动态读取，
    支持灵活调整以适应不同市场环境和监管要求。

    实际案例：
    - 莱斯信息案例：基金规模上限7.4亿元 (流动性约束)
    - 参数：权重4.46%，中盘股α=0.012，流通市值55.4亿元
    """

    def __init__(self, config_path: str = None):
        """
        初始化基金规模估算器

        Args:
            config_path: 配置文件路径，默认使用 config/market_cap_config.json
        """
        if config_path is None:
            # 尝试多个可能的配置文件路径
            possible_paths = [
                "config/market_cap_config.json",
                "../config/market_cap_config.json",
                "../../config/market_cap_config.json"
            ]
            for path in possible_paths:
                if os.path.exists(path):
                    config_path = path
                    break
            else:
                config_path = "config/market_cap_config.json"  # 默认路径

        self.config_path = config_path
        self.logger = logging.getLogger(__name__)
        self.config = self._load_config()

    def _load_config(self) -> Dict:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            self.logger.error(f"配置文件未找到: {self.config_path}")
            raise
        except json.JSONDecodeError as e:
            self.logger.error(f"配置文件格式错误: {e}")
            raise

    def get_alpha_by_market_cap(self, market_cap_yi: float, market_type: str = "A-Share") -> float:
        """
        根据市值查找流动性因子α

        Args:
            market_cap_yi: 市值，单位亿元
            market_type: 市场类型，"A-Share" 或 "HK-Share"

        Returns:
            float: 对应的流动性因子α
        """
        try:
            # 获取市场配置
            market_config = self.config["market_cap_styles"][market_type]
            thresholds = market_config["thresholds"]
            alpha_values = market_config["alpha_values"]

            # 将亿元转换为万元（配置文件中的单位）
            market_cap_wan = market_cap_yi * 10000

            # 按从大到小的顺序检查阈值
            if market_cap_wan >= thresholds.get("Mega", float('inf')):
                return alpha_values["Mega"]
            elif market_cap_wan >= thresholds.get("Large", float('inf')):
                return alpha_values["Large"]
            elif market_cap_wan >= thresholds.get("Mid", float('inf')):
                return alpha_values["Mid"]
            elif market_cap_wan >= thresholds.get("Small", float('inf')):
                return alpha_values["Small"]
            else:
                return alpha_values["Micro"]

        except Exception as e:
            self.logger.warning(f"获取α值失败 {e}，使用默认值 0.01")
            return 0.01  # 默认1%

    def calculate_single_stock_limit(self, market_cap_yi: float, weight: float) -> Dict:
        """
        计算单只股票的规模限制

        基于"一张纸公式"模型：
        - 监管上限: N_reg = (C * reg_limit) / w
        - 流动性上限: N_liq = (C * α * trade_limit * d) / w
        - 最终限制: N_max = min(N_reg, N_liq)

        Args:
            market_cap_yi: 股票市值，单位亿元
            weight: 在基金中的权重，小数形式 (如0.05表示5%)

        Returns:
            Dict: 包含监管上限、流动性上限、最终上限等信息
        """
        if weight <= 0:
            raise ValueError("权重必须大于0")

        # 从配置文件动态获取可调参数
        # 注意：所有参数均可通过修改 config/market_cap_config.json 进行调整
        params = self.config["fund_scale_parameters"]
        reg_limit = params["reg_limit"]        # 监管持股比例上限 (可调，默认5%)
        trade_limit = params["trade_limit"]    # 每日可占成交额比例上限 (可调，默认10%)
        d = params["d"]                        # 允许的调仓天数 (可调，默认5天)

        # 查找流动性因子 (α值也是可配置的，按市值档位从配置文件读取)
        alpha = self.get_alpha_by_market_cap(market_cap_yi)

        # 计算两个上限 - 使用可调配置参数
        n_reg = (market_cap_yi * reg_limit) / weight  # 监管上限：基于5%举牌线规定
        n_liq = (market_cap_yi * alpha * trade_limit * d) / weight  # 流动性上限：基于交易活跃度

        # 取最小值作为该股票的限制
        n_max = min(n_reg, n_liq)

        # 判断瓶颈类型
        bottleneck_type = "监管约束" if n_reg <= n_liq else "流动性约束"

        return {
            "market_cap_yi": market_cap_yi,
            "weight": weight,
            "alpha": alpha,
            "n_reg": n_reg,
            "n_liq": n_liq,
            "n_max": n_max,
            "bottleneck_type": bottleneck_type,
            "parameters_used": {
                "reg_limit": reg_limit,
                "trade_limit": trade_limit,
                "d": d
            }
        }

    def estimate_fund_scale_limit(self, holdings: List[Dict]) -> Dict:
        """
        估算基金整体规模上限

        Args:
            holdings: 重仓股列表，每个元素包含 {'market_cap_yi': float, 'weight': float, 'stock_name': str}

        Returns:
            Dict: 基金规模估算结果
        """
        if not holdings:
            raise ValueError("重仓股列表不能为空")

        stock_limits = []

        # 计算每只股票的限制
        for holding in holdings:
            try:
                stock_limit = self.calculate_single_stock_limit(
                    holding['market_cap_yi'],
                    holding['weight']
                )
                stock_limit['stock_name'] = holding.get('stock_name', '未知')
                stock_limits.append(stock_limit)

            except Exception as e:
                self.logger.error(f"计算股票 {holding.get('stock_name', '未知')} 限制时出错: {e}")
                continue

        if not stock_limits:
            raise ValueError("没有成功计算出任何股票的限制")

        # 找出最严格的限制（最小的n_max）
        bottleneck_stock = min(stock_limits, key=lambda x: x['n_max'])
        fund_scale_limit = bottleneck_stock['n_max']

        # 统计约束类型
        regulatory_constraints = sum(1 for s in stock_limits if s['bottleneck_type'] == '监管约束')
        liquidity_constraints = sum(1 for s in stock_limits if s['bottleneck_type'] == '流动性约束')

        return {
            "fund_scale_limit_yi": fund_scale_limit,
            "bottleneck_stock": {
                "name": bottleneck_stock['stock_name'],
                "market_cap_yi": bottleneck_stock['market_cap_yi'],
                "weight": bottleneck_stock['weight'],
                "constraint_type": bottleneck_stock['bottleneck_type']
            },
            "constraint_summary": {
                "total_stocks": len(stock_limits),
                "regulatory_constraints": regulatory_constraints,
                "liquidity_constraints": liquidity_constraints
            },
            "all_stock_limits": stock_limits,
            "model_version": self.config.get("version", "3.0")
        }

    def format_result_for_llm(self, result: Dict) -> str:
        """
        格式化结果供LLM分析使用

        Args:
            result: estimate_fund_scale_limit的返回结果

        Returns:
            str: 格式化的文本结果
        """
        fund_limit = result["fund_scale_limit_yi"]
        bottleneck = result["bottleneck_stock"]
        summary = result["constraint_summary"]

        text = f"""基金规模上限估算结果 (V{result['model_version']}):

整体规模上限: {fund_limit:.1f}亿元

瓶颈股票: {bottleneck['name']}
- 市值: {bottleneck['market_cap_yi']:.1f}亿元
- 权重: {bottleneck['weight']:.1%}
- 约束类型: {bottleneck['constraint_type']}

约束分布:
- 总计股票: {summary['total_stocks']}只
- 监管约束: {summary['regulatory_constraints']}只
- 流动性约束: {summary['liquidity_constraints']}只

说明: 基于前十大重仓股的动态模型估算，实际规模可能受其他因素影响。"""

        return text
