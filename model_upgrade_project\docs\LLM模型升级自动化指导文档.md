# LLM模型升级自动化指导文档

## 🎯 设计逻辑与目标

### 为什么需要这份自动化指导文档？

#### 问题背景
在我们刚完成的新老模型切换项目中，发现了以下关键问题：

1. **人工成本高**：整个切换过程需要大量人工分析、测试、优化
2. **经验依赖强**：需要深度理解老模型特征和新模型问题
3. **重复工作多**：每次模型升级都要重复相似的诊断和优化流程
4. **质量风险大**：人工操作容易遗漏关键检查项，影响切换质量

#### 核心设计逻辑
基于我们成功的切换经验，将**人工智慧转化为AI可执行的标准化流程**：

```
人工经验 → 标准化流程 → AI自动执行 → 降低升级难度
```

**设计原则**：
- **经验固化**：将我们发现问题→分析问题→解决问题的成功路径固化为AI可理解的步骤
- **问题导向**：基于我们遇到的实际问题（配置错误、质量差距、估值数据缺失等）设计针对性检查
- **质量优先**：继承用户强调的"质量优先原则"，确保AI也以质量为核心进行决策
- **渐进优化**：复制我们"基础优化→针对性优化→验证切换"的成功模式

### 文档目标
让AI助手能够自动执行90%以上的模型升级工作，将人工角色从"执行者"转变为"监督者"和"决策者"。

## 📋 升级任务标准流程

### 流程设计逻辑说明

#### 为什么分为4个阶段？
基于我们的实际经验，模型切换失败的根本原因有层次性：

1. **配置层问题**（阶段1）：API密钥错误、地址错误 → 导致连接失败
2. **基础质量问题**（阶段2）：输出长度不足、结构不完整 → 导致质量下降
3. **深层质量问题**（阶段3）：估值数据缺失、概念标签不足 → 导致专业性不够
4. **整体验证问题**（阶段4）：稳定性、一致性验证 → 确保切换安全

**设计原理**：先解决底层问题，再解决高层问题，避免在错误配置上浪费时间优化质量。

#### 为什么要自动化这些步骤？
我们在手工切换中发现：
- **配置验证**：人工容易忽略细节，如API地址格式错误
- **质量对比**：人工分析耗时且主观，需要客观的数值化对比
- **问题诊断**：需要系统性检查多个维度，人工容易遗漏
- **优化策略**：基于问题类型有固定的解决模式，可以程序化

### 阶段1: 配置验证与环境准备

#### 设计逻辑：为什么先验证配置？
**经验教训**：我们在初始测试中遇到401认证错误和503服务错误，浪费了大量时间才发现是配置问题。

**自动化价值**：AI可以在几秒内完成配置验证，避免在错误配置上进行后续优化工作。

#### 任务1.1: 验证新模型配置
```python
# 执行步骤
1. 检查新模型配置参数：
   - model_name: 新模型名称
   - api_key: 新模型API密钥（必须以'sk-'开头）
   - api_base: 新模型API地址（必须以'https://'开头）

2. 验证配置正确性：
   - 测试API连通性
   - 验证认证是否成功
   - 确认模型响应正常

3. 如发现配置错误，立即报告并要求用户提供正确配置
```

#### 任务1.2: 建立老模型质量基线
```python
# 执行步骤
1. 选择3-5只测试基金（建议使用项目中已有的基金代码）
2. 使用老模型对每只基金进行分析
3. 记录关键指标：
   - 输出长度（字符数）
   - 质量评分
   - Token使用量
   - 分析耗时
   - JSON完整性
   - 结构完整性（7个部分）

4. 计算平均值作为质量基线
```

### 阶段2: 初步优化与测试

#### 设计逻辑：为什么要基础优化？
**经验总结**：我们发现新模型的主要问题是输出长度不足（9000字符 vs 老模型13000字符）和结构不完整。

**解决思路**：
1. **长度匹配**：让新模型学习老模型的输出长度标准
2. **结构对齐**：确保新模型输出包含所有必需的7个部分
3. **Few-shot Learning**：嵌入老模型优秀输出作为学习范例

**自动化价值**：AI可以自动分析老模型特征，生成针对性的基础优化提示词。

#### 任务2.1: 基础提示词优化
```python
# 自动优化策略
1. 分析老模型输出特征：
   - 平均输出长度
   - 常用的分析结构
   - 关键术语和表达方式

2. 调整新模型提示词：
   - 设置目标长度为老模型平均长度的90-110%
   - 增加结构完整性要求
   - 嵌入老模型优秀输出片段作为Few-shot范例

3. 优化模板：
   ```
   # 黄金质量标准（基于老模型{old_avg_length}字符优秀范本学习）
   **目标输出长度**: {target_min}-{target_max}字符
   **结构完整性**: 严格按照7部分结构，每部分都要有充分的分析深度
   **专业表达**: 使用精准的基金分析术语，大量引用具体数据
   ```
```

#### 任务2.2: 初步对比测试
```python
# 执行步骤
1. 使用相同的测试基金
2. 用优化后的新模型进行分析
3. 对比关键指标：
   - 质量差距是否<10%
   - 输出长度是否达到老模型80%以上
   - 结构完整性是否达到100%

4. 自动判断是否需要进入下一阶段：
   - 如果质量差距>10%，进入针对性优化
   - 如果质量差距<5%，可考虑进入验证阶段
```

### 阶段3: 针对性问题诊断与优化

#### 设计逻辑：为什么需要针对性优化？
**关键发现**：用户精准识别出新模型的三个核心问题：
1. **个股分析深度不足**：老模型每个个股都有详细分析，新模型过于简洁
2. **数据支撑不够**：缺少具体的PE、PB等估值指标
3. **概念标签数量不足**：需要提取更多概念标签

**解决逻辑**：
- **问题诊断自动化**：通过对比分析自动识别这三类问题
- **解决方案模板化**：为每类问题提供固定的优化策略
- **效果验证程序化**：优化后自动测试验证效果

**自动化价值**：将我们手工发现问题→分析原因→制定方案→验证效果的过程程序化，AI可以快速重复这个成功路径。

#### 任务3.1: 自动问题诊断
```python
# 问题识别清单
def diagnose_quality_issues(old_output, new_output):
    issues = []
    
    # 1. 输出长度问题
    if len(new_output) < len(old_output) * 0.8:
        issues.append("输出长度不足")
    
    # 2. 估值数据问题
    old_pe_count = old_output.count('PE(TTM)')
    new_pe_count = new_output.count('PE(TTM)')
    if new_pe_count < old_pe_count * 0.8:
        issues.append("估值数据引用不足")
    
    # 3. 概念标签问题
    old_json = extract_json_from_report(old_output)
    new_json = extract_json_from_report(new_output)
    if old_json and new_json:
        old_tags = len(old_json.get('Manager_Explicit_Concept_Tags_FromReport', []))
        new_tags = len(new_json.get('Manager_Explicit_Concept_Tags_FromReport', []))
        if new_tags < old_tags * 0.8:
            issues.append("概念标签数量不足")
    
    # 4. 结构完整性问题
    required_sections = ["核心洞察与关键评估摘要", "核心投资逻辑摘要", "投资组合与变动分析"]
    missing_sections = [s for s in required_sections if s not in new_output]
    if missing_sections:
        issues.append(f"缺失结构部分: {missing_sections}")
    
    return issues
```

#### 任务3.2: 自动针对性优化
```python
# 针对性优化策略
def apply_targeted_optimization(issues):
    optimizations = []
    
    if "输出长度不足" in issues:
        optimizations.append("""
        # 增加长度要求
        **目标输出长度**: 14000-16000字符（学习老模型的丰富度）
        **详细分析要求**:
        - 第0部分：3个重要发现，每个至少120字详细阐述
        - 第1部分：4个核心概念定义，每个至少60字
        - 第2部分：至少1400字的聚焦领域分析
        - 第3部分：至少1800字的组合分析
        """)
    
    if "估值数据引用不足" in issues:
        optimizations.append("""
        **强制要求**：
        1. **估值数据必须包含**：每个个股必须包含"PE(TTM) XX.XX倍，PB X.XX倍"格式
        2. **分析深度**：每个代表性个股分析至少200字
        
        **Few-shot范例**：
        *估值特征*: **PE(TTM) 12.5倍，PB 1.21倍**，在行业中估值合理...
        """)
    
    if "概念标签数量不足" in issues:
        optimizations.append("""
        **概念标签要求**：
        Manager_Explicit_Concept_Tags_FromReport字段必须包含17个以上从季报文本中提炼的概念标签
        """)
    
    return "\n".join(optimizations)
```

### 阶段4: 验证与切换决策

#### 设计逻辑：为什么需要全面验证？
**经验教训**：我们通过3只随机基金的测试发现，新模型在不同基金上表现差异很大：
- 630005.OF：新模型显著优于老模型（+58.9%）
- 003993.OF：老模型略优（-3.3%）
- 009861.OF：老模型略优（-5.2%）

**验证逻辑**：
1. **多基金测试**：确保解决方案在不同基金上都有效
2. **多维度评估**：不仅看质量，还要看效率、稳定性、成本
3. **量化决策**：基于具体数据而非主观判断做切换决策

**自动化价值**：AI可以客观地进行大量测试和数据分析，避免人工判断的主观性和遗漏。

#### 任务4.1: 全面验证测试
```python
# 验证标准
def comprehensive_validation(test_results):
    validation_criteria = {
        'quality_gap': 5,      # 质量差距<5%
        'length_ratio': 0.85,  # 长度达到老模型85%以上
        'structure_complete': 1.0,  # 结构完整性100%
        'json_valid': 1.0,     # JSON格式100%正确
        'stability': 0.9       # 90%以上测试成功率
    }
    
    results = {}
    for criterion, threshold in validation_criteria.items():
        actual_value = calculate_metric(test_results, criterion)
        results[criterion] = {
            'actual': actual_value,
            'threshold': threshold,
            'passed': actual_value >= threshold
        }
    
    overall_passed = all(r['passed'] for r in results.values())
    return results, overall_passed
```

#### 任务4.2: 自动切换建议
```python
# 切换决策逻辑
def generate_migration_recommendation(validation_results, performance_metrics):
    if validation_results['overall_passed']:
        return {
            'recommendation': 'READY_FOR_MIGRATION',
            'confidence': 'HIGH',
            'migration_strategy': 'GRADUAL',
            'benefits': [
                f"速度提升: {performance_metrics['speed_improvement']:.1f}%",
                f"成本降低: {performance_metrics['cost_reduction']:.1f}%",
                f"质量保持: {performance_metrics['quality_retention']:.1f}%"
            ]
        }
    else:
        failed_criteria = [k for k, v in validation_results.items() if not v['passed']]
        return {
            'recommendation': 'NEEDS_FURTHER_OPTIMIZATION',
            'confidence': 'MEDIUM',
            'failed_criteria': failed_criteria,
            'next_steps': generate_optimization_suggestions(failed_criteria)
        }
```

## 🔧 自动化工具函数

### 工具设计逻辑说明

#### 为什么要提供这些具体的代码模板？
**核心原因**：我们在手工切换中积累的成功经验需要转化为AI可直接执行的代码。

**设计思路**：
1. **配置管理**：基于我们遇到的配置错误问题，设计自动验证逻辑
2. **质量分析**：基于我们使用的质量评估方法，设计自动化分析函数
3. **优化建议**：基于我们成功的优化策略，设计自动化建议生成器

**实现价值**：AI不需要重新学习如何分析质量问题，直接使用我们验证过的成功方法。

### 配置管理
```python
class AutoModelConfig:
    def __init__(self, config_dict):
        self.name = config_dict['name']
        self.api_key = config_dict['api_key']
        self.api_base = config_dict['api_base']
        
    def validate(self):
        """自动验证配置"""
        errors = []
        if not self.api_key.startswith('sk-'):
            errors.append("API密钥格式错误")
        if not self.api_base.startswith('https://'):
            errors.append("API地址必须使用HTTPS")
        return errors
    
    def test_connectivity(self):
        """测试连通性"""
        try:
            # 执行简单的API调用测试
            response = test_api_call(self.api_key, self.api_base)
            return response.status_code == 200
        except Exception as e:
            return False, str(e)
```

### 质量分析
```python
def auto_quality_analysis(output_text):
    """自动质量分析"""
    return {
        'length': len(output_text),
        'structure_score': calculate_structure_completeness(output_text),
        'json_score': validate_json_format(output_text),
        'valuation_score': count_valuation_references(output_text),
        'concept_tags_score': count_concept_tags(output_text),
        'overall_score': calculate_overall_quality(output_text)
    }
```

### 优化建议生成
```python
def generate_optimization_suggestions(issues):
    """根据问题自动生成优化建议"""
    suggestions = {
        'length_insufficient': "增加目标长度要求和详细分析要求",
        'valuation_missing': "添加强制估值数据格式要求",
        'tags_insufficient': "提高概念标签数量要求",
        'structure_incomplete': "强化结构完整性检查"
    }
    return [suggestions.get(issue, f"需要针对{issue}进行优化") for issue in issues]
```

## 📊 执行检查清单

### 检查清单设计逻辑

#### 为什么需要详细的检查清单？
**经验教训**：在我们的手工切换过程中，容易因为步骤复杂而遗漏关键检查项，导致：
- 配置问题未及时发现
- 质量问题诊断不全面
- 优化效果验证不充分

**设计原理**：
1. **防遗漏**：确保AI执行时不跳过任何关键步骤
2. **可追溯**：每个检查项都有明确的验证标准
3. **可监控**：人工可以通过检查清单监督AI的执行进度

**实际价值**：将我们的成功经验转化为AI必须遵循的执行标准。

### AI助手执行时必须检查的项目

#### 阶段1检查清单
- [ ] 新模型配置验证通过
- [ ] API连通性测试成功
- [ ] 老模型质量基线建立完成
- [ ] 测试基金列表确定

#### 阶段2检查清单
- [ ] 基础提示词优化完成
- [ ] 初步测试结果获得
- [ ] 质量差距计算完成
- [ ] 是否需要进入下一阶段判断

#### 阶段3检查清单
- [ ] 问题诊断完成
- [ ] 针对性优化策略制定
- [ ] 优化后测试验证
- [ ] 问题解决效果评估

#### 阶段4检查清单
- [ ] 全面验证测试完成
- [ ] 所有验证标准检查
- [ ] 切换建议生成
- [ ] 风险评估完成

## 🎯 AI助手执行指导

### 执行指导设计逻辑

#### 为什么要给AI这些具体的执行原则？
**核心目的**：确保AI继承我们在手工切换中总结的成功原则。

**关键原则来源**：
1. **严格按照流程执行**：来自我们"渐进式优化"的成功经验
2. **数据驱动决策**：来自用户强调的"质量优先原则"
3. **问题导向优化**：来自我们"针对性解决问题"的成功策略
4. **质量优先**：直接继承用户的核心要求

**设计意图**：让AI不仅知道"做什么"，更知道"为什么这样做"和"如何判断做得对不对"。

### 执行原则
1. **严格按照流程执行**：不跳过任何阶段和检查项
2. **数据驱动决策**：所有判断基于具体的测试数据
3. **问题导向优化**：针对具体问题制定优化策略
4. **质量优先**：确保新模型质量不低于老模型95%

### 异常处理
1. **配置错误**：立即停止并要求用户提供正确配置
2. **API故障**：记录错误信息，建议检查网络和服务状态
3. **质量严重下降**：暂停升级，详细分析问题原因
4. **测试失败**：记录失败原因，提供具体的修复建议

### 报告生成
每个阶段完成后，自动生成包含以下内容的报告：
- 执行步骤和结果
- 关键指标对比
- 发现的问题和解决方案
- 下一步建议

## 🚀 使用说明

### 启动升级任务
```
请按照《LLM模型升级自动化指导文档》执行新模型升级任务：
- 老模型配置：[提供老模型配置]
- 新模型配置：[提供新模型配置]
- 测试基金：[提供测试基金列表]
```

### 监控执行进度
AI助手将自动：
1. 按阶段执行升级流程
2. 在每个关键节点报告进度
3. 遇到问题时主动寻求指导
4. 完成后提供完整的升级报告

## 🤖 AI助手自动化脚本模板

### 主执行脚本
```python
class AutoModelUpgradeExecutor:
    def __init__(self, old_model_config, new_model_config, test_funds):
        self.old_model = AutoModelConfig(old_model_config)
        self.new_model = AutoModelConfig(new_model_config)
        self.test_funds = test_funds
        self.upgrade_log = []

    def execute_full_upgrade(self):
        """执行完整的自动化升级流程"""
        try:
            # 阶段1: 配置验证
            self.log_step("开始阶段1: 配置验证与环境准备")
            self.validate_configurations()
            self.establish_baseline()

            # 阶段2: 初步优化
            self.log_step("开始阶段2: 初步优化与测试")
            self.basic_optimization()
            self.initial_testing()

            # 阶段3: 针对性优化
            self.log_step("开始阶段3: 针对性问题诊断与优化")
            issues = self.diagnose_issues()
            if issues:
                self.targeted_optimization(issues)
                self.retest_after_optimization()

            # 阶段4: 验证与决策
            self.log_step("开始阶段4: 验证与切换决策")
            validation_results = self.comprehensive_validation()
            recommendation = self.generate_recommendation(validation_results)

            return self.generate_final_report(recommendation)

        except Exception as e:
            self.log_error(f"升级过程中发生错误: {e}")
            return self.generate_error_report(e)

    def log_step(self, message):
        """记录执行步骤"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.upgrade_log.append(f"[{timestamp}] {message}")
        print(f"🔄 {message}")
```

### 自动化测试脚本
```python
def auto_comparison_test(old_model, new_model, fund_code):
    """自动化对比测试"""
    print(f"📊 开始测试基金: {fund_code}")

    # 准备数据
    fund_data = prepare_fund_data(fund_code)
    if not fund_data:
        return None

    # 测试老模型
    print("  🔄 测试老模型...")
    old_result = test_model(old_model, fund_code, fund_data)

    # 测试新模型
    print("  🔄 测试新模型...")
    new_result = test_model(new_model, fund_code, fund_data)

    # 分析结果
    if old_result['success'] and new_result['success']:
        comparison = compare_results(old_result, new_result)
        print(f"    ✅ 质量对比: {comparison['quality_change']:+.1f}%")
        print(f"    ⚡ 速度对比: {comparison['speed_change']:+.1f}%")
        return comparison
    else:
        print("    ❌ 测试失败")
        return None
```

### 智能优化建议生成器
```python
class IntelligentOptimizer:
    def __init__(self):
        self.optimization_patterns = {
            'length_gap': {
                'threshold': 0.8,
                'solution': self.optimize_length,
                'priority': 'HIGH'
            },
            'valuation_gap': {
                'threshold': 0.7,
                'solution': self.optimize_valuation_data,
                'priority': 'HIGH'
            },
            'tags_gap': {
                'threshold': 0.8,
                'solution': self.optimize_concept_tags,
                'priority': 'MEDIUM'
            }
        }

    def analyze_and_optimize(self, comparison_results):
        """智能分析并生成优化方案"""
        optimizations = []

        for pattern_name, pattern_config in self.optimization_patterns.items():
            if self.detect_pattern(comparison_results, pattern_name, pattern_config['threshold']):
                optimization = pattern_config['solution'](comparison_results)
                optimization['priority'] = pattern_config['priority']
                optimizations.append(optimization)

        # 按优先级排序
        optimizations.sort(key=lambda x: {'HIGH': 3, 'MEDIUM': 2, 'LOW': 1}[x['priority']], reverse=True)
        return optimizations

    def optimize_length(self, results):
        """优化输出长度"""
        target_length = results['old_model']['length']
        return {
            'type': 'length_optimization',
            'target': f"{int(target_length * 0.9)}-{int(target_length * 1.1)}字符",
            'prompt_addition': f"""
            **目标输出长度**: {int(target_length * 0.9)}-{int(target_length * 1.1)}字符
            **详细分析要求**: 每个部分都要充分展开，避免过度简洁
            """
        }
```

## 📋 快速启动指令

### 标准启动命令
```
🚀 执行模型升级任务

请严格按照《LLM模型升级自动化指导文档》执行以下升级任务：

**老模型配置**:
- name: "vertexai/gemini-2.5-pro-preview-05-06"
- api_key: "sk-mqWRZm6zFLW04WRL72FeEcF983634a5c8bF7Ef1b955eC3Ab"
- api_base: "http://206.189.40.22:3009/v1"

**新模型配置**:
- name: "[新模型名称]"
- api_key: "[新模型API密钥]"
- api_base: "[新模型API地址]"

**测试基金**: ["017826.OF", "000573.OF", "002350.OF"]

**执行要求**:
1. 严格按照4个阶段执行
2. 每个阶段完成后报告进度
3. 遇到问题立即停止并寻求指导
4. 最终提供完整的升级建议报告
```

### 紧急修复命令
```
🔧 执行紧急问题修复

基于发现的问题执行针对性优化：

**已知问题**: [具体问题描述]
**目标改进**: [期望达到的效果]
**测试基金**: [用于验证的基金代码]

请直接进入阶段3进行针对性优化。
```

## 🎯 质量达标判断标准（基于实战经验重新设计）

### 质量评估的核心问题

#### 当前评估方法的局限性
我们在测试中发现，简单的数值化评分存在问题：
- **630005.OF**: 老模型0.534分，新模型0.848分 → 数值显示新模型更好，但实际需要人工验证
- **003993.OF**: 老模型0.876分，新模型0.847分 → 微小差异，但实际质量差异可能更大

#### 重新设计的多层次质量评估体系

### 第一层：基础格式质量（必须100%通过）
```python
BASIC_QUALITY_CRITERIA = {
    'json_validity': 1.0,          # JSON格式100%正确
    'structure_completeness': 1.0,  # 7个部分100%完整
    'length_adequacy': 0.85,       # 长度达到老模型85%以上
    'encoding_correctness': 1.0    # 字符编码100%正确
}
```

### 第二层：专业内容质量（核心评估）
```python
PROFESSIONAL_QUALITY_CRITERIA = {
    # 基于用户识别的三大核心问题设计
    'valuation_data_completeness': {
        'description': '估值数据完整性',
        'check_method': 'count_pe_pb_references',
        'threshold': 5,  # 至少5次PE(TTM)、PB引用
        'weight': 0.3
    },
    'concept_tags_adequacy': {
        'description': '概念标签充足性',
        'check_method': 'count_concept_tags',
        'threshold': 17,  # 至少17个概念标签
        'weight': 0.2
    },
    'individual_stock_analysis_depth': {
        'description': '个股分析深度',
        'check_method': 'analyze_stock_analysis_depth',
        'threshold': 200,  # 每个个股至少200字分析
        'weight': 0.3
    },
    'investment_logic_clarity': {
        'description': '投资逻辑清晰度',
        'check_method': 'check_concept_definitions',
        'threshold': 4,  # 至少4个核心概念定义
        'weight': 0.2
    }
}
```

### 第三层：相对质量对比（与老模型对比）
```python
COMPARATIVE_QUALITY_CRITERIA = {
    'content_richness_ratio': {
        'description': '内容丰富度比率',
        'calculation': 'new_model_length / old_model_length',
        'acceptable_range': (0.85, 1.15),  # 85%-115%范围内
        'weight': 0.25
    },
    'analysis_depth_ratio': {
        'description': '分析深度比率',
        'calculation': 'compare_analysis_depth',
        'acceptable_range': (0.9, 1.1),   # 90%-110%范围内
        'weight': 0.35
    },
    'professional_terminology_ratio': {
        'description': '专业术语使用比率',
        'calculation': 'compare_professional_terms',
        'acceptable_range': (0.8, 1.2),   # 80%-120%范围内
        'weight': 0.2
    },
    'insight_quality_ratio': {
        'description': '洞察质量比率',
        'calculation': 'compare_insight_quality',
        'acceptable_range': (0.9, 1.1),   # 90%-110%范围内
        'weight': 0.2
    }
}
```

### 综合质量达标判断逻辑
```python
def comprehensive_quality_assessment(old_output, new_output):
    """综合质量评估"""

    # 第一层：基础质量检查（一票否决）
    basic_results = check_basic_quality(new_output)
    if not all(basic_results.values()):
        return {
            'status': 'FAILED',
            'reason': 'BASIC_QUALITY_INSUFFICIENT',
            'failed_criteria': [k for k, v in basic_results.items() if not v]
        }

    # 第二层：专业内容质量评估
    professional_score = 0
    professional_details = {}

    for criterion, config in PROFESSIONAL_QUALITY_CRITERIA.items():
        actual_value = globals()[config['check_method']](new_output)
        passed = actual_value >= config['threshold']
        score = min(actual_value / config['threshold'], 1.0) * config['weight']

        professional_score += score
        professional_details[criterion] = {
            'actual': actual_value,
            'threshold': config['threshold'],
            'passed': passed,
            'score': score
        }

    # 第三层：相对质量对比
    comparative_score = 0
    comparative_details = {}

    for criterion, config in COMPARATIVE_QUALITY_CRITERIA.items():
        ratio = globals()[config['calculation']](old_output, new_output)
        min_ratio, max_ratio = config['acceptable_range']

        if min_ratio <= ratio <= max_ratio:
            score = config['weight']
            passed = True
        else:
            # 超出范围的惩罚
            if ratio < min_ratio:
                score = config['weight'] * (ratio / min_ratio)
            else:
                score = config['weight'] * (max_ratio / ratio)
            passed = False

        comparative_score += score
        comparative_details[criterion] = {
            'ratio': ratio,
            'acceptable_range': config['acceptable_range'],
            'passed': passed,
            'score': score
        }

    # 综合评分
    total_score = professional_score + comparative_score
    max_possible_score = sum(c['weight'] for c in PROFESSIONAL_QUALITY_CRITERIA.values()) + \
                        sum(c['weight'] for c in COMPARATIVE_QUALITY_CRITERIA.values())

    final_score = total_score / max_possible_score

    # 达标判断
    if final_score >= 0.9:
        status = 'EXCELLENT'
    elif final_score >= 0.8:
        status = 'GOOD'
    elif final_score >= 0.7:
        status = 'ACCEPTABLE'
    else:
        status = 'NEEDS_IMPROVEMENT'

    return {
        'status': status,
        'final_score': final_score,
        'professional_details': professional_details,
        'comparative_details': comparative_details,
        'recommendation': generate_quality_recommendation(status, final_score)
    }
```

### 具体检查方法实现
```python
def count_pe_pb_references(output_text):
    """统计PE、PB估值数据引用次数"""
    pe_count = output_text.count('PE(TTM)')
    pb_count = output_text.count('PB')
    return min(pe_count, pb_count)  # 取较小值，确保两种数据都有

def count_concept_tags(output_text):
    """统计概念标签数量"""
    json_data = extract_json_from_report(output_text)
    if json_data:
        tags = json_data.get('Manager_Explicit_Concept_Tags_FromReport', [])
        return len(tags) if isinstance(tags, list) else 0
    return 0

def analyze_stock_analysis_depth(output_text):
    """分析个股分析深度"""
    # 查找个股分析部分
    stock_analyses = re.findall(r'\*\*.*?\(.*?\) - .*?\*\*(.*?)(?=\*\*|$)', output_text, re.DOTALL)
    if not stock_analyses:
        return 0

    # 计算平均长度
    avg_length = sum(len(analysis.strip()) for analysis in stock_analyses) / len(stock_analyses)
    return avg_length

def compare_analysis_depth(old_output, new_output):
    """对比分析深度"""
    old_depth = analyze_stock_analysis_depth(old_output)
    new_depth = analyze_stock_analysis_depth(new_output)
    return new_depth / old_depth if old_depth > 0 else 0
```

## 🎯 总结：为什么这样设计能解决问题？

### 核心设计逻辑回顾

#### 问题根源分析
我们在模型切换中遇到的根本问题是：
1. **经验依赖**：需要深度理解老模型特征和新模型问题
2. **重复劳动**：每次升级都要重复相似的分析和优化过程
3. **质量风险**：人工操作容易遗漏，影响切换质量
4. **效率低下**：大量时间花在重复性的技术工作上

#### 解决方案逻辑
```
成功经验 → 标准化流程 → 自动化执行 → 降低升级难度
    ↓           ↓           ↓           ↓
我们的实战    4阶段流程    AI自动执行    90%工作自动化
经验总结     +检查清单    +代码模板    +质量保证
```

#### 设计价值验证
1. **经验固化**：将我们发现的"配置→基础→针对性→验证"成功路径固化
2. **问题预防**：基于我们遇到的实际问题设计预防性检查
3. **质量保证**：继承用户的"质量优先原则"，确保AI也以质量为核心
4. **效率提升**：AI可以快速执行我们验证过的成功方法

#### 实际应用效果预期
- **人工工作量减少90%**：从手工分析转为监督AI执行
- **升级成功率提高**：标准化流程避免遗漏关键步骤
- **质量风险降低**：程序化检查比人工检查更全面
- **知识传承**：新团队成员可以直接使用成熟经验

**这份自动化指导文档的核心价值在于：将我们在模型切换中积累的成功经验和解决方案，转化为AI可以自动执行的标准化流程，从而显著降低未来模型升级的难度和风险！**
