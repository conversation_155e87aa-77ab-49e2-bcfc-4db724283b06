#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字段名转换工具模块
负责英文字段名和中文字段名之间的转换
"""

import json
import os
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

class FieldNameConverter:
    """字段名转换器"""
    
    def __init__(self, config_path: str = "config/field_name_mapping.json"):
        """
        初始化字段名转换器
        
        Args:
            config_path: 字段名映射配置文件路径
        """
        self.config_path = config_path
        self.field_mapping = {}
        self.reverse_mapping = {}
        self._load_mapping()
    
    def _load_mapping(self):
        """加载字段名映射配置"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.field_mapping = config.get('field_mapping', {})
                    self.reverse_mapping = config.get('reverse_mapping', {})
                logger.info(f"成功加载字段名映射配置，包含 {len(self.field_mapping)} 个字段映射")
            else:
                logger.warning(f"字段名映射配置文件不存在: {self.config_path}")
        except Exception as e:
            logger.error(f"加载字段名映射配置失败: {e}")
    
    def english_to_chinese(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        将英文字段名转换为中文字段名
        
        Args:
            data: 包含英文字段名的字典
            
        Returns:
            包含中文字段名的字典
        """
        if not self.field_mapping:
            logger.warning("字段名映射配置为空，返回原始数据")
            return data
        
        converted_data = {}
        for english_key, value in data.items():
            chinese_key = self.field_mapping.get(english_key, english_key)
            converted_data[chinese_key] = value
            
            # 记录转换信息
            if chinese_key != english_key:
                logger.debug(f"字段名转换: {english_key} -> {chinese_key}")
        
        logger.info(f"完成字段名转换，转换了 {len([k for k in data.keys() if k in self.field_mapping])} 个字段")
        return converted_data
    
    def chinese_to_english(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        将中文字段名转换为英文字段名
        
        Args:
            data: 包含中文字段名的字典
            
        Returns:
            包含英文字段名的字典
        """
        if not self.reverse_mapping:
            logger.warning("反向字段名映射配置为空，返回原始数据")
            return data
        
        converted_data = {}
        for chinese_key, value in data.items():
            english_key = self.reverse_mapping.get(chinese_key, chinese_key)
            converted_data[english_key] = value
            
            # 记录转换信息
            if english_key != chinese_key:
                logger.debug(f"字段名转换: {chinese_key} -> {english_key}")
        
        logger.info(f"完成字段名转换，转换了 {len([k for k in data.keys() if k in self.reverse_mapping])} 个字段")
        return converted_data
    
    def get_chinese_field_name(self, english_field_name: str) -> str:
        """
        获取英文字段名对应的中文字段名
        
        Args:
            english_field_name: 英文字段名
            
        Returns:
            中文字段名，如果没有映射则返回原字段名
        """
        return self.field_mapping.get(english_field_name, english_field_name)
    
    def get_english_field_name(self, chinese_field_name: str) -> str:
        """
        获取中文字段名对应的英文字段名
        
        Args:
            chinese_field_name: 中文字段名
            
        Returns:
            英文字段名，如果没有映射则返回原字段名
        """
        return self.reverse_mapping.get(chinese_field_name, chinese_field_name)
    
    def get_all_chinese_field_names(self) -> list:
        """获取所有中文字段名列表"""
        return list(self.field_mapping.values())
    
    def get_all_english_field_names(self) -> list:
        """获取所有英文字段名列表"""
        return list(self.field_mapping.keys())


# 全局转换器实例
_converter = None

def get_converter() -> FieldNameConverter:
    """获取全局字段名转换器实例"""
    global _converter
    if _converter is None:
        _converter = FieldNameConverter()
    return _converter

def convert_to_chinese_fields(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    便捷函数：将英文字段名转换为中文字段名
    
    Args:
        data: 包含英文字段名的字典
        
    Returns:
        包含中文字段名的字典
    """
    return get_converter().english_to_chinese(data)

def convert_to_english_fields(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    便捷函数：将中文字段名转换为英文字段名
    
    Args:
        data: 包含中文字段名的字典
        
    Returns:
        包含英文字段名的字典
    """
    return get_converter().chinese_to_english(data)
