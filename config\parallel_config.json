{"parallel_processing": {"enabled": false, "max_workers": 1, "request_interval": 3.0, "description": "LLM并行处理配置，针对503错误优化", "settings": {"conservative": {"max_workers": 1, "request_interval": 5.0, "description": "保守设置，适合API限制严格的情况，避免503错误"}, "balanced": {"max_workers": 1, "request_interval": 3.0, "description": "平衡设置，推荐的默认配置，针对503错误优化"}, "aggressive": {"max_workers": 2, "request_interval": 2.0, "description": "激进设置，需要确认API支持高并发"}}, "api_limits": {"gemini": {"recommended_max_workers": 2, "rate_limit_per_minute": 60}, "openai": {"recommended_max_workers": 3, "rate_limit_per_minute": 100}, "claude": {"recommended_max_workers": 2, "rate_limit_per_minute": 30}}}, "fallback": {"auto_disable_on_error": true, "error_threshold": 3, "description": "连续失败3次后自动切换到串行模式"}, "monitoring": {"log_performance": true, "save_timing_stats": true, "compare_with_serial": false}}