# 实施计划: 自动化基金季报分析系统 (MVP v1.4 - 集成持仓数据)

## 1. 引言 (Introduction)

本文档基于 [`RFC.md`](RFC.md) (版本1.4) 和技术规范文档 [` .roo/rules-architect/rules.md`](.roo/rules-architect/rules.md) 制定，旨在为自动化基金季报分析系统的最小可行产品 (MVP) 提供详细的实施步骤。

**核心原则**:
- MVP 优先：首先实现核心的单个基金分析流程。
- 迭代开发：MVP 成功后，逐步扩展功能并偿还技术债。
- 本计划旨在提供细致的操作指引，并以任务列表形式呈现，方便跟踪进度。

## 2. 环境准备 (Environment Setup)

- **Python 版本**:
  - `[x]` 确认并安装 Python 3.8+ (参考 [` .roo/rules-architect/rules.md`](.roo/rules-architect/rules.md:20))。
- **虚拟环境**: (参考 [`RFC.md`](RFC.md:74), [` .roo/rules-architect/rules.md`](.roo/rules-architect/rules.md:17))
  - `[x]` 创建虚拟环境: `python -m venv .venv`。
  - `[]` 激活虚拟环境 (例如：Windows: `.venv\Scripts\activate`, Linux/macOS: `source .venv/bin/activate`)。
- **依赖管理**: [`requirements.txt`](requirements.txt) (参考 [`RFC.md`](RFC.md:74), [` .roo/rules-architect/rules.md`](.roo/rules-architect/rules.md:18))
  - `[x]` 创建初始 `requirements.txt` 文件。建议包含:
    - `python-dotenv`
    - `openai` (或其他选定的 LLM API SDK)
    - `SQLAlchemy` (若作为备选方案)
    - `psycopg2-binary` (若直接使用 PostgreSQL 或 SQLAlchemy)
  - `[]` 安装初始依赖: `pip install -r requirements.txt`。
  - `[]` 后续更新依赖后执行: `pip freeze > requirements.txt`。
- **Git 初始化**: (参考 [`RFC.md`](RFC.md:81), [` .roo/rules-architect/rules.md`](.roo/rules-architect/rules.md:125))
  - `[x]` 初始化 Git 仓库: `git init`。
  - `[x]` 创建 `.gitignore` 文件，至少包含:
    ```
    .venv/
    __pycache__/
    *.pyc
    .env
    *.log
    reports/
    ```
  - `[x]` 进行首次提交。
- **Linter**: Flake8 (参考 [`RFC.md`](RFC.md:76), [` .roo/rules-architect/rules.md`](.roo/rules-architect/rules.md:85))
  - `[]` 安装 Flake8: `pip install flake8`。
  - `[]` (可选) 创建 `.flake8` 配置文件，例如:
    ```ini
    [flake8]
    max-line-length = 88
    extend-ignore = E203, W503
    ```
  - `[]` 运行 Flake8 检查代码。

## 3. 核心模块：单个基金分析流程 (MVP)

(参考 [`RFC.md`](RFC.md:38-53))

### 3.1 输入处理模块

- `[~]` **任务**: 接收单个基金的标识 (例如基金代码 `005682.OF`，参考 [`RFC.md`](RFC.md:42))。 (MVP阶段决定使用 `input()`)
- `[]` **实现**:
    - `[x]` 初期：在主脚本中使用硬编码或简单的 `input()` 进行测试。 (已决定使用 `input()`)
    - `[]` 优化：使用 `argparse` 模块实现命令行参数接收 (例如 `python main.py --fund_code 005682.OF`)。

### 3.2 数据获取模块

(参考 [`RFC.md`](RFC.md:43-45))
- **职责**: 连接数据库，执行查询，提取季报文本数据和前十大持仓数据，进行初步错误处理。
- **数据库与表**:
    - 季报文本数据: `tusharedb`，表: `fund_quarterly_data` (参考 [`RFC.md`](RFC.md:44))。
    - 前十大持仓数据: `tusharedb`，表: `tushare_fund_portfolio` (参考 [`RFC.md`](RFC.md:18) 更新)。
- **技术选型**: 优先 MCP `query` 工具 (参考 [`RFC.md`](RFC.md:71))。备选：`SQLAlchemy Core` (参考 [`RFC.md`](RFC.md:72))。
- `[x]` **实现季报文本数据获取逻辑**:
    - `[x]` **MCP `query` 工具方式**:
        - `[x]` 构造调用 MCP `query` 工具的参数，包含 SQL 查询语句。 (SQL 和参数结构已确认, 并在 src/data_fetcher.py 中实现)
        - SQL 示例 (季报文本): `SELECT fund_name, fund_manager, market_outlook, operation_analysis, end_date FROM fund_quarterly_data WHERE fund_code = :fund_code AND is_latest = true ORDER BY report_date DESC LIMIT 1;` (MVP阶段获取最新，`end_date` 用于关联持仓)。
    - `[]` **`SQLAlchemy Core` 方式 (若选择)**:
        - `[]` 实现数据库连接 (连接字符串从配置加载)。
        - `[]` 使用参数化查询执行 SQL (同上，需包含 `end_date`)。
- `[ ]` **实现前十大持仓数据获取逻辑** (获取与上述季报文本 `end_date` 对应的持仓):
    - `[ ]` **MCP `query` 工具方式**:
        - `[ ]` 构造调用 MCP `query` 工具的参数，包含 SQL 查询语句。
        - SQL 示例 (持仓数据): `SELECT symbol, mkv, stk_mkv_ratio FROM tushare_fund_portfolio WHERE ts_code = :fund_code AND end_date = :report_end_date_from_quarterly_data ORDER BY stk_mkv_ratio DESC LIMIT 10;`
    - `[]` **`SQLAlchemy Core` 方式 (若选择)**:
        - `[]` 实现数据库连接。
        - `[]` 使用参数化查询执行 SQL (同上)。
- `[x]` **关键提取字段 (季报文本)**: 确认并提取至少“基金名称” (fund_name), “基金经理” (fund_manager), “市场展望” (market_outlook), “运作分析” (operation_analysis) 和 “报告期截止日” (end_date) 或等效字段 (参考 [`RFC.md`](RFC.md:18))。
- `[ ]` **关键提取字段 (持仓数据)**: 确认并提取至少股票代码 (symbol), 持有市值 (mkv), 占股票市值比 (stk_mkv_ratio)。
- `[x]` **SQL 安全与效率**: 确保使用参数化查询 (参考 [`RFC.md`](RFC.md:73), [` .roo/rules-architect/rules.md`](.roo/rules-architect/rules.md:39-40))。 (设计上已满足)
- `[x]` **错误处理**:
    - `[x]` 实现数据库连接失败处理 (参考 [` .roo/rules-architect/rules.md`](.roo/rules-architect/rules.md:36), [` .roo/rules-architect/rules.md`](.roo/rules-architect/rules.md:51))。 (MVP策略已在 src/data_fetcher.py 中实现)
    - `[x]` 实现查询执行错误处理。 (MVP策略已在 src/data_fetcher.py 中实现)
    - `[x]` 实现未找到数据或数据不完整的处理。 (MVP策略已在 src/data_fetcher.py 中实现)
    - `[x]` 将错误记录到日志系统。 (已通过各项错误处理策略在 src/data_fetcher.py 中实现)

### 3.3 分析框架加载模块

(参考 [`RFC.md`](RFC.md:46))
- `[~]` **任务**: 加载并准备 [`前置rules.md`](前置rules.md) 的全部内容。 (MVP策略: 使用提供的示例函数，文件未找到时记录错误并返回空字符串)
- `[]` **实现**: 创建函数读取文件内容 (UTF-8 编码)。
  ```python
  # def load_analysis_rules(filepath: str = "前置rules.md") -> str:
  #     try:
  #         with open(filepath, "r", encoding="utf-8") as f:
  #             return f.read()
  #     except FileNotFoundError:
  #         logging.error(f"分析规则文件未找到: {filepath}")
  #         return "" # 或者抛出异常
  ```

### 3.4 LLM 分析执行模块

(参考 [`RFC.md`](RFC.md:47-49))
- **职责**: 构造 Prompt, 调用 LLM API, 获取分析结果，处理 API 错误。
- **技术选型**: OpenAI API 兼容接口的服务 (参考 [`RFC.md`](RFC.md:79))。
- `[x]` **Prompt 构建**:
    - `[x]` 设计包含明确角色指示、完整 [`前置rules.md`](前置rules.md) 内容、基金季报文本信息、基金前十大持仓数据和输出格式要求的 Prompt 模板。 (Prompt组成要素已确认, 并在 src/llm_analyzer.py 中实现，需根据输入数据调整)
- `[x]` **API 调用封装**:
    - `[x]` 创建函数处理 API 请求和响应 (例如使用 `openai` 库)。 (核心职责已确认, 并在 src/llm_analyzer.py 中实现)
    - `[x]` 从配置加载 API 密钥和模型名称。 (已在 main.py 和 src/llm_analyzer.py 中处理)
- `[x]` **成本控制**:
    - `[x]` 确认开发测试阶段使用的模型。 (已确定为 `gemini-2.5-pro-exp-03-25`)
    - `[x]` (可选) 实现 token 消耗估算或监控的初步逻辑。 (MVP策略已在 src/llm_analyzer.py 中初步实现)
- `[x]` **错误处理**:
    - `[x]` 实现 API 密钥错误处理。 (MVP策略已在 src/llm_analyzer.py 中实现)
    - `[x]` 实现网络问题、请求超时、速率限制等处理。 (MVP策略已在 src/llm_analyzer.py 中实现)
    - `[x]` 实现 LLM 返回非预期内容或错误的初步处理。 (MVP策略已在 src/llm_analyzer.py 中实现)
    - `[x]` 将错误记录到日志系统。 (已通过各项LLM错误处理策略在 src/llm_analyzer.py 中实现)

### 3.5 报告生成与输出模块

(参考 [`RFC.md`](RFC.md:50-52) 及V1.4更新)
- `[x]` **职责**: 将 LLM 生成的包含季报文本和持仓数据综合分析的结果保存为 Markdown 文件，提取表格数据（可能包括持仓相关表格）保存为 CSV 文件，并执行 MVP 阶段的格式校验。 (已在src/report_generator.py中实现，需根据持仓数据整合进行调整)
- `[x]` **Markdown 文件保存**:
    - `[x]` 实现 Markdown 报告保存函数，文件名包含基金代码和时间戳 (例如 `005682.OF_20250509_153000.md`)。 (已在src/report_generator.py中实现)
    - `[ ]` Markdown 报告中应包含对持仓数据的分析章节和/或表格。
    - `[x]` 报告保存到指定的输出目录 (例如 `reports/`)。 (已在src/report_generator.py中实现)
- `[x]` **CSV 文件生成与保存 (单个基金)**:
    - `[x]` 从 LLM 生成的 Markdown 报告中解析并提取第 7 部分的 JSON 数据（以及可能的其他结构化持仓数据）。 (已在src/report_generator.py中实现，需评估是否需要调整以适应持仓数据)
    - `[x]` 将提取的 JSON 数据（及可能的持仓数据）转换为适合 CSV 的格式。 (已在src/report_generator.py中实现，需评估调整)
    - `[x]` 实现将转换后的数据保存为 CSV 文件的函数 (文件名与 .md 对应，例如 `005682.OF_20250509_153000.csv`)，保存到同一输出目录。 (已在src/report_generator.py中实现)
    - `[x]` 处理 JSON 解析失败或数据不适合转换为 CSV 的情况 (记录错误/警告，跳过 CSV 生成)。 (已在src/report_generator.py中实现)
- `[x]` **格式校验 (MVP 轻量级)**: (参考 [`RFC.md`](RFC.md:60))
    - `[x]` 实现检查第7部分的 JSON 数据块是否可成功解析 (使用 `json.loads()`)。 (MVP策略已在src/report_generator.py中实现)
    - `[x]` 实现检查报告文本是否大致包含 [`前置rules.md`](前置rules.md) 所要求的1-6部分的主要章节标题。 (MVP策略已在src/report_generator.py中实现)
    - `[x]` 如果校验失败，记录警告或错误到日志。 (MVP策略已在src/report_generator.py中实现)

## 4. 配置文件管理

(参考 [`RFC.md`](RFC.md:80))
- `[x]` **技术选型**: 使用 `.env` 文件结合 `python-dotenv` 库。
- `[x]` **创建 `.env` 文件**: 包含 `LLM_API_KEY`, `LLM_MODEL_NAME`, 数据库连接参数等。 (已创建 `.env.example` 作为模板)
  ```env
  # .env.example - 复制为 .env 并填入实际值
  LLM_API_KEY="your_api_key_here"
  LLM_MODEL_NAME="gpt-3.5-turbo"
  DB_HOST="localhost"
  DB_PORT="5432"
  DB_NAME="tusharedb"
  DB_USER="your_db_user"
  DB_PASSWORD="your_db_password"
  ```
- `[~]` **实现配置加载逻辑**: 在代码中安全加载配置项。 (MVP策略: 使用 `python-dotenv` 的 `load_dotenv()` 和 `os.getenv()`)
- `[x]` **安全**: 确保 `.env` 文件已加入 `.gitignore` (参考 [` .roo/rules-architect/rules.md`](.roo/rules-architect/rules.md:34))。 (已在IMP第34行创建.gitignore时包含)

## 5. 日志与错误处理 (MVP 标准)

(参考 [`RFC.md`](RFC.md:62-64))
- `[x]` **技术**: Python `logging` 模块。
- `[]` **配置**:
    - `[~]` 设置日志级别 (例如 `INFO` 或 `DEBUG`)。 (MVP策略: 默认为 INFO, 使用示例配置)
    - `[~]` 配置日志格式 (时间戳, 日志级别, 模块名, 消息)。 (MVP策略: 使用示例配置的详细格式)
    - `[~]` 配置日志输出到控制台和/或日志文件 (`app.log`)。 (MVP策略: 使用示例配置，同时输出到控制台和app.log)
  ```python
  # import logging
  # logging.basicConfig(
  #     level=logging.INFO,
  #     format='%(asctime)s - %(levelname)s - [%(module)s.%(funcName)s:%(lineno)d] - %(message)s',
  #     handlers=[
  #         logging.StreamHandler(),
  #         logging.FileHandler("app.log", encoding='utf-8')
  #     ]
  # )
  ```
- `[~]` **实施**: 在关键操作点（数据获取、LLM调用、文件保存等）添加日志记录。 (将在各模块编码时具体实现)
- `[x]` **遵循规范**: 避免在日志中记录敏感信息 (参考 [` .roo/rules-architect/rules.md`](.roo/rules-architect/rules.md:72))。 (将始终遵守此原则)

## 6. 代码组织与规范

- `[~]` **项目结构**: 按照建议的目录结构组织代码 (例如 `src/` 目录，各模块文件)。 (已决定采用 src/ 结构)
- `[]` **编码规范**: (参考 [`RFC.md`](RFC.md:69), [` .roo/rules-architect/rules.md`](.roo/rules-architect/rules.md:6), [` .roo/rules-architect/rules.md`](.roo/rules-architect/rules.md:10-11))
    - `[~]` 遵循 PEP 8 (使用 Flake8 持续检查)。 (将遵守PEP 8, Flake8检查待定)
    - `[~]` 为所有主要函数、类和模块编写 `docstrings`。 (将遵守)
    - `[~]` 尽可能使用 `type hints`。 (将遵守)
    - MVP 阶段以功能实现为首要目标，后续迭代中逐步提升规范符合度 (参考 [`RFC.md`](RFC.md:99))。

## 7. 测试 (MVP 阶段)

(参考 [`RFC.md`](RFC.md:77), [`RFC.md`](RFC.md:98))
- `[~]` **重点**: 手动测试核心流程，使用一个或多个示例基金代码，确保能成功生成报告并通过基本校验。 (将执行)
- `[]` **(可选) 单元测试**: (参考 [` .roo/rules-architect/rules.md`](.roo/rules-architect/rules.md:78-82))
    - `[~]` 考虑为 `utils.py` 中的纯逻辑函数或 `report_generator.py` 中的校验逻辑编写简单单元测试。 (可选/待定)

## 8. 版本控制

- `[]` **Git 使用**:
    - `[~]` 将所有代码、[`前置rules.md`](前置rules.md)、配置文件模板 (`.env.example`)、`requirements.txt` 等纳入 Git 管理 (参考 [`RFC.md`](RFC.md:81))。 (部分已完成，将持续执行)
    - `[~]` 编写有意义的 commit messages (参考 [` .roo/rules-architect/rules.md`](.roo/rules-architect/rules.md:126))。 (将遵守)
    - `[~]` (可选) 采用简单的分支策略 (例如 `main` 和 `develop` 分支)。 (待定/可选，MVP可直接使用主分支)

## 9. 技术债记录

(参考 [`RFC.md`](RFC.md:91-93))
- `[]` **方式**:
    - `[~]` 在代码中使用 `# TODO: MVP_DEBT - <描述>` 或 `# FIXME: <描述>` 注释。 (将采用此方式)
    - `[~]` (可选) 创建 `TECH_DEBT.md` 文件记录已知技术债及其偿还计划。 (可选/MVP阶段不强制)

## 10. 主执行脚本 (`main.py`) 骨架

- `[x]` **创建 `main.py`**: (将作为程序入口)
    - `[x]` 实现命令行参数解析 (如果选择 `argparse`)。 (MVP阶段使用 `input()`，`argparse`后续考虑)
    - `[x]` 编排调用各模块的函数 (配置加载, 数据获取, 规则加载, LLM分析, 报告生成与校验, 日志记录)。 (将按顺序调用已定义的模块/功能)
    - `[x]` 实现顶层错误捕获。 (将使用try-except捕获意外错误并记录)
  ```python
  # # main.py
  # import logging
  # import argparse
  # # from src.config_loader import load_config # 假设的配置加载
  # # from src.data_fetcher import fetch_fund_data
  # # from src.llm_analyzer import analyze_with_llm, load_analysis_rules
  # # from src.report_generator import save_report, validate_report_format # 更改了函数名以匹配之前示例

  # # 配置日志 (应在程序早期执行)
  # # logging.basicConfig(...) 

  # def run_analysis(fund_code: str):
  #     logging.info(f"开始分析基金: {fund_code}")
  #     # config = load_config()
  #     try:
  #         # fund_raw_data = fetch_fund_data(fund_code, config) # 假设返回结构化数据或文本
  #         # if not fund_raw_data:
  #         #     logging.error(f"未能获取基金 {fund_code} 的数据。")
  #         #     return

  #         # analysis_rules = load_analysis_rules("前置rules.md")
  #         # if not analysis_rules:
  #         #     return # load_analysis_rules 中已记录错误

  #         # llm_report_content = analyze_with_llm(fund_raw_data, analysis_rules, config)
  #         # if not llm_report_content:
  #         #     logging.error(f"LLM未能生成基金 {fund_code} 的报告。")
  #         #     return

  #         # is_valid = validate_report_format(llm_report_content, analysis_rules)
  #         # if not is_valid:
  #         #     logging.warning(f"基金 {fund_code} 生成的报告格式校验未通过，但仍会保存。")
  #         # else:
  #         #     logging.info(f"基金 {fund_code} 生成的报告格式校验通过。")
            
  #         # report_path = save_report(llm_report_content, fund_code, "reports")
  #         # logging.info(f"基金 {fund_code} 分析完成，报告已保存至: {report_path}")
  #         logging.info("模拟执行完成。") # MVP 占位符

  #     except Exception as e:
  #         logging.error(f"处理基金 {fund_code} 时发生意外错误: {e}", exc_info=True)

  # if __name__ == "__main__":
  #     # parser = argparse.ArgumentParser(description="自动化基金季报分析系统 MVP")
  #     # parser.add_argument("--fund_code", type=str, required=True, help="要分析的基金代码")
  #     # args = parser.parse_args()
  #     # run_analysis(args.fund_code)
  #     run_analysis("000001.OF") # 测试用
  ```

## 11. 下一步 (MVP 完成后)

- `[ ]` **评估 MVP 效果**: 根据实际生成的报告质量，迭代优化 [`前置rules.md`](前置rules.md) 和 Prompt。
- `[ ]` **批量处理流程扩展**: (参考 [`RFC.md`](RFC.md:56-68))
    - `[ ]` **阶段一：准备与配置 (批量)**
        - `[ ]` **任务**: 确定基金列表的来源。
        - `[ ]` **实现**:
            - `[ ]` 方案1: 从命令行接收一个包含多个基金代码的参数 (例如 `--fund_codes 000001.OF,000002.OF`)。
            - `[ ]` 方案2: 从一个文本文件读取基金代码列表 (例如 `fund_list.txt`，每行一个基金代码)。
            - `[ ]` 方案3: 从数据库查询符合特定条件的基金列表。
            - `[ ]` (MVP 批量阶段选择一个简单方案，例如方案2)。
        - `[ ]` **配置**: 如果需要，在 `.env` 文件中添加相关配置 (例如基金列表文件路径)。
    - `[ ]` **阶段二：数据获取与预处理 (批量)**
        - `[ ]` **任务**: 针对基金列表中的每个基金，获取其季报文本和持仓数据。
        - `[ ]` **实现**:
            - `[ ]` 修改 `src/data_fetcher.py` 中的函数，使其能够接收基金代码列表，并循环获取数据。
            - `[ ]` 考虑错误处理：单个基金数据获取失败不应中断整个批量流程，应记录错误并继续处理下一个。
    - `[ ]` **阶段三：核心分析执行 (批量)**
        - `[ ]` **任务**: 对获取到的每个基金的数据执行 LLM 分析。
        - `[ ]` **实现**:
            - `[ ]` 修改 `main.py` 或创建一个新的批量处理入口脚本。
            - `[ ]` 实现循环调用单个基金的分析逻辑 (`run_analysis` 函数或类似功能)。
            - `[ ]` (可选，MVP后考虑) 实现并行处理以提高效率 (例如使用 `concurrent.futures`)。MVP阶段可串行处理。
    - `[ ]` **阶段四：报告整合与输出 (批量)**
        - `[ ]` **任务**: 为每个成功分析的基金生成并保存报告，并进行格式校验。
        - `[ ]` **实现**:
            - `[ ]` 确保 `src/report_generator.py` 中的报告保存和校验逻辑可以被循环调用。
            - `[ ]` 考虑批量报告的命名和存储结构 (例如，在 `reports/` 目录下按日期或批次创建子目录)。
        - `[ ]` **格式校验 (轻量级 - MVP标准)**: (参考 [`RFC.md`](RFC.md:62))
            - `[ ]` 确保对每个生成的报告都执行在 [`implementation_plan_v2.md:138-141`](implementation_plan_v2.md:138) 中定义的格式校验。
    - `[ ]` **阶段五：监控、日志与错误处理 (批量)**
        - `[ ]` **日志记录 (MVP标准)**: (参考 [`RFC.md`](RFC.md:65))
            - `[ ]` 确保批量处理过程中的关键步骤（开始、单个基金处理开始/结束、错误、完成）都有日志记录。
            - `[ ]` 日志应能区分不同基金的处理过程。
        - `[ ]` **错误处理 (MVP标准)**: (参考 [`RFC.md`](RFC.md:66))
            - `[ ]` 捕获并记录在处理单个基金时发生的错误。
            - `[ ]` 确保单个基金的错误不会导致整个批量任务中断。
        - `[ ]` **执行摘要**: (参考 [`RFC.md`](RFC.md:67))
            - `[ ]` **任务**: 在批量处理完成后，生成一个总结性报告。
            - `[ ]` **实现**:
                - `[ ]` 统计成功处理的基金数量、失败的基金数量。
                - `[ ]` 列出处理失败的基金及其错误原因。
                - `[ ]` (可选) 总体耗时。
                - `[ ]` 输出摘要到控制台或保存为文件 (例如 `batch_summary_YYYYMMDD_HHMMSS.txt`)。
- `[ ]` **逐步偿还技术债**: (参考 [`RFC.md`](RFC.md:94-102))
    - `[ ]` 增强错误处理机制。
    - `[ ]` 完善日志系统。
    - `[ ]` (若需要) 引入数据库迁移工具。
    - `[ ]` 增加单元测试覆盖。
    - `[ ]` 持续代码重构。