#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版新老模型对比测试工具
专门用于对比 vertexai/gemini-2.5-pro-preview-05-06 和 gemini-2.5-pro-preview-06-05
重点关注提示词工程优化和输出质量对比
"""

import os
import json
import logging
import difflib
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv

# 导入项目模块
from src.llm_analyzer import analyze_with_llm
from src.data_fetcher import fetch_fund_data, fetch_fund_portfolio_data
from src.external_data_provider import get_external_stock_details
from src.report_generator import extract_json_from_report

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def analyze_sw_industry_distribution(portfolio_data):
    """简化版申万行业分析"""
    if not portfolio_data:
        return "未提供持仓数据，无法进行申万行业分析。"

    industry_stats = {}
    for item in portfolio_data:
        industry = item.get('sw_l1_industry', '未知行业')
        ratio = item.get('stk_mkv_ratio', 0)
        if industry in industry_stats:
            industry_stats[industry] += ratio
        else:
            industry_stats[industry] = ratio

    result = "申万行业分布分析：\n"
    for industry, ratio in sorted(industry_stats.items(), key=lambda x: x[1], reverse=True):
        result += f"- {industry}: {ratio:.2f}%\n"

    return result

def analyze_market_cap_distribution(portfolio_data):
    """简化版市值风格分析"""
    if not portfolio_data:
        return "未提供持仓数据，无法进行市值风格分析。"

    style_stats = {}
    pe_values = []
    pb_values = []

    for item in portfolio_data:
        style = item.get('market_cap_style', '未知风格')
        ratio = item.get('stk_mkv_ratio', 0)
        if style in style_stats:
            style_stats[style] += ratio
        else:
            style_stats[style] = ratio

        # 收集估值数据
        if item.get('pe_ttm') is not None:
            pe_values.append(item['pe_ttm'])
        if item.get('pb') is not None:
            pb_values.append(item['pb'])

    result = "市值风格分布分析：\n"
    for style, ratio in sorted(style_stats.items(), key=lambda x: x[1], reverse=True):
        result += f"- {style}: {ratio:.2f}%\n"

    if pe_values:
        avg_pe = sum(pe_values) / len(pe_values)
        result += f"\n估值统计摘要：\n"
        result += f"- 平均PE(TTM): {avg_pe:.2f}\n"
        result += f"- PE数据覆盖率: {len(pe_values)}/{len(portfolio_data)} ({len(pe_values)/len(portfolio_data)*100:.1f}%)\n"

    if pb_values:
        avg_pb = sum(pb_values) / len(pb_values)
        result += f"- 平均PB: {avg_pb:.2f}\n"
        result += f"- PB数据覆盖率: {len(pb_values)}/{len(portfolio_data)} ({len(pb_values)/len(portfolio_data)*100:.1f}%)\n"

    return result

def analyze_scale_constraints(fund_code, portfolio_data):
    """简化版规模约束分析"""
    if not portfolio_data:
        return "未提供持仓数据，无法进行规模约束分析。"

    total_positions = len(portfolio_data)
    result = f"基金 {fund_code} 规模约束分析：\n"
    result += f"- 当前持仓股票数量: {total_positions}只\n"
    result += f"- 基于持仓分散度的流动性评估: {'良好' if total_positions >= 10 else '需关注'}\n"

    return result

def load_analysis_rules():
    """加载分析规则"""
    try:
        with open("docs/深度分析专精.md", "r", encoding="utf-8") as f:
            return f.read()
    except FileNotFoundError:
        logger.error("分析规则文件未找到")
        return ""

class EnhancedModelComparator:
    """增强版模型对比器"""
    
    def __init__(self):
        load_dotenv()
        
        # 老模型配置
        self.old_model_config = {
            "name": "vertexai/gemini-2.5-pro-preview-05-06",
            "api_key": "sk-mqWRZm6zFLW04WRL72FeEcF983634a5c8bF7Ef1b955eC3Ab",
            "api_base": "http://206.189.40.22:3009/v1"
        }

        # 新模型配置（基于记忆中的正确配置）
        self.new_model_config = {
            "name": "gemini-2.5-pro-exp-03-25",
            "api_key": "sk-gRq2nHouhXUgRqVzdnKbQYVDwPYIhLj94NmG2rgTjAuZA8SC",
            "api_base": "https://axhixwxzsmtd.us-west-1.clawcloudrun.com/v1"
        }
        
        self.analysis_rules = load_analysis_rules()
        self.results_dir = Path("model_comparison_results")
        self.results_dir.mkdir(exist_ok=True)
        
    def prepare_fund_data(self, fund_code):
        """准备基金数据"""
        logger.info(f"准备基金 {fund_code} 的数据...")
        
        # 获取基金基本数据
        fund_data = fetch_fund_data(fund_code)
        if not fund_data:
            logger.error(f"无法获取基金 {fund_code} 的基本数据")
            return None
            
        # 获取持仓数据
        portfolio_data = fetch_fund_portfolio_data(fund_code)
        if not portfolio_data:
            logger.warning(f"无法获取基金 {fund_code} 的持仓数据")
            portfolio_data = []
            
        # 获取外部估值数据
        try:
            external_data = {}
            for item in portfolio_data:
                symbol = item.get('symbol')
                if symbol:
                    stock_external_data = get_external_stock_details(symbol)
                    if stock_external_data:
                        external_data[symbol] = stock_external_data
            logger.info(f"成功获取 {len(external_data)} 只股票的外部估值数据")
        except Exception as e:
            logger.warning(f"获取外部估值数据失败: {e}")
            external_data = {}

        # 合并估值数据
        for item in portfolio_data:
            symbol = item.get('symbol')
            if symbol in external_data:
                item.update(external_data[symbol])
                
        # 申万行业分析
        sw_industry_info = analyze_sw_industry_distribution(portfolio_data)
        
        # 市值风格分析
        market_cap_info = analyze_market_cap_distribution(portfolio_data)
        
        # 规模约束分析
        scale_analysis = analyze_scale_constraints(fund_code, portfolio_data)
        
        return {
            'fund_data': fund_data,
            'portfolio_data': portfolio_data,
            'sw_industry_info': sw_industry_info,
            'market_cap_info': market_cap_info,
            'scale_analysis': scale_analysis
        }
        
    def test_single_model(self, model_config, fund_code, prepared_data):
        """测试单个模型"""
        logger.info(f"使用模型 {model_config['name']} 分析基金 {fund_code}")
        
        fund_data = prepared_data['fund_data']
        portfolio_data = prepared_data['portfolio_data']
        
        # 格式化持仓数据
        formatted_portfolio_items = []
        for item in portfolio_data:
            total_mv_wanyuan = item.get('total_mv', 0)
            if isinstance(total_mv_wanyuan, (int, float)) and total_mv_wanyuan > 0:
                total_mv_str = f"{total_mv_wanyuan / 10000:.2f}亿元"
            else:
                total_mv_str = "未知市值"
                
            market_cap_style_str = item.get('market_cap_style', '未知风格')
            pe_str = f"{item.get('pe', 'N/A')}" if item.get('pe') is not None else "N/A"
            pe_ttm_str = f"{item.get('pe_ttm', 'N/A')}" if item.get('pe_ttm') is not None else "N/A"
            pb_str = f"{item.get('pb', 'N/A')}" if item.get('pb') is not None else "N/A"
            
            formatted_portfolio_items.append(
                f"- {item['symbol']} ({item.get('stock_name', '未知名称')} - {item.get('sw_l1_industry', '未知行业')}): "
                f"总市值: {total_mv_str}, 市值风格: {market_cap_style_str}, "
                f"PE: {pe_str}, PE(TTM): {pe_ttm_str}, PB: {pb_str}, "
                f"占净值比例 {item.get('stk_mkv_ratio', 'N/A')}%"
            )
            
        portfolio_data_str = "\n".join(formatted_portfolio_items) if formatted_portfolio_items else "未提供持仓数据。"
        
        # 调用LLM分析
        try:
            result = analyze_with_llm(
                fund_code=fund_code,
                fund_name=fund_data.get('fund_name', ''),
                fund_manager=fund_data.get('fund_manager', ''),
                report_end_date=fund_data.get('report_end_date', ''),
                market_outlook=fund_data.get('market_outlook', ''),
                operation_analysis=fund_data.get('operation_analysis', ''),
                portfolio_data=portfolio_data_str,
                sw_industry_info=prepared_data['sw_industry_info'],
                valuation_summary=prepared_data['market_cap_info'],
                scale_analysis=prepared_data['scale_analysis'],
                analysis_rules=self.analysis_rules,
                model_name=model_config['name'],
                api_key=model_config['api_key'],
                api_base=model_config['api_base']
            )
            
            if result:
                logger.info(f"模型 {model_config['name']} 分析成功，报告长度: {len(result)} 字符")
                return result
            else:
                logger.error(f"模型 {model_config['name']} 分析失败")
                return None
                
        except Exception as e:
            logger.error(f"模型 {model_config['name']} 分析出错: {e}")
            return None
            
    def compare_models(self, fund_code):
        """对比两个模型"""
        logger.info(f"开始对比分析基金 {fund_code}")
        
        # 准备数据
        prepared_data = self.prepare_fund_data(fund_code)
        if not prepared_data:
            logger.error("数据准备失败")
            return None
            
        # 测试老模型
        old_result = self.test_single_model(self.old_model_config, fund_code, prepared_data)
        
        # 测试新模型
        new_result = self.test_single_model(self.new_model_config, fund_code, prepared_data)
        
        if not old_result or not new_result:
            logger.error("模型测试失败")
            return None
            
        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存原始报告
        old_file = self.results_dir / f"{fund_code}_old_model_{timestamp}.md"
        new_file = self.results_dir / f"{fund_code}_new_model_{timestamp}.md"
        
        with open(old_file, 'w', encoding='utf-8') as f:
            f.write(old_result)
        with open(new_file, 'w', encoding='utf-8') as f:
            f.write(new_result)
            
        logger.info(f"报告已保存: {old_file}, {new_file}")
        
        # 生成对比分析
        comparison_result = self.analyze_differences(old_result, new_result, fund_code, timestamp)
        
        return {
            'old_result': old_result,
            'new_result': new_result,
            'comparison': comparison_result,
            'fund_code': fund_code,
            'timestamp': timestamp
        }
        
    def analyze_differences(self, old_result, new_result, fund_code, timestamp):
        """分析两个结果的差异"""
        logger.info("分析新老模型输出差异...")
        
        comparison = {
            'length_comparison': {
                'old_length': len(old_result),
                'new_length': len(new_result),
                'length_diff': len(new_result) - len(old_result)
            },
            'json_comparison': {},
            'text_diff': [],
            'quality_assessment': {}
        }
        
        # JSON对比
        old_json = extract_json_from_report(old_result)
        new_json = extract_json_from_report(new_result)
        
        comparison['json_comparison'] = {
            'old_json_valid': old_json is not None,
            'new_json_valid': new_json is not None,
            'json_fields_comparison': {}
        }
        
        if old_json and new_json:
            # 对比JSON字段
            old_keys = set(old_json.keys())
            new_keys = set(new_json.keys())
            
            comparison['json_comparison']['json_fields_comparison'] = {
                'common_fields': list(old_keys & new_keys),
                'old_only_fields': list(old_keys - new_keys),
                'new_only_fields': list(new_keys - old_keys)
            }
            
        # 文本差异分析
        old_lines = old_result.split('\n')
        new_lines = new_result.split('\n')
        
        diff = list(difflib.unified_diff(
            old_lines, new_lines,
            fromfile='老模型输出',
            tofile='新模型输出',
            lineterm=''
        ))
        
        comparison['text_diff'] = diff[:100]  # 只保存前100行差异
        
        # 质量评估
        comparison['quality_assessment'] = self.assess_quality(old_result, new_result)
        
        # 保存对比结果
        comparison_file = self.results_dir / f"{fund_code}_comparison_{timestamp}.json"
        with open(comparison_file, 'w', encoding='utf-8') as f:
            json.dump(comparison, f, ensure_ascii=False, indent=2)
            
        logger.info(f"对比结果已保存: {comparison_file}")
        
        return comparison
        
    def assess_quality(self, old_result, new_result):
        """评估输出质量"""
        assessment = {}
        
        # 长度评估
        assessment['length_score'] = {
            'old': min(len(old_result) / 15000, 1.0),  # 15000字为满分
            'new': min(len(new_result) / 15000, 1.0)
        }
        
        # JSON格式评估
        old_json = extract_json_from_report(old_result)
        new_json = extract_json_from_report(new_result)
        
        assessment['json_score'] = {
            'old': 1.0 if old_json else 0.0,
            'new': 1.0 if new_json else 0.0
        }
        
        # 结构完整性评估
        required_sections = [
            "核心洞察与关键评估摘要",
            "核心投资逻辑摘要", 
            "投资组合与变动分析",
            "业绩归因与关键驱动",
            "关键洞察与风险提示",
            "其他重要信息",
            "表格数据"
        ]
        
        old_sections = sum(1 for section in required_sections if section in old_result)
        new_sections = sum(1 for section in required_sections if section in new_result)
        
        assessment['structure_score'] = {
            'old': old_sections / len(required_sections),
            'new': new_sections / len(required_sections)
        }
        
        # 标识符使用评估
        emoji_indicators = ['🔴', '🟡', '🟢']
        old_indicators = sum(old_result.count(emoji) for emoji in emoji_indicators)
        new_indicators = sum(new_result.count(emoji) for emoji in emoji_indicators)
        
        assessment['indicator_score'] = {
            'old': min(old_indicators / 10, 1.0),  # 10个标识符为满分
            'new': min(new_indicators / 10, 1.0)
        }
        
        # 综合评分
        assessment['overall_score'] = {
            'old': (assessment['length_score']['old'] + 
                   assessment['json_score']['old'] + 
                   assessment['structure_score']['old'] + 
                   assessment['indicator_score']['old']) / 4,
            'new': (assessment['length_score']['new'] + 
                   assessment['json_score']['new'] + 
                   assessment['structure_score']['new'] + 
                   assessment['indicator_score']['new']) / 4
        }
        
        return assessment

def main():
    """主函数"""
    comparator = EnhancedModelComparator()
    
    # 测试基金列表（选择一个代表性基金进行快速测试）
    test_funds = [
        "017826.OF"  # 之前的高质量范本
    ]
    
    print("🚀 开始增强版新老模型对比测试")
    print(f"老模型: {comparator.old_model_config['name']}")
    print(f"新模型: {comparator.new_model_config['name']}")
    print("-" * 60)
    
    results = []
    
    for fund_code in test_funds:
        print(f"\n📊 测试基金: {fund_code}")
        try:
            result = comparator.compare_models(fund_code)
            if result:
                results.append(result)
                
                # 显示简要对比结果
                comparison = result['comparison']
                quality = comparison['quality_assessment']
                
                print(f"✅ 对比完成")
                print(f"   老模型质量评分: {quality['overall_score']['old']:.2f}")
                print(f"   新模型质量评分: {quality['overall_score']['new']:.2f}")
                print(f"   长度对比: {comparison['length_comparison']['old_length']} vs {comparison['length_comparison']['new_length']}")
                print(f"   JSON有效性: {comparison['json_comparison']['old_json_valid']} vs {comparison['json_comparison']['new_json_valid']}")
                
            else:
                print(f"❌ 基金 {fund_code} 对比失败")
                
        except Exception as e:
            print(f"❌ 基金 {fund_code} 测试出错: {e}")
            
    # 生成总结报告
    if results:
        print(f"\n📋 生成总结报告...")
        summary_file = comparator.results_dir / f"comparison_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        summary = {
            'test_time': datetime.now().isoformat(),
            'old_model': comparator.old_model_config['name'],
            'new_model': comparator.new_model_config['name'],
            'total_tests': len(results),
            'results': [
                {
                    'fund_code': r['fund_code'],
                    'quality_scores': r['comparison']['quality_assessment']['overall_score'],
                    'length_comparison': r['comparison']['length_comparison']
                }
                for r in results
            ]
        }
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
            
        print(f"✅ 总结报告已保存: {summary_file}")
        
        # 计算平均质量评分
        avg_old_score = sum(r['comparison']['quality_assessment']['overall_score']['old'] for r in results) / len(results)
        avg_new_score = sum(r['comparison']['quality_assessment']['overall_score']['new'] for r in results) / len(results)
        
        print(f"\n🎯 总体评估:")
        print(f"   老模型平均质量评分: {avg_old_score:.3f}")
        print(f"   新模型平均质量评分: {avg_new_score:.3f}")
        print(f"   质量提升: {((avg_new_score - avg_old_score) / avg_old_score * 100):+.1f}%")
        
    print(f"\n🎉 对比测试完成！结果保存在 {comparator.results_dir} 目录中")

if __name__ == "__main__":
    main()
