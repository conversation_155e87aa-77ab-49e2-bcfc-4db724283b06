import argparse
import logging
import os
import difflib
import json
import re
from dotenv import load_dotenv

# 从现有模块导入所需函数
from main import setup_logging, load_analysis_rules
from src.data_fetcher import fetch_fund_data, fetch_fund_portfolio_data
from src.llm_analyzer import analyze_with_llm

def _extract_json_str(report_content: str) -> str | None:
    """从报告内容中提取JSON字符串"""
    match = re.search(r'```json\s*(\{.*?\})\s*```', report_content, re.DOTALL)
    if match:
        return match.group(1)
    return None

def compare_models(fund_code: str):
    """
    执行单个基金在两个不同模型下的对比分析，并生成对比报告。
    """
    logger = logging.getLogger(__name__)
    
    # 1. 加载配置
    load_dotenv()
    api_key = os.getenv("LLM_API_KEY")
    api_base = os.getenv("OPENAI_API_BASE")
    if not api_key:
        logger.critical("LLM_API_KEY 未在 .env 文件中正确配置。对比测试终止。")
        return

    # 2. 定义模型
    OLD_MODEL = "vertexai/gemini-2.5-pro-preview-05-06"
    NEW_MODEL = "gemini-2.5-pro-exp-03-25"

    # 模型配置
    old_model_config = {
        "api_key": "sk-mqWRZm6zFLW04WRL72FeEcF983634a5c8bF7Ef1b955eC3Ab",
        "api_base": "http://206.189.40.22:3009/v1"
    }

    new_model_config = {
        "api_key": "sk-gRq2nHouhXUgRqVzdnKbQYVDwPYIhLj94NmG2rgTjAuZA8SC",
        "api_base": "https://axhixwxzsmtd.us-west-1.clawcloudrun.com/v1"
    }

    logger.info(f"旧模型: {OLD_MODEL}")
    logger.info(f"新模型: {NEW_MODEL}")

    # 3. 数据获取
    logger.info("正在为对比测试准备通用输入数据...")
    analysis_rules_content = load_analysis_rules()
    if not analysis_rules_content:
        logger.error("未能加载分析规则，对比测试终止。")
        return
    fund_data_raw = fetch_fund_data(fund_code)
    if fund_data_raw is None:
        logger.error(f"未能获取基金 {fund_code} 的数据，对比测试终止。")
        return
    fund_name = fund_data_raw.get("fund_name", "未知基金名称")
    fund_manager = fund_data_raw.get("fund_manager", "未知基金经理")
    market_outlook = fund_data_raw.get("market_outlook", "")
    operation_analysis = fund_data_raw.get("operation_analysis", "")
    report_end_date = fund_data_raw.get("report_end_date")
    if not report_end_date:
        logger.error(f"未能获取报告期截止日，对比测试终止。")
        return
    portfolio_data = fetch_fund_portfolio_data(fund_code, report_end_date)
    portfolio_data_str = "未获取到持仓数据或无持仓。"
    if portfolio_data:
        formatted_portfolio_items = [
            f"- {item['symbol']} ({item.get('stock_name', '未知名称')}): 占净值比例 {item.get('stk_mkv_ratio', 'N/A')}%"
            for item in portfolio_data
        ]
        portfolio_data_str = "\n".join(formatted_portfolio_items)
    logger.info("通用输入数据准备完成。")

    # 4. 调用模型
    base_params = {
        "fund_code": fund_code, "fund_name": fund_name, "fund_manager": fund_manager,
        "report_end_date": report_end_date, "market_outlook": market_outlook,
        "operation_analysis": operation_analysis, "portfolio_data": portfolio_data_str,
        "sw_industry_info": "", "valuation_summary": "", "scale_analysis": "",
        "analysis_rules": analysis_rules_content
    }

    logger.info(f"正在使用旧模型 ({OLD_MODEL}) 进行分析...")
    old_params = {**base_params, "model_name": OLD_MODEL, **old_model_config}
    report_old = analyze_with_llm(**old_params)
    if not report_old:
        logger.error(f"使用旧模型 ({OLD_MODEL}) 分析失败。")
        return
    logger.info(f"旧模型 ({OLD_MODEL}) 分析成功。")

    logger.info(f"正在使用新模型 ({NEW_MODEL}) 进行分析...")
    new_params = {**base_params, "model_name": NEW_MODEL, **new_model_config}
    report_new = analyze_with_llm(**new_params)
    if not report_new:
        logger.error(f"使用新模型 ({NEW_MODEL}) 分析失败。")
        return
    logger.info(f"新模型 ({NEW_MODEL}) 分析成功。")

    # 5. 保存和对比报告
    os.makedirs("reports", exist_ok=True)
    
    old_report_path = f"reports/OLD_MODEL_{fund_code}.md"
    new_report_path = f"reports/NEW_MODEL_{fund_code}.md"
    with open(old_report_path, "w", encoding="utf-8") as f:
        f.write(report_old)
    with open(new_report_path, "w", encoding="utf-8") as f:
        f.write(report_new)
    logger.info(f"原始报告已保存至: {old_report_path} 和 {new_report_path}")

    diff_text = difflib.unified_diff(
        report_old.splitlines(keepends=True),
        report_new.splitlines(keepends=True),
        fromfile=old_report_path,
        tofile=new_report_path,
    )
    diff_text_str = ''.join(diff_text)
    
    json_old_str = _extract_json_str(report_old)
    json_new_str = _extract_json_str(report_new)
    diff_json_str = ""

    if json_old_str and json_new_str:
        try:
            json_old_obj = json.loads(json_old_str)
            json_new_obj = json.loads(json_new_str)
            
            formatted_old = json.dumps(json_old_obj, indent=2, sort_keys=True, ensure_ascii=False)
            formatted_new = json.dumps(json_new_obj, indent=2, sort_keys=True, ensure_ascii=False)
            
            diff_json = difflib.unified_diff(
                formatted_old.splitlines(keepends=True),
                formatted_new.splitlines(keepends=True),
                fromfile="OLD_MODEL_JSON",
                tofile="NEW_MODEL_JSON",
            )
            diff_json_str = ''.join(diff_json)
        except json.JSONDecodeError as e:
            diff_json_str = f"JSON解析失败: {e}\n旧JSON:\n{json_old_str}\n新JSON:\n{json_new_str}"
    elif not json_old_str:
        diff_json_str = "旧模型报告中未找到JSON部分。"
    elif not json_new_str:
        diff_json_str = "新模型报告中未找到JSON部分。"

    comparison_report_path = f"reports/COMPARISON_{fund_code}.md"
    comparison_content = f"""# 模型对比报告: {fund_code} ({fund_name})

## 摘要
本报告对比了以下两个模型针对同一份基金数据的分析结果：
- **旧模型**: `{OLD_MODEL}`
- **新模型**: `{NEW_MODEL}`

## JSON部分对比
```diff
{diff_json_str}
```

## 完整报告文本对比
```diff
{diff_text_str}
```
"""
    with open(comparison_report_path, "w", encoding="utf-8") as f:
        f.write(comparison_content)
    logger.info(f"对比报告已生成: {comparison_report_path}")

if __name__ == "__main__":
    setup_logging()
    logger = logging.getLogger(__name__)

    parser = argparse.ArgumentParser(description="Model Comparison Test Script.")
    parser.add_argument("fund_code", type=str, help="The fund code to be tested (e.g., 005682.OF).")

    args = parser.parse_args()

    logger.info(f"Starting model comparison for fund: {args.fund_code}")
    compare_models(args.fund_code)
    logger.info(f"Model comparison for fund {args.fund_code} finished.")
