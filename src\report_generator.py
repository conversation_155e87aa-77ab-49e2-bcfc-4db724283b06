import logging
import os
import json
import csv
from datetime import datetime
from .field_name_converter import convert_to_chinese_fields, convert_to_english_fields

logger = logging.getLogger(__name__)

def _ensure_reports_dir_exists(base_dir: str = "reports", use_date_structure: bool = True) -> str:
    """
    确保报告输出目录存在，如果不存在则创建。

    Args:
        base_dir: 基础目录路径
        use_date_structure: 是否使用年月分层目录结构 (YYYY/MM/)

    Returns:
        最终的报告目录路径
    """
    if not os.path.isabs(base_dir):
        base_reports_dir = os.path.join(os.getcwd(), base_dir)
    else:
        base_reports_dir = base_dir

    if use_date_structure:
        # 创建按年月分层的目录结构：reports/YYYY/MM/
        current_date = datetime.now()
        year_dir = current_date.strftime("%Y")
        month_dir = current_date.strftime("%m")
        reports_dir = os.path.join(base_reports_dir, year_dir, month_dir)
    else:
        reports_dir = base_reports_dir

    if not os.path.exists(reports_dir):
        try:
            os.makedirs(reports_dir, exist_ok=True)
            logger.info(f"成功创建报告目录: {reports_dir}")
        except OSError as e:
            logger.error(f"创建报告目录失败 {reports_dir}: {e}", exc_info=True)
            return ""
    return reports_dir

def save_report_md(report_content: str, fund_code: str, fund_name: str, fund_manager: str, output_base_dir: str = "reports") -> str | None:
    """
    将LLM生成的报告内容保存为Markdown文件。
    文件名包含基金代码和时间戳。
    遵循 IMP 3.5.1 Markdown 文件保存中的设计。

    Args:
        report_content (str): LLM生成的报告文本。
        fund_code (str): 基金代码。
        fund_name (str): 基金名称。
        fund_manager (str): 基金经理。
        output_base_dir (str, optional): 报告保存的基础目录。 Defaults to "reports".

    Returns:
        str | None: 成功保存的文件路径，或失败时返回 None。
    """
    reports_dir = _ensure_reports_dir_exists(output_base_dir)
    if not reports_dir:
        return None

    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        # 新格式：时间前置 - YYYYMMDD_HHMMSS_基金代码.md
        # 处理基金代码：移除.OF后缀，避免重复
        clean_fund_code = fund_code.replace('.OF', '').replace('.', '_')
        filename = f"{timestamp}_{clean_fund_code}_OF.md"
        filepath = os.path.join(reports_dir, filename)
        
        # report_date_display = actual_report_date if actual_report_date and actual_report_date != "未知报告日期" else "日期未提供" # 移除

        with open(filepath, "w", encoding="utf-8") as f:
            # 在报告顶部添加基金名称和经理信息 (移除报告日期)
            header = f"# {fund_name} ({fund_code}) - 基金分析报告\n\n**基金经理:** {fund_manager}\n\n---\n\n"
            f.write(header + report_content)
        logger.info(f"Markdown报告成功保存至: {filepath}")
        return filepath
    except IOError as e:
        logger.error(f"保存Markdown报告失败 (基金: {fund_code}, 文件: {filepath}): {e}", exc_info=True)
        return None
    except Exception as e:
        logger.error(f"保存Markdown报告时发生意外错误 (基金: {fund_code}): {e}", exc_info=True)
        return None

def _extract_json_from_report(report_content: str, fund_code: str | None = None, fund_name: str | None = None, fund_manager: str | None = None) -> dict | None:
    """
    从报告文本中提取第七部分的JSON数据块。
    简单实现：查找 "## 第七部分：表格数据" 和 ```json ... ``` 标记。
    如果提供了 fund_code, fund_name, fund_manager, 则会更新提取到的JSON对象。
    """
    try:
        # 支持多种格式的第七部分标记
        json_block_start_keywords = [
            "## 第七部分：表格数据",
            "### **第七部分：表格数据**",
            "### 第七部分：表格数据",
            "**第七部分：表格数据**"
        ]

        json_start_index = -1
        for keyword in json_block_start_keywords:
            json_start_index = report_content.find(keyword)
            if json_start_index != -1:
                logger.debug(f"找到第七部分标记: {keyword}")
                break

        if json_start_index == -1:
            logger.warning("在报告中未找到第七部分（表格数据）的开始标记。")
            return None

        code_block_start_tag = "```json"
        actual_json_start = report_content.find(code_block_start_tag, json_start_index)
        if actual_json_start == -1:
            logger.warning("在第七部分标记后未找到JSON代码块开始标记 '```json'。")
            return None
        
        actual_json_start += len(code_block_start_tag)

        code_block_end_tag = "```"
        actual_json_end = report_content.find(code_block_end_tag, actual_json_start)
        if actual_json_end == -1:
            logger.warning("未找到JSON代码块的结束标记 '```'。")
            return None
            
        json_str = report_content[actual_json_start:actual_json_end].strip()

        if not json_str:
            logger.warning("提取的JSON字符串为空。")
            return None

        # 清理JSON字符串中的控制字符和格式错误
        def clean_json_string(json_text):
            """清理JSON字符串中的控制字符和格式错误"""
            import re

            # 第一步：清理JSON结构错误 - 移除行首的 "- " 前缀
            # 这种错误通常出现在LLM生成的JSON中，如: - "key": "value"
            lines = json_text.split('\n')
            cleaned_lines = []
            for line in lines:
                # 移除行首的 "- " 前缀，但保留缩进
                cleaned_line = re.sub(r'^(\s*)- ', r'\1', line)
                cleaned_lines.append(cleaned_line)
            json_text = '\n'.join(cleaned_lines)

            # 第二步：清理字符串值内的控制字符
            # 使用正则表达式找到所有字符串值（在双引号内的内容）
            # 并将其中的换行符替换为空格
            def replace_newlines_in_string(match):
                string_content = match.group(1)
                # 将换行符和回车符替换为空格，并清理多余空格
                cleaned = re.sub(r'[\n\r]+', ' ', string_content)
                cleaned = re.sub(r'\s+', ' ', cleaned)
                return f'"{cleaned}"'

            # 匹配JSON字符串值（简化版本，不处理嵌套引号）
            pattern = r'"([^"]*(?:\\.[^"]*)*)"'
            cleaned_json = re.sub(pattern, replace_newlines_in_string, json_text)
            return cleaned_json

        # 清理JSON字符串
        cleaned_json_str = clean_json_string(json_str)

        parsed_json = json.loads(cleaned_json_str)

        # 检查是否为中文字段名，如果是则转换为英文字段名以保持兼容性
        if "基金代码" in parsed_json or "基金名称" in parsed_json or "基金经理" in parsed_json:
            logger.info("检测到中文字段名，转换为英文字段名以保持兼容性")
            parsed_json = convert_to_english_fields(parsed_json)

        # 移除调试日志和注入逻辑，因为这已在 main.py 中通过字符串替换处理
        # logger.debug(f"原始解析的JSON (部分): {str(parsed_json)[:200]}")
        # logger.debug(f"传入的 fund_code: {fund_code}, fund_name: {fund_name}, fund_manager: {fund_manager}")

        # # 如果提供了额外信息，则更新JSON对象
        # if fund_code is not None:
        #     parsed_json["FundCode"] = fund_code
        #     logger.debug(f"更新后 FundCode: {parsed_json.get('FundCode')}")
        # if fund_name is not None:
        #     parsed_json["FundName"] = fund_name
        #     logger.debug(f"更新后 FundName: {parsed_json.get('FundName')}")
        # if fund_manager is not None:
        #     parsed_json["ManagerName"] = fund_manager
        #     logger.debug(f"更新后 ManagerName: {parsed_json.get('ManagerName')}")
        # # if actual_report_date is not None and actual_report_date != "未知报告日期": # 移除 ReportDate 的注入
        #     # parsed_json["ReportDate"] = actual_report_date

        # logger.debug(f"最终将返回的JSON (部分): {str(parsed_json)[:200]}")
        return parsed_json
    except json.JSONDecodeError as e:
        logger.error(f"解析报告中的JSON数据失败: {e}", exc_info=True)
        problematic_json_str = report_content[actual_json_start:actual_json_end].strip() if 'actual_json_start' in locals() and 'actual_json_end' in locals() else "N/A"
        logger.debug(f"问题JSON字符串 (部分预览): {problematic_json_str[:200]}")
        return None
    except Exception as e:
        logger.error(f"提取JSON数据时发生意外错误: {e}", exc_info=True)
        return None

def save_report_csv(report_content: str, fund_code: str, fund_name: str, fund_manager: str, output_base_dir: str = "reports") -> str | None:
    """
    从LLM报告中提取表格数据并保存为CSV文件。
    遵循 IMP 3.5.2 CSV 文件生成与保存中的设计。

    Args:
        report_content (str): LLM生成的报告文本。
        fund_code (str): 基金代码。
        fund_name (str): 基金名称。
        fund_manager (str): 基金经理。
        output_base_dir (str, optional): 报告保存的基础目录。 Defaults to "reports".

    Returns:
        str | None: 成功保存的文件路径，或失败时返回 None。
    """
    reports_dir = _ensure_reports_dir_exists(output_base_dir)
    if not reports_dir:
        return None

    json_data = _extract_json_from_report(report_content, fund_code, fund_name, fund_manager) # 移除 actual_report_date
    if not json_data or not isinstance(json_data, dict):
        logger.warning(f"未能从报告中提取有效的JSON对象用于CSV生成 (基金: {fund_code})。")
        return None

    # 修改：不再寻找列表，直接将提取的JSON对象作为单行数据写入CSV
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        # 新格式：时间前置 - YYYYMMDD_HHMMSS_基金代码_OF_summary.csv
        # 处理基金代码：移除.OF后缀，避免重复
        clean_fund_code = fund_code.replace('.OF', '').replace('.', '_')
        filename = f"{timestamp}_{clean_fund_code}_OF_summary.csv"
        filepath = os.path.join(reports_dir, filename)

        # 清理JSON数据中的换行符，避免CSV格式问题
        def clean_value_for_csv(value):
            """清理值中的换行符和其他可能破坏CSV格式的字符"""
            if isinstance(value, str):
                # 将换行符替换为空格，并清理多余的空格
                return ' '.join(value.replace('\n', ' ').replace('\r', ' ').split())
            return value

        # 递归清理JSON数据
        def clean_json_for_csv(obj):
            if isinstance(obj, dict):
                return {k: clean_json_for_csv(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [clean_json_for_csv(item) for item in obj]
            else:
                return clean_value_for_csv(obj)

        cleaned_json_data = clean_json_for_csv(json_data)

        # 将英文字段名转换为中文字段名
        chinese_json_data = convert_to_chinese_fields(cleaned_json_data)

        # 将转换后的JSON字典作为单行数据
        data_to_write = [chinese_json_data]

        if not data_to_write: # 理论上如果 json_data 有效，这里不会是空
             logger.error(f"内部错误：有效的JSON数据未能放入写入列表 (基金: {fund_code})。")
             return None

        # 获取所有键作为表头
        headers = list(data_to_write[0].keys())
        
        with open(filepath, "w", encoding="utf-8", newline="") as f:
            writer = csv.DictWriter(f, fieldnames=headers)
            writer.writeheader()
            writer.writerows(data_to_write) # 写入单行数据
            
        logger.info(f"CSV报告 (摘要) 成功保存至: {filepath}")
        return filepath
    except IOError as e:
        logger.error(f"保存CSV报告失败 (基金: {fund_code}, 文件: {filepath}): {e}", exc_info=True)
        return None
    except (IndexError, KeyError) as e: # 处理可能的字典或列表访问错误
         logger.error(f"处理JSON数据以写入CSV时出错 (基金: {fund_code}): {e}", exc_info=True)
         return None
    except Exception as e:
        logger.error(f"保存CSV报告时发生意外错误 (基金: {fund_code}): {e}", exc_info=True)
        return None

def validate_report_format(report_content: str, fund_code: str, fund_name: str, fund_manager: str, analysis_rules_content: str | None = None) -> bool:
    """
    对LLM生成的报告进行MVP阶段的轻量级格式校验。
    遵循 IMP 3.5.3 中的设计。

    Args:
        report_content (str): LLM生成的完整报告文本。
        fund_code (str): 基金代码。
        fund_name (str): 基金名称。
        fund_manager (str): 基金经理。
        analysis_rules_content (str | None, optional): 前置rules.md的内容，用于提取预期的章节标题。
                                如果为None，则章节标题校验将被跳过或使用一组默认标题。 Defaults to None.

    Returns:
        bool: True 如果基本校验通过，否则 False。
    """
    is_valid = True
    logger.info("开始对生成的报告进行格式校验...")

    json_data = _extract_json_from_report(report_content, fund_code, fund_name, fund_manager) # 移除 actual_report_date
    if json_data is None:
        logger.warning("格式校验失败：报告的第七部分JSON数据块无法解析或未找到。")
        is_valid = False
    else:
        logger.info("格式校验：第七部分JSON数据块可成功解析。")

    expected_main_sections_keywords = [
        "一、本期基金业绩回顾与分析",
        "二、管理人对宏观经济、证券市场及行业走势的简要展望",
        "三、管理人对公平交易情况的专项说明",
        "四、管理人对报告期内基金估值程序等事项的说明",
        "五、管理人对报告期内基金利润分配情况的说明",
        "六、投资组合报告附注外的其他文字描述部分",
    ]
    
    if analysis_rules_content:
        # TODO: 更高级的实现可以从 analysis_rules_content 动态解析出章节标题
        pass

    missing_sections = []
    for section_keyword in expected_main_sections_keywords:
        if section_keyword not in report_content:
            missing_sections.append(section_keyword)
            
    if missing_sections:
        logger.warning(f"格式校验警告：报告文本中可能缺失以下主要章节标题: {', '.join(missing_sections)}")
        # is_valid = False # MVP阶段仅警告
    else:
        logger.info("格式校验：报告文本大致包含了预期的主要章节标题。")

    if is_valid:
        logger.info("报告格式基本校验通过。")
    else:
        logger.warning("报告格式基本校验未通过。详情请查看上述日志。")
        
    return is_valid

if __name__ == '__main__':
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(levelname)s - [%(name)s.%(funcName)s:%(lineno)d] - %(message)s',
        handlers=[logging.StreamHandler()]
    )

    sample_report_md_content = """# 基金分析报告...

这是内容。

## 一、本期基金业绩回顾与分析
回顾内容...

## 二、管理人对宏观经济、证券市场及行业走势的简要展望
展望内容...

## 三、管理人对公平交易情况的专项说明
公平交易说明...

## 四、管理人对报告期内基金估值程序等事项的说明
估值说明...

## 五、管理人对报告期内基金利润分配情况的说明
利润分配说明...

## 六、投资组合报告附注外的其他文字描述部分
其他描述...

## 第七部分：表格数据
```json
{
  "FundCode": "001234.OFTEST",
  "FundName": "测试基金名称",
  "ManagerName": "测试基金经理",
  "top_holdings_analysis": {
    "summary": "持仓集中度较高，主要投资于科技和消费行业。",
    "details": [
        {"stock_name": "股票A", "percentage": "5.00%", "value_mn": 100.0, "analysis": "核心持仓，表现稳健"},
        {"stock_name": "股票B", "percentage": "4.50%", "value_mn": 90.0, "analysis": "新增持仓，看好其成长性"}
    ]
  },
  "sector_allocation": [
    {"sector_name": "科技", "percentage": "30.00%"},
    {"sector_name": "消费", "percentage": "25.00%"}
  ]
}
```
"""
    sample_fund_code_main = "001234.OFTEST"
    sample_fund_name_main = "测试基金名称"
    sample_fund_manager_main = "测试基金经理"
    # sample_actual_report_date_main = "2024-03-31" # 移除

    logger.info("--- 测试 Markdown 保存 ---")
    # 移除 actual_report_date
    md_path_main = save_report_md(sample_report_md_content, sample_fund_code_main, sample_fund_name_main, sample_fund_manager_main)
    if md_path_main:
        logger.info(f"测试 Markdown 保存成功: {md_path_main}")
    else:
        logger.error("测试 Markdown 保存失败。")

    logger.info("\n--- 测试 CSV 保存 ---")
    # 移除 actual_report_date
    csv_path_main = save_report_csv(sample_report_md_content, sample_fund_code_main, sample_fund_name_main, sample_fund_manager_main)
    if csv_path_main:
        logger.info(f"测试 CSV 保存成功: {csv_path_main}")
    else:
        logger.error("测试 CSV 保存失败。")
    
    logger.info("\n--- 测试格式校验 (有效报告) ---")
    # 移除 actual_report_date
    is_valid_report = validate_report_format(sample_report_md_content, sample_fund_code_main, sample_fund_name_main, sample_fund_manager_main)
    logger.info(f"有效报告格式校验结果: {is_valid_report}")

    logger.info("\n--- 测试格式校验 (无效JSON) ---")
    invalid_json_report = "# 报告...\n## 第七部分：表格数据\n```json\n{\n  \"key\": \"value\",,,\n}\n```"
    # 移除 actual_report_date
    is_invalid_json_valid = validate_report_format(invalid_json_report, "INVALIDJSON", "无效JSON基金", "无效经理")
    logger.info(f"无效JSON报告格式校验结果: {is_invalid_json_valid}")
    
    logger.info("\n--- 测试格式校验 (缺少章节) ---")
    missing_section_report = """# 基金分析报告...
## 一、本期基金业绩回顾与分析
回顾内容...
## 第七部分：表格数据
```json
{"data": "valid", "FundCode": "MISSINGSEC", "FundName": "测试基金", "ManagerName": "测试经理"}
```
"""
    # 移除 actual_report_date
    is_missing_section_valid = validate_report_format(missing_section_report, "MISSINGSEC", "测试基金", "测试经理")
    logger.info(f"缺少章节报告格式校验结果: {is_missing_section_valid}")

    logger.info("\n--- 测试提取无效JSON (来自save_report_csv的场景) ---")
    # 移除 actual_report_date
    save_report_csv(invalid_json_report, "INVALIDJSON.TESTCSV", "无效JSON基金", "无效经理")
    
    logger.info("\n--- 测试无表格JSON (来自save_report_csv的场景) ---")
    no_table_json_report = "# 报告...\n## 第七部分：表格数据\n```json\n{\n  \"description\": \"这是一个描述而非表格列表\", \"FundCode\": \"NOTABLE\", \"FundName\": \"测试基金\", \"ManagerName\": \"测试经理\" \n}\n```"
    # 移除 actual_report_date
    save_report_csv(no_table_json_report, "NOTABLE.TESTCSV", "测试基金", "测试经理")

    logger.info("\n--- 测试无第七部分 (来自save_report_csv的场景) ---")
    no_section7_report = "# 报告...\n## 第六部分：其他\n内容"
    # 移除 actual_report_date
    save_report_csv(no_section7_report, "NOSEC7.TESTCSV", "无第七部分基金", "无经理")