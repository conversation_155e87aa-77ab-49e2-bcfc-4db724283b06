#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强版提示词V2效果
专门针对估值数据和概念标签提取进行优化测试
"""

import os
import json
import time
from datetime import datetime
from dotenv import load_dotenv

# 导入项目模块
from src.llm_analyzer import analyze_with_llm
from src.data_fetcher import fetch_fund_data, fetch_fund_portfolio_data
from src.external_data_provider import get_external_stock_details
from src.report_generator import _extract_json_from_report as extract_json_from_report

def load_analysis_rules():
    """加载分析规则"""
    try:
        with open("docs/深度分析专精.md", "r", encoding="utf-8") as f:
            return f.read()
    except FileNotFoundError:
        print("❌ 分析规则文件未找到")
        return ""

def test_enhanced_prompt_v2():
    """测试增强版提示词V2"""
    load_dotenv()
    
    # 新模型配置
    new_model_config = {
        "name": "gemini-2.5-pro-preview-06-05",
        "api_key": "sk-gRq2nHouhXUgRqVzdnKbQYVDwPYIhLj94NmG2rgTjAuZA8SC",
        "api_base": "https://axhixwxzsmtd.us-west-1.clawcloudrun.com/v1"
    }
    
    # 测试基金 - 使用之前质量差异较大的基金
    fund_code = "003993.OF"
    
    print("🧪 测试增强版提示词V2效果")
    print(f"模型: {new_model_config['name']}")
    print(f"测试基金: {fund_code}")
    print("重点验证: 估值数据、概念标签、个股分析深度")
    print("=" * 70)
    
    # 获取基金数据
    print("📥 获取基金数据...")
    fund_data = fetch_fund_data(fund_code)
    if not fund_data:
        print("❌ 无法获取基金数据")
        return
        
    portfolio_data = fetch_fund_portfolio_data(fund_code, fund_data.get('report_end_date', '2025-03-31'))
    if not portfolio_data:
        print("⚠️ 无法获取持仓数据，使用空数据")
        portfolio_data = []
        
    # 获取外部估值数据
    print("📊 获取外部估值数据...")
    try:
        external_data = {}
        for item in portfolio_data[:5]:  # 取前5个持仓
            symbol = item.get('symbol')
            if symbol:
                stock_external_data = get_external_stock_details(symbol)
                if stock_external_data:
                    external_data[symbol] = stock_external_data
        print(f"✅ 成功获取 {len(external_data)} 只股票的外部估值数据")
    except Exception as e:
        print(f"⚠️ 获取外部估值数据失败: {e}")
        external_data = {}
        
    # 合并估值数据
    for item in portfolio_data:
        symbol = item.get('symbol')
        if symbol in external_data:
            item.update(external_data[symbol])
    
    # 格式化持仓数据（包含估值信息）
    formatted_portfolio_items = []
    for item in portfolio_data:
        total_mv_wanyuan = item.get('total_mv', 0)
        if isinstance(total_mv_wanyuan, (int, float)) and total_mv_wanyuan > 0:
            total_mv_str = f"{total_mv_wanyuan / 10000:.2f}亿元"
        else:
            total_mv_str = "未知市值"
            
        market_cap_style_str = item.get('market_cap_style', '未知风格')
        pe_str = f"{item.get('pe', 'N/A')}" if item.get('pe') is not None else "N/A"
        pe_ttm_str = f"{item.get('pe_ttm', 'N/A')}" if item.get('pe_ttm') is not None else "N/A"
        pb_str = f"{item.get('pb', 'N/A')}" if item.get('pb') is not None else "N/A"
        
        formatted_portfolio_items.append(
            f"- {item['symbol']} ({item.get('stock_name', '未知名称')} - {item.get('sw_l1_industry', '未知行业')}): "
            f"总市值: {total_mv_str}, 市值风格: {market_cap_style_str}, "
            f"PE: {pe_str}, PE(TTM): {pe_ttm_str}, PB: {pb_str}, "
            f"占净值比例 {item.get('stk_mkv_ratio', 'N/A')}%"
        )
    
    portfolio_data_str = "\n".join(formatted_portfolio_items) if formatted_portfolio_items else "未提供持仓数据。"
    
    print(f"📋 持仓数据预览:")
    print(portfolio_data_str[:500] + "..." if len(portfolio_data_str) > 500 else portfolio_data_str)
    
    analysis_rules = load_analysis_rules()
    
    print("\n🤖 调用增强版V2新模型分析...")
    start_time = time.time()
    
    try:
        result = analyze_with_llm(
            fund_code=fund_code,
            fund_name=fund_data.get('fund_name', ''),
            fund_manager=fund_data.get('fund_manager', ''),
            report_end_date=fund_data.get('report_end_date', ''),
            market_outlook=fund_data.get('market_outlook', ''),
            operation_analysis=fund_data.get('operation_analysis', ''),
            portfolio_data=portfolio_data_str,
            sw_industry_info="增强版V2测试用申万行业信息",
            valuation_summary="增强版V2测试用估值信息",
            scale_analysis="增强版V2测试用规模分析",
            analysis_rules=analysis_rules,
            model_name=new_model_config['name'],
            api_key=new_model_config['api_key'],
            api_base=new_model_config['api_base']
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        if result:
            print(f"✅ 分析成功！耗时: {duration:.1f}秒")
            
            # 详细质量评估
            print(f"\n📊 增强版V2质量评估:")
            print(f"   报告长度: {len(result):,} 字符")
            print(f"   报告行数: {len(result.split(chr(10))):,} 行")
            
            # 检查估值数据
            pe_ttm_count = result.count('PE(TTM)')
            pb_count = result.count('PB')
            valuation_pattern_count = result.count('倍')
            print(f"\n💰 估值数据检查:")
            print(f"   PE(TTM)提及次数: {pe_ttm_count}")
            print(f"   PB提及次数: {pb_count}")
            print(f"   估值倍数提及: {valuation_pattern_count}")
            
            if pe_ttm_count >= 5 and pb_count >= 5:
                print(f"   ✅ 估值数据充分")
            else:
                print(f"   ⚠️ 估值数据可能不足")
            
            # 检查JSON
            json_data = extract_json_from_report(result)
            if json_data:
                print(f"\n📋 JSON格式检查:")
                print(f"   ✅ JSON格式正确，包含 {len(json_data)} 个字段")
                
                # 检查概念标签
                concept_tags = json_data.get('Manager_Explicit_Concept_Tags_FromReport', [])
                if isinstance(concept_tags, list):
                    print(f"   概念标签数量: {len(concept_tags)}")
                    if len(concept_tags) >= 17:
                        print(f"   ✅ 概念标签数量达标 (≥17)")
                    else:
                        print(f"   ⚠️ 概念标签数量不足 (<17)")
                    print(f"   标签示例: {concept_tags[:5]}")
                else:
                    print(f"   ❌ 概念标签格式错误")
                    
                if len(json_data) >= 30:
                    print(f"   🎯 JSON字段数量达标 (≥30)")
                else:
                    print(f"   ⚠️ JSON字段数量不足 (<30)")
            else:
                print("   ❌ JSON格式错误或缺失")
                
            # 检查结构
            required_sections = [
                "核心洞察与关键评估摘要",
                "核心投资逻辑摘要", 
                "投资组合与变动分析",
                "业绩归因与关键驱动",
                "关键洞察与风险提示",
                "其他重要信息",
                "表格数据"
            ]
            
            found_sections = sum(1 for section in required_sections if section in result)
            print(f"\n📋 结构完整性: {found_sections}/{len(required_sections)} 个必需部分")
            
            # 检查个股分析深度
            individual_stock_analysis = result.count('*核心定位*:')
            print(f"\n🔍 个股分析深度:")
            print(f"   个股分析数量: {individual_stock_analysis}")
            print(f"   平均每个个股字数: {len(result) // max(individual_stock_analysis, 1):,}")
            
            # 检查标识符
            emoji_count = result.count('🔴') + result.count('🟡') + result.count('🟢')
            print(f"   重点标识: {emoji_count} 个 🔴🟡🟢 标识符")
            
            # 保存结果
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"quick_test_results/enhanced_prompt_v2_test_{fund_code}_{timestamp}.md"
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(result)
                
            print(f"\n💾 结果已保存: {output_file}")
            
            # 与目标对比
            print(f"\n🎯 与老模型目标对比:")
            target_length = 14141  # 003993.OF老模型的长度
            length_ratio = len(result) / target_length
            print(f"   长度达成率: {length_ratio:.1%} (目标: 14000-16000字符)")
            
            if length_ratio >= 0.9:
                print(f"   ✅ 长度达标")
            elif length_ratio >= 0.7:
                print(f"   🟡 长度接近目标")
            else:
                print(f"   🔴 长度仍需提升")
                
            # 显示报告开头
            print(f"\n📄 报告预览 (前800字符):")
            print("-" * 70)
            print(result[:800] + "..." if len(result) > 800 else result)
            print("-" * 70)
            
            # 生成改进分析
            generate_improvement_analysis(result, target_length, duration, pe_ttm_count, pb_count, len(concept_tags) if isinstance(concept_tags, list) else 0)
            
        else:
            print("❌ 分析失败")
            
    except Exception as e:
        print(f"❌ 分析出错: {e}")

def generate_improvement_analysis(result, target_length, duration, pe_count, pb_count, concept_count):
    """生成改进分析"""
    print(f"\n📈 增强版V2效果分析:")
    
    # 长度分析
    actual_length = len(result)
    length_improvement = (actual_length - 10915) / 10915 * 100  # 与之前新模型对比
    print(f"   📏 长度提升: {length_improvement:+.1f}% (相比之前的10915字符)")
    
    # 估值数据分析
    print(f"   💰 估值数据改进:")
    print(f"      PE(TTM)提及: {pe_count} 次")
    print(f"      PB提及: {pb_count} 次")
    if pe_count >= 5 and pb_count >= 5:
        print(f"      ✅ 估值数据显著改善")
    else:
        print(f"      🔴 估值数据仍需加强")
    
    # 概念标签分析
    print(f"   🏷️ 概念标签改进:")
    print(f"      标签数量: {concept_count}")
    if concept_count >= 17:
        print(f"      ✅ 概念标签达到老模型水平")
    else:
        print(f"      🔴 概念标签仍需提升")
    
    # 效率分析
    chars_per_second = actual_length / duration
    print(f"   ⚡ 生成效率: {chars_per_second:.0f} 字符/秒")
    
    # 综合评估
    print(f"\n🏆 V2版本综合评估:")
    
    improvements = 0
    total_checks = 4
    
    if actual_length >= 12000:
        print(f"   ✅ 长度改善: 已接近老模型水平")
        improvements += 1
    else:
        print(f"   🔴 长度改善: 仍需进一步提升")
        
    if pe_count >= 5 and pb_count >= 5:
        print(f"   ✅ 估值数据: 显著改善")
        improvements += 1
    else:
        print(f"   🔴 估值数据: 需要继续优化")
        
    if concept_count >= 17:
        print(f"   ✅ 概念标签: 达到目标")
        improvements += 1
    else:
        print(f"   🔴 概念标签: 需要继续优化")
        
    if duration < 150:
        print(f"   ✅ 生成效率: 保持优势")
        improvements += 1
    else:
        print(f"   🟡 生成效率: 可接受")
        
    success_rate = improvements / total_checks
    print(f"\n📊 改进成功率: {success_rate:.1%} ({improvements}/{total_checks})")
    
    if success_rate >= 0.75:
        print(f"🎉 V2版本优化效果显著！")
    elif success_rate >= 0.5:
        print(f"🟡 V2版本有所改善，需继续优化")
    else:
        print(f"🔴 V2版本需要重大改进")

def main():
    """主函数"""
    test_enhanced_prompt_v2()

if __name__ == "__main__":
    main()
