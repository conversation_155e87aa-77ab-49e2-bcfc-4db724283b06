# System Patterns *Optional*

This file documents recurring patterns and standards used in the project.
It is optional, but recommended to be updated as the project evolves.
2025-05-09 14:12:06 - Log of updates made.

*

## Coding Patterns

*   

## Architectural Patterns

*   

## Testing Patterns

*
## 数据源集成前的抽样验证

- **模式**: 在正式将新的数据源或对现有数据源的重大读取逻辑变更集成到核心代码前，建议执行一次小规模的、直接的数据库查询（或API调用），以抽样验证数据的实际结构、格式、内容和质量。
- **场景**: 当引入新的数据表（如本次引入 `hk_stock_sw_quarterly`），或更改对现有表的字段解释、JOIN 条件时。
- **益处**:
  - 提前发现与预期的偏差，减少后期调试成本。
  - 增强对数据源的理解，确保代码逻辑的正确性。
  - 降低因数据问题导致核心功能出错的风险。
- **示例 (2025-05-25)**: 在修改 `src/data_fetcher.py` 以使用 `hk_stock_sw_quarterly` 表获取港股市值和行业前，先通过 Code 模式直接查询了该表的样本数据，确认了 `trade_date`, `l1_name`, `total_mv`, `mv_unit` 等字段的内容符合预期。