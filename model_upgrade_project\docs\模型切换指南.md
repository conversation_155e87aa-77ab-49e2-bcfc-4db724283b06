# Context
Filename: 模型切换指南.md
Created On: 2025-06-17 12:08:00
Created By: AI
Associated Protocol: RIPER-5 + Multidimensional + Agent Protocol

# Task Description
用户希望从老模型 `vertexai/gemini-2.5-pro-preview-05-06` 切换到新模型 `gemini-2.5-pro-preview-06-05`。需要分析并解决切换模型可能带来的逻辑问题和对生成文件质量的影响。

# Project Overview
本项目是一个基于 LLM 的自动化基金季报分析系统，它通过整合数据库、外部API等多源数据，构建复杂 Prompt，调用 LLM 生成结构化的分析报告。系统的核心价值在于高质量的 Prompt 工程和对 LLM 结构化输出的依赖。

---
*The following sections are maintained by the AI during protocol execution*
---

# Analysis (Populated by RESEARCH mode)
- **核心风险**: 模型切换的核心风险在于新旧模型在处理高度复杂和定制化的金融分析Prompt时，在逻辑推理能力、专业术语理解、指令遵循精确度以及结构化输出（JSON）稳定性上可能存在的差异。
- **系统复杂性**: 系统 Prompt 构建过程精细，对LLM的上下文理解能力要求极高。
- **既有模式**: 项目已建立“先抽样验证，再正式集成”的开发模式。
- **关键代码**: 模型调用主要通过 `main.py` 编排，由 `src/llm_analyzer.py` 中的 `analyze_with_llm` 函数执行，模型名称由环境变量 `LLM_MODEL_NAME` 控制。

# Proposed Solution (Populated by INNOVATE mode)
- **核心思路**: 创建一个独立的、可控的“模型对比测试框架”，而非直接切换。
- **方案**:
  1. **创建对比测试脚本**: 新建 `model_comparison_test.py`。
  2. **并行调用模型**: 在脚本中针对同一基金代码，分别调用新、旧两个模型。
  3. **生成独立报告**: 将两次调用结果分别保存为独立的 `OLD_MODEL_xxx.md` 和 `NEW_MODEL_xxx.md` 文件。
  4. **自动化差异分析**: 使用 `difflib` 对比文本差异，并对报告中的 JSON 结构和内容进行对比。
  5. **生成对比摘要**: 将所有分析结果汇总到一份清晰的对比总结报告中。

# Implementation Plan (Generated by PLAN mode)
1.  **创建模型配置文件**: 创建新文件 `config/models.json`，用于定义可用模型及其属性（名称、别名、优先级、状态）。
2.  **实现配置管理器**: 在 `src/config_manager.py` 中增加 `load_models_config` 和 `get_model_by_alias` 函数，用于读取和查询模型配置。
3.  **升级对比测试脚本**: 修改 `model_comparison_test.py`：
    *   移除硬编码的模型名称。
    *   修改命令行参数，接受 `--model1` 和 `--model2` 别名作为输入。
    *   在执行前，调用配置管理器函数来获取实际的模型名称。
4.  **（可选）升级主程序**: 修改 `main.py` 以实现基于优先级的模型自动回退逻辑。
