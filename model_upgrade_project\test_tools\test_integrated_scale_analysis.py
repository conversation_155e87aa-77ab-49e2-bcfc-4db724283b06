"""
测试集成后的基金规模估算功能
验证从数据获取到LLM分析的完整流程
"""

import sys
import os
sys.path.append('.')

from modules.fund_scale_integration_v2 import integrate_scale_analysis_to_main_flow, FundScaleIntegrationV2
from src.data_fetcher import fetch_fund_data, fetch_fund_portfolio_data
import json


def test_integrated_scale_analysis():
    """测试集成的规模分析功能"""
    
    # 测试基金列表
    test_funds = [
        {
            "fund_code": "510300.SH",
            "fund_name": "沪深300ETF",
            "description": "被动ETF基金"
        },
        {
            "fund_code": "017488.OF", 
            "fund_name": "嘉实信息产业A",
            "description": "主动管理基金"
        }
    ]
    
    print("=" * 80)
    print("测试集成的基金规模估算功能")
    print("=" * 80)
    
    for fund_info in test_funds:
        fund_code = fund_info["fund_code"]
        fund_name = fund_info["fund_name"]
        description = fund_info["description"]
        
        print(f"\n{'='*60}")
        print(f"测试基金: {fund_name} ({fund_code}) - {description}")
        print(f"{'='*60}")
        
        try:
            # 1. 获取基金基本信息
            print(f"\n【1. 获取基金基本信息】")
            if fund_code.endswith('.SH'):
                # 对于ETF，直接构造基金数据
                fund_data = {
                    'fund_code': fund_code,
                    'fund_name': fund_name,
                    'fund_manager': 'ETF管理团队',
                    'report_end_date': '2025-03-31'
                }
                print(f"基金代码: {fund_code}")
                print(f"基金名称: {fund_name}")
                print(f"基金类型: ETF")
            else:
                fund_data = fetch_fund_data(fund_code)
                if fund_data:
                    print(f"基金代码: {fund_data.get('fund_code', 'N/A')}")
                    print(f"基金名称: {fund_data.get('fund_name', 'N/A')}")
                    print(f"基金经理: {fund_data.get('fund_manager', 'N/A')}")
                    print(f"报告期: {fund_data.get('report_end_date', 'N/A')}")
                else:
                    print("未能获取基金基本信息")
                    continue
            
            # 2. 获取持仓数据
            print(f"\n【2. 获取持仓数据】")
            if fund_code.endswith('.SH'):
                # 对于ETF，直接查询持仓表
                portfolio_data = get_etf_portfolio_data(fund_code)
            else:
                portfolio_data = fetch_fund_portfolio_data(fund_code, fund_data.get('report_end_date'))
            
            if not portfolio_data:
                print("未能获取持仓数据")
                continue
                
            print(f"获取到 {len(portfolio_data)} 只持仓股票")
            
            # 显示前5只持仓
            print(f"\n前5大持仓:")
            for i, stock in enumerate(portfolio_data[:5], 1):
                print(f"  {i}. {stock.get('stock_name', 'N/A')} ({stock.get('symbol', 'N/A')}) "
                      f"权重: {stock.get('stk_mkv_ratio', 0):.2f}%")
            
            # 3. 进行规模约束分析
            print(f"\n【3. 规模约束分析】")
            scale_analysis_result = integrate_scale_analysis_to_main_flow(fund_data, portfolio_data)
            
            if scale_analysis_result.get("success", False):
                fund_scale_limit = scale_analysis_result["fund_scale_limit"]
                bottleneck_stocks = scale_analysis_result["bottleneck_stocks"]
                analysis = scale_analysis_result["analysis"]
                
                print(f"✅ 分析成功")
                print(f"理论规模上限: {fund_scale_limit:.1f}亿元")
                print(f"瓶颈股票数量: {len(bottleneck_stocks)}只")
                
                # 显示主要瓶颈股票
                print(f"\n主要瓶颈股票:")
                for stock in bottleneck_stocks[:3]:
                    print(f"  - {stock['stock_name']} ({stock['symbol']}): {stock['N_max']:.1f}亿元 "
                          f"({stock['bottleneck']}约束)")
                
                # 4. 格式化为LLM提示
                print(f"\n【4. LLM提示格式化】")
                integration = FundScaleIntegrationV2()
                llm_prompt = integration.format_for_llm_prompt(scale_analysis_result)
                
                print("LLM提示内容:")
                print("-" * 40)
                print(llm_prompt)
                print("-" * 40)
                
                # 5. 统计分析
                print(f"\n【5. 统计分析】")
                all_stock_limits = scale_analysis_result["all_stock_limits"]
                liquidity_bottlenecks = [s for s in all_stock_limits if s['bottleneck'] == 'liquidity']
                regulatory_bottlenecks = [s for s in all_stock_limits if s['bottleneck'] == 'regulatory']
                
                print(f"总分析股票: {len(all_stock_limits)}只")
                print(f"流动性瓶颈: {len(liquidity_bottlenecks)}只")
                print(f"监管瓶颈: {len(regulatory_bottlenecks)}只")
                
                # 按规模上限排序
                sorted_stocks = sorted(all_stock_limits, key=lambda x: x['N_max'])
                print(f"\n规模上限最小的3只股票:")
                for i, stock in enumerate(sorted_stocks[:3], 1):
                    print(f"  {i}. {stock['stock_name']} ({stock['symbol']}): {stock['N_max']:.1f}亿元")
                
            else:
                print(f"❌ 分析失败: {scale_analysis_result.get('error', '未知错误')}")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()


def get_etf_portfolio_data(fund_code):
    """获取ETF持仓数据的辅助函数"""
    from sqlalchemy import create_engine, text
    from dotenv import load_dotenv
    
    # 加载环境变量
    load_dotenv()
    
    # 数据库连接
    DB_USER = os.getenv("DB_USER")
    DB_PASSWORD = os.getenv("DB_PASSWORD")
    DB_HOST = os.getenv("DB_HOST", "localhost")
    DB_PORT = os.getenv("DB_PORT", "5432")
    DB_NAME = os.getenv("DB_NAME")
    
    DATABASE_URL = f"postgresql+psycopg2://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
    engine = create_engine(DATABASE_URL)
    
    try:
        with engine.connect() as conn:
            sql = text("""
                SELECT 
                    tfp.symbol,
                    COALESCE(tsb.name, tfp.symbol) AS stock_name,
                    tfp.stk_mkv_ratio,
                    sdb.circ_mv,
                    sdb.total_mv,
                    sdb.turnover_rate,
                    sdb.pe_ttm,
                    sdb.pb,
                    tfp.end_date
                FROM tushare_fund_portfolio tfp
                LEFT JOIN tushare_stock_basic tsb ON tfp.symbol = tsb.ts_code
                LEFT JOIN tushare_stock_dailybasic sdb ON tfp.symbol = sdb.ts_code 
                    AND sdb.trade_date = (
                        SELECT MAX(sdb2.trade_date) 
                        FROM tushare_stock_dailybasic sdb2 
                        WHERE sdb2.ts_code = tfp.symbol 
                        AND sdb2.trade_date <= tfp.end_date
                    )
                WHERE tfp.ts_code = :fund_code
                AND tfp.end_date = (
                    SELECT MAX(end_date) 
                    FROM tushare_fund_portfolio 
                    WHERE ts_code = :fund_code
                )
                ORDER BY tfp.stk_mkv_ratio DESC
                LIMIT 15
            """)
            
            result = conn.execute(sql, {"fund_code": fund_code})
            portfolio_data = result.fetchall()
            
            # 转换为字典格式
            processed_data = []
            for row in portfolio_data:
                processed_data.append({
                    'symbol': row[0],
                    'stock_name': row[1],
                    'stk_mkv_ratio': float(row[2]) if row[2] else 0,
                    'circ_mv': float(row[3]) if row[3] else 0,
                    'total_mv': float(row[4]) if row[4] else 0,
                    'turnover_rate': float(row[5]) if row[5] else 0,
                    'pe_ttm': float(row[6]) if row[6] else 0,
                    'pb': float(row[7]) if row[7] else 0,
                    'end_date': row[8]
                })
            
            return processed_data
            
    except Exception as e:
        print(f"获取ETF持仓数据失败: {e}")
        return None


if __name__ == "__main__":
    test_integrated_scale_analysis()
