# 深度分析专精升级说明

## 概述
本次升级将前置rules从通用分析模式升级为深度分析专精模式，并集成了A股和港股的申万1,2,3级行业分类信息以及PE、PB估值数据，以提供更精准的基金分析能力。

## 主要变更

### 1. 前置Rules升级
- **文件变更**: `前置rules.md` → `深度分析专精.md`
- **分析模式**: 通用分析 → 聚焦型策略基金深度分析专精
- **版本**: V2.5-FocusRefined
- **核心特点**:
  - 专注于聚焦型策略基金（行业、赛道、主题/概念基金）
  - 强化"核心聚焦领域/叙事"识别与定义
  - 集成申万行业分类和概念标签分析
  - 提供多维度概括性标签提炼

### 2. 申万行业分类集成

#### 数据库表结构确认
通过PostgreSQL数据库查询确认了以下申万行业相关表：
- `sw_industry_members_monthly`: 申万行业成员月度数据
- `sw_l1_daily`: 申万一级行业日度数据
- `sw_l2_daily`: 申万二级行业日度数据

#### 申万一级行业分类（共30个）
```
801010.SI - 农林牧渔    801030.SI - 基础化工    801040.SI - 钢铁
801050.SI - 有色金属    801080.SI - 电子        801110.SI - 家用电器
801120.SI - 食品饮料    801130.SI - 纺织服饰    801140.SI - 轻工制造
801150.SI - 医药生物    801160.SI - 公用事业    801170.SI - 交通运输
801180.SI - 房地产      801200.SI - 商贸零售    801210.SI - 社会服务
801230.SI - 综合        801710.SI - 建筑材料    801720.SI - 建筑装饰
801730.SI - 电力设备    801740.SI - 国防军工    801750.SI - 计算机
801760.SI - 传媒        801770.SI - 通信        801780.SI - 银行
801790.SI - 非银金融    801880.SI - 汽车        801890.SI - 机械设备
801950.SI - 煤炭        801960.SI - 石油石化    801970.SI - 环保
801980.SI - 美容护理
```

### 3. 代码功能增强

#### 新增函数 (src/data_fetcher.py)
1. **fetch_sw_industry_mapping()**: 获取完整A股申万行业分类映射
2. **get_fund_sw_industry_info()**: 获取基金持仓A股的申万行业分类信息
3. **get_fund_hk_sw_industry_info()**: 获取基金持仓港股的申万行业分类信息
4. **get_fund_valuation_summary()**: 获取基金持仓股票的估值统计摘要（PE、PB等）
5. **_get_quarter_end_date()**: 计算季度末日期的辅助函数

#### 修改函数 (src/llm_analyzer.py)
1. **_build_llm_prompt()**: 添加申万行业分类信息和估值统计信息参数
2. **analyze_with_llm()**: 集成申万行业分类信息和估值数据到LLM分析

#### 修改函数 (main.py)
1. **load_analysis_rules()**: 默认加载深度分析专精规则
2. **run_analysis()**: 集成A股和港股申万行业分类信息以及估值统计信息获取和传递
3. **run_batch_analysis()**: 批量分析中集成A股和港股申万行业分类信息以及估值统计信息

### 4. 配置文件新增
- **sw_industry_mapping.json**: 申万行业分类映射配置文件
  - 包含30个一级行业映射
  - 行业分析框架（成长、价值、周期、防御）
  - 概念标签分类（AI、新消费、新能源等）

### 5. 数据字典更新
更新了 `项目总结/tusharedb_data_dictionary.md`，新增：
- hk_stock_sw_quarterly表结构（港股申万行业分类）
- 确认了tushare_index_swmember表结构（A股申万行业分类）
- 确认了tushare_stock_dailybasic表结构（A股估值数据）

## 技术实现细节

### 申万行业数据获取流程
#### A股申万行业分类
1. 从`tushare_index_swmember`表获取A股的1,2,3级行业分类
2. 使用`is_new = 'Y'`条件获取最新的行业分类
3. 与基金持仓数据关联，提供详细的行业分布信息

#### 港股申万行业分类
1. 从`hk_stock_sw_quarterly`表获取港股的1,2,3级行业分类
2. 使用季度末日期匹配，获取最接近报告期的行业分类数据
3. 通过`_get_quarter_end_date()`函数计算对应的季度末日期

#### 数据合并和格式化
1. 分别获取A股和港股的申万行业分类信息
2. 合并为统一的文本格式，区分A股和港股部分
3. 格式化为LLM可理解的文本格式

### PE、PB估值数据获取流程
1. 从`tushare_stock_dailybasic`表获取最新交易日的估值数据
2. 包含PE（市盈率）、PE_TTM（滚动市盈率）、PB（市净率）
3. 计算基金持仓的估值统计摘要（平均值、中位数、覆盖率）
4. 在持仓明细中显示每只股票的具体估值水平

### LLM Prompt增强
原有Prompt结构：
```
【分析规则】+ 【基金季报信息】+ 【前十大持仓数据】
```

升级后Prompt结构：
```
【分析规则】+ 【基金季报信息】+ 【前十大持仓数据】+ 【申万行业分类信息】+ 【估值统计信息】
```

### 深度分析专精特点
1. **核心聚焦领域识别**: 精准识别基金的投资聚焦点
2. **多层级行业分析**: 利用申万1,2,3级行业进行精细化分析
3. **概念标签提炼**: 从四个维度提炼概括性标签
4. **风格倾向分析**: 深入分析聚焦领域内的投资风格

## 使用说明

### 环境要求
- PostgreSQL数据库连接
- 包含申万行业分类数据的数据库表
- 深度分析专精.md规则文件

### 运行方式
```bash
python main.py  # 默认执行批量分析，使用深度分析专精规则
```

### 输出增强
分析报告将包含：
- 详细的申万行业分类信息
- 基于行业分类的投资逻辑分析
- 聚焦领域的精准定义
- 多维度概括性标签
- 持仓股票的估值水平分析
- 基金整体估值统计摘要
- 基于估值的投资价值和风险评估

## 注意事项
1. 确保数据库连接到正确的tusharedb数据库
2. 确保数据库中存在以下表：
   - `tushare_index_swmember`（A股申万行业分类）
   - `hk_stock_sw_quarterly`（港股申万行业分类）
   - `tushare_stock_dailybasic`（A股估值数据）
   - `tushare_fund_portfolio`（基金持仓数据）
3. 深度分析专精.md文件需要在项目根目录
4. A股支持申万行业分类和估值数据，港股仅支持申万行业分类
5. 港股申万行业分类数据按季度更新，A股数据实时更新
6. 建议定期更新申万行业分类数据和估值数据以保持准确性
7. 估值数据基于最新交易日，可能存在时间差

## 后续优化方向
1. 支持港股的估值数据（PE、PB等）
2. 增加更多概念标签的自动识别
3. 优化行业轮动分析功能
4. 集成行业估值数据和相对估值分析
5. 添加估值历史分位数分析
6. 支持更多估值指标（如PEG、EV/EBITDA等）
7. 优化港股申万行业分类数据的时效性
8. 支持美股等其他市场的行业分类
