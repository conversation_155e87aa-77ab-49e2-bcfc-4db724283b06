# Wind API 字段代码参考

## 概述

本文档列出了项目中使用的Wind API字段代码，并提供了验证和替代方案。

## 当前使用的字段代码

### ✅ 估值数据字段（已验证）

| 字段代码 | 中文名称 | 数据类型 | 说明 |
|---------|---------|---------|------|
| `S_VAL_PETTM` | 市盈率TTM | 数值 | 滚动12个月市盈率 |
| `S_VAL_PBLF` | 市净率LF | 数值 | 最新财报市净率 |
| `S_VAL_MV` | 总市值 | 数值 | 总市值（万元） |

### ⚠️ 财务数据字段（需验证）

| 字段代码 | 中文名称 | 数据类型 | 状态 |
|---------|---------|---------|------|
| `S_FA_ROE` | 净资产收益率 | 数值 | ✅ 常用字段 |
| `S_FA_ROIC` | 投入资本回报率 | 数值 | ⚠️ 需验证 |
| `S_FA_GROSSMARGIN` | 毛利率 | 数值 | ✅ 已修正 |
| `S_FA_NETPROFITMARGIN` | 净利率 | 数值 | ⚠️ 需验证 |

### ✅ 实时数据字段（基本正确）

| 字段代码 | 中文名称 | 数据类型 | 说明 |
|---------|---------|---------|------|
| `RT_LAST` | 最新价 | 数值 | 实时最新成交价 |
| `RT_CHG_PCT` | 涨跌幅% | 数值 | 涨跌幅百分比 |
| `RT_VOLUME` | 成交量 | 数值 | 实时成交量 |
| `RT_MKT_CAP` | 实时市值 | 数值 | 实时总市值 |

## 常见的Wind字段代码替代方案

### 估值指标
```
# 市盈率相关
S_VAL_PETTM     # 市盈率TTM（推荐）
S_VAL_PE        # 静态市盈率
S_VAL_PETTM_NEW # 新版市盈率TTM

# 市净率相关  
S_VAL_PBLF      # 市净率LF（推荐）
S_VAL_PB        # 市净率

# 市值相关
S_VAL_MV        # 总市值（推荐）
S_DQ_MV         # 流通市值
```

### 财务指标
```
# 盈利能力
S_FA_ROE        # 净资产收益率（推荐）
S_FA_ROIC       # 投入资本回报率
S_FA_ROA        # 总资产收益率

# 利润率
S_FA_GROSSMARGIN      # 毛利率（推荐）
S_FA_NETPROFITMARGIN  # 净利率
S_FA_OPERATEMARGIN    # 营业利润率
```

## 验证建议

### 1. 测试代码
```python
import WindPy as w

# 测试字段代码是否有效
def test_wind_fields():
    w.start()
    
    # 测试估值字段
    error_code, data = w.wss("000001.SZ", "S_VAL_PETTM,S_VAL_PBLF,S_VAL_MV")
    if error_code == 0:
        print("估值字段测试成功")
    else:
        print(f"估值字段测试失败，错误代码: {error_code}")
    
    # 测试财务字段
    error_code, data = w.wss("000001.SZ", "S_FA_ROE,S_FA_GROSSMARGIN")
    if error_code == 0:
        print("财务字段测试成功")
    else:
        print(f"财务字段测试失败，错误代码: {error_code}")
```

### 2. 字段查询方法
```python
# 查询可用字段
error_code, data = w.wset("sectorconstituent", "date=20231201;sectorid=a001010100000000")
```

## 推荐的验证步骤

1. **安装WindPy环境**
   ```bash
   pip install WindPy
   ```

2. **登录Wind终端**
   - 确保Wind终端已启动并登录

3. **运行测试脚本**
   ```python
   # 测试当前配置的字段代码
   python -c "
   import WindPy as w
   w.start()
   
   # 测试港股
   error_code, data = w.wss('00700.HK', 'S_VAL_PETTM,S_VAL_PBLF,S_VAL_MV')
   print(f'港股测试结果: {error_code}')
   
   # 测试A股
   error_code, data = w.wss('000001.SZ', 'S_VAL_PETTM,S_VAL_PBLF,S_VAL_MV')
   print(f'A股测试结果: {error_code}')
   "
   ```

4. **检查返回结果**
   - 错误代码为0表示成功
   - 非0错误代码需要检查字段代码或权限

## 注意事项

1. **字段代码区分大小写**
2. **不同市场可能有不同的字段支持**
3. **部分字段需要特定的数据权限**
4. **字段代码可能随Wind版本更新而变化**

## 如果字段代码有误

### 修正步骤
1. 在Wind终端中查询正确的字段代码
2. 更新 `external_api_config.json` 中的字段配置
3. 重新测试API调用
4. 更新字段描述和文档

### 联系方式
如发现字段代码问题，请：
1. 记录错误的字段代码和错误信息
2. 在Wind终端中验证正确的字段代码
3. 更新配置文件并测试
